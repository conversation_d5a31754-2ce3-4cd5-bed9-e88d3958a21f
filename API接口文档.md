# 神奇农场管理后台API接口文档

## 基础配置

### 接口基础信息
- **基础URL**: `https://api.zj7hui.com/pasture`
- **认证方式**: Header认证
- **认证字段**: `Accesstoken`
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

## 核心接口

### 1. 实时报告接口

#### 获取维度列表
- **接口路径**: `/admin/realTimeReports/dimensionList`
- **请求方法**: GET
- **请求参数**:
  - `appCode` (string): 应用代码
    - `pasture`: 神奇农场
    - `pasture_zy`: 神奇农场-掌育
    - `pasture_xm`: 神奇农场-小米

**请求示例**:
```
GET /admin/realTimeReports/dimensionList?appCode=pasture
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 26,
      "name": "今日订单数",
      "total": 1,
      "unitType": 3,
      "range": 100,
      "rangeType": 1,
      "weekRange": 75.00,
      "weekRangeType": 2
    },
    {
      "id": 27,
      "name": "今日付款金额（元）",
      "total": 19,
      "unitType": 3,
      "range": 50,
      "rangeType": 1,
      "weekRange": 25.00,
      "weekRangeType": 2
    },
    {
      "id": 28,
      "name": "今日新增用户数",
      "total": 1008,
      "unitType": 3,
      "range": 200,
      "rangeType": 1,
      "weekRange": 150.00,
      "weekRangeType": 1
    }
  ]
}
```

**字段说明**:
- `id`: 维度ID
- `name`: 维度名称
- `total`: 总数值
- `unitType`: 单位类型 (1=秒, 2=个, 3=元/金额)
- `range`: 较昨日同期变化百分比
- `rangeType`: 变化趋势 (1=上涨, 2=下跌)
- `weekRange`: 较上周同期变化百分比
- `weekRangeType`: 周变化趋势 (1=上涨, 2=下跌)

### 2. 看板数据接口

#### 获取维度详情
- **接口路径**: `/admin/spectaculars/dimensionDetail`
- **请求方法**: GET
- **请求参数**:
  - `id` (number): 维度ID
  - `startTime` (string): 开始时间 (格式: YYYY-MM-DD)
  - `endTime` (string): 结束时间 (格式: YYYY-MM-DD)
  - `type` (number): 统计类型 (固定为1)

**请求示例**:
```
GET /admin/spectaculars/dimensionDetail?id=189&startTime=2025-07-01&endTime=2025-07-31&type=1
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "chartData": [
      {
        "date": "2025-07-01",
        "value": 1000
      },
      {
        "date": "2025-07-02",
        "value": 1050
      }
    ],
    "summary": {
      "total": 31500,
      "average": 1016.13,
      "growth": 5.2
    }
  }
}
```

### 3. 维度ID映射表

| 维度名称 | 维度ID | 说明 |
|---------|--------|------|
| 用户总数 | 189 | 累计注册用户总数 |
| 新增用户数 | 190 | 当日新注册用户数 |
| 活跃用户数 | 191 | 当日活跃用户数 |
| 付费金额 | 192 | 当日付费总金额 |
| 付费人数 | 193 | 当日付费用户数 |
| 入账金额 | 194 | 当日入账总金额 |
| 入账笔数 | 195 | 当日入账交易笔数 |
| 出账金额 | 196 | 当日出账总金额 |
| 出账笔数 | 197 | 当日出账交易笔数 |
| 次日留存率 | 198 | 用户次日留存率 |

## 认证接口

### 用户登录
- **接口路径**: `/admin/auth/login`
- **请求方法**: POST
- **请求参数**:
```json
{
  "username": "admin",
  "password": "password"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "id": 1,
      "username": "admin",
      "role": "admin"
    }
  }
}
```

## 错误码说明

| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 200 | 成功 | 正常处理 |
| 400 | 请求参数错误 | 检查请求参数 |
| 401 | 未授权 | 重新登录 |
| 403 | 权限不足 | 联系管理员 |
| 404 | 接口不存在 | 检查接口路径 |
| 500 | 服务器内部错误 | 稍后重试 |

## 接口调用示例

### JavaScript/TypeScript
```typescript
// 设置请求头
const headers = {
  'Content-Type': 'application/json',
  'Accesstoken': 'your-token-here'
}

// 获取实时数据
const getRealTimeData = async (appCode: string) => {
  const response = await fetch(
    `https://api.zj7hui.com/pasture/admin/realTimeReports/dimensionList?appCode=${appCode}`,
    { headers }
  )
  return response.json()
}

// 获取看板数据
const getDashboardData = async (params: {
  id: number
  startTime: string
  endTime: string
  type: number
}) => {
  const queryString = new URLSearchParams(params).toString()
  const response = await fetch(
    `https://api.zj7hui.com/pasture/admin/spectaculars/dimensionDetail?${queryString}`,
    { headers }
  )
  return response.json()
}
```

## 注意事项

1. **认证Token**: 所有接口都需要在请求头中携带有效的`Accesstoken`
2. **时间格式**: 时间参数统一使用`YYYY-MM-DD`格式
3. **应用代码**: 实时报告接口的`appCode`参数区分大小写
4. **数据缓存**: 实时数据建议5分钟刷新一次，避免频繁请求
5. **错误处理**: 建议对所有API调用进行错误处理和重试机制
6. **数据格式**: 数值类型的数据可能包含小数，前端需要根据业务需求进行格式化

## 更新日志

### 2025-08-02
- 实时报告接口从`/admin/spectaculars/dimensionDetail`更改为`/admin/realTimeReports/dimensionList`
- 看板数据接口参数简化，移除`appCode`、`statisticsType`、`dimensionType`参数
- 新增三个应用渠道的支持：`pasture`、`pasture_zy`、`pasture_xm`
- 优化数据格式化规则，整数类型不显示小数点
