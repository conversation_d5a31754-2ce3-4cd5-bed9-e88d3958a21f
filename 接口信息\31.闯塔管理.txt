挑战塔（手动）

**接口地址** `/pasture/player/tower/api/boutTower`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| param         |      param   |     body        |       true      | 塔挑战参数   | 塔挑战参数     |
            



**schema属性说明**
  
**塔挑战参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId  | 玩家ID |   body    |   false   |int64  |       |
| watchFirst  | 是否观战方先手 |   body    |   false   |boolean  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«战斗»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    战斗   |   战斗    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**战斗**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| awardMenuList         |     掉落物品      |  array   | 掉落物品明细     |
            | boutNum         |     最终回合数      |  int32   |      |
            | combatResult         |     战斗结果 1观战方胜利 2对战方胜利 3进行中      |  int32   |      |
            | dbzResult         |     武道会结果      |  武道会结果   | 武道会结果     |
            | drops         |     抽奖掉落物品（是真的掉落）      |  array   | 掉落物品明细     |
            | exp         |     掉落经验      |  int32   |      |
            | link         |     link      |  string   |      |
            | playerInfo         |     玩家缩略信息      |  玩家缩略信息   | 玩家缩略信息     |
            | rankResult         |     排位结果      |  排位结果   | 排位结果     |
            | sessions         |     战斗场次      |  array   | 战斗场次     |
            | showDrops         |     抽奖未掉落物品（不是真的掉落）      |  array   | 掉落物品明细     |
            | stars         |     战斗结算      |  array   | 关卡目标     |
            | suggest         |     suggest建议      |  string   |      |
            

**掉落物品明细**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| code         |     图标      |  string   |      |
            | name         |     名称      |  string   |      |
            | sourceId         |     sourceId道具id      |  int64   |      |
            | sourceType         |     sourceType类型 1背包方案 2背包道具 3主角经验 4能量币 5体力 6矿石 7宠物 8装备 9排位积分 10 金币      |  int32   |      |
            | type         |     类型 1普通掉落 2首次掉落      |  int32   |      |
            | upgrade         |     人物升级信息      |  升级后玩家信息   | 升级后玩家信息     |
            | value         |     num数量      |  int32   |      |
            

**升级后玩家信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coinType         |     收获代币类型： 1-金币，2-能量      |  int32   |      |
            | coinVal         |     收获代币值      |  int32   |      |
            | drops         |     物品掉落      |  array   | 掉落物品明细     |
            | level         |     人物等级      |  int32   |      |
            | player         |     玩家      |  玩家详情   | 玩家详情     |
            | score         |     评分      |  int32   |      |
            

**玩家详情**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | breed         |     动物养殖      |  动物养殖   | 动物养殖     |
            | coin         |     金币      |  int32   |      |
            | energy         |     能量      |  int32   |      |
            | equipageScore         |     装备评分      |  int32   |      |
            | equipages         |     装备      |  array   | 玩家装备     |
            | events         |     互动事件      |  array   | 互动事件     |
            | expValue         |     expValue当前经验值      |  int64   |      |
            | followWx         |     是否关注微信公众号 1-已关注 0-未关注      |  int32   |      |
            | icon         |     玩家头像图标地址      |  string   |      |
            | interacts         |     互动      |  array   | 互动事件     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | level         |     人物等级      |  int32   |      |
            | levelName         |     官职      |  string   |      |
            | model         |     角色模型      |  string   |      |
            | nextExpValue         |     nextExpValue下一个等级所需经验值      |  int64   |      |
            | nickname         |     玩家昵称      |  string   |      |
            | ore         |     粮草      |  int32   |      |
            | playerId         |     玩家id      |  int64   |      |
            | protectStatus         |     保护状态 1有保护 2无保护      |  int32   |      |
            | rankScore         |     排位积分      |  int32   |      |
            | score         |     总评分      |  int32   |      |
            | sex         |     玩家性别 1-男 2-女      |  int32   |      |
            | status         |     玩家状态 0-不可用 1-正常      |  int32   |      |
            | userId         |     用户id      |  string   |      |
            | vim         |     活力      |  int32   |      |
            | vimLimit         |     活力上限      |  int32   |      |
            | vipOre         |     vip粮草      |  int32   |      |
            | vipStatus         |     vip状态      |  int32   |      |
            | wish         |     许愿值      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**动物养殖**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| animalId         |     animalId动物id      |  int64   |      |
            | breedTime         |     breedTime养殖时间      |  date-time   |      |
            | createTime         |     createTime      |  date-time   |      |
            | downTime         |     倒计时      |  int64   |      |
            | modelSpine         |     动效      |  string   |      |
            | petId         |     petId宠物id      |  int64   |      |
            | pickStatus         |     是否可以采摘 1可以 2自己不能偷 3好友不能偷      |  int32   |      |
            | playerBreedId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态1 成长中 2待收获 3已收获      |  int32   |      |
            | suspendTime         |     暂停时间 不为空就是被暂停      |  date-time   |      |
            

**玩家装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| id         |     id      |  int64   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | type         |     类型 1神兵 2宝马 3皮肤 4兵书 5虎符      |  int32   |      |
            

**互动事件**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coin         |     金币      |  int32   |      |
            | createTime         |     createTime      |  date-time   |      |
            | detail         |     详情      |  string   |      |
            | eventType         |     操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽  8便便 9蚊子 10老鼠      |  int32   |      |
            | eventVal         |     eventVal事件值      |  int32   |      |
            | finishVal         |     eventVal事件完成值      |  int32   |      |
            | icon         |     图片      |  string   |      |
            | playerEventId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            

**武道会结果**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| levelName         |     当前level      |  string   |      |
            | remind         |     提示      |  string   |      |
            | status         |     状态 1不进级 2进级      |  int32   |      |
            

**玩家缩略信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| expValue         |     当前经验值      |  int64   |      |
            | icon         |     玩家头像图标地址      |  string   |      |
            | level         |     人物等级      |  int32   |      |
            | model         |     角色模型      |  string   |      |
            | nextExpValue         |     下一个等级所需经验值      |  int64   |      |
            | nickname         |     玩家昵称      |  string   |      |
            | playerId         |     玩家id      |  int64   |      |
            

**排位结果**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| beforeRank         |     变化前段位      |  段位2   | 段位2     |
            | rank         |     变化后段位      |  段位2   | 段位2     |
            | status         |     状态 1升星 2降星 3升段 4降段      |  int32   |      |
            

**段位2**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| currentStar         |     本段位星星数      |  int32   |      |
            | icon         |     段位图标      |  string   |      |
            | needStar         |     升段所需星星数      |  int32   |      |
            | rank         |     段位      |  int32   |      |
            | rankLevel         |     段位等级      |  string   |      |
            | rankName         |     段位名称      |  string   |      |
            | seasonLevel         |     赛季      |  int32   |      |
            

**战斗场次**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| bouts         |     战斗回合      |  array   | 战斗回合     |
            | fightRole         |     对战方角色      |  战斗角色   | 战斗角色     |
            | sessionNum         |     场次      |  int32   |      |
            | sessionResult         |     本场战斗结果      |  boolean   |      |
            | thumbnails         |     战斗角色缩略图      |  array   | 战斗角色缩略图     |
            | watchFirst         |     观战方是否先手 true 观战方先手      |  boolean   |      |
            | watchRole         |     观战方角色      |  战斗角色   | 战斗角色     |
            

**战斗回合**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| boutLimit         |     回合上限      |  int32   |      |
            | boutNum         |     当前回合数      |  int32   |      |
            | fight         |     对战方      |  战斗回合进攻   | 战斗回合进攻     |
            | watch         |     观战方      |  战斗回合进攻   | 战斗回合进攻     |
            

**战斗回合进攻**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| bloodLimit         |     防御方血量上限      |  int32   |      |
            | endBlood         |     防御方回合结束血量      |  int32   |      |
            | harms         |     观战方输出      |  array   | 战斗伤害     |
            | isRestrain         |     是否命中元素克制  1不是 2是      |  int32   |      |
            | isStrike         |     是否命中暴击 1不是 2是      |  int32   |      |
            | skills         |     技能列表      |  array   | 战斗技能     |
            | startBlood         |     防御方回合开始血量      |  int32   |      |
            

**战斗伤害**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| harm         |     伤害数值      |  int32   |      |
            | type         |     伤害类型 1普通伤害 2草伤害 3火伤害 4水伤害 5光伤害 6暗伤害      |  int32   |      |
            

**战斗技能**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| cooling         |     剩余冷却回合      |  int32   |      |
            | effectBout         |     effectBout释放回合      |  int32   |      |
            | fullLevel         |     fullLevel满级      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | id         |     id      |  int64   |      |
            | intervalBout         |     intervalBout间隔回合      |  int32   |      |
            | intervalLevel         |     间隔等级      |  int32   |      |
            | level         |     当前等级      |  int32   |      |
            | magic         |     magic动效      |  string   |      |
            | name         |     name名称      |  string   |      |
            | reachLevel         |     level拥有等级      |  int32   |      |
            | status         |     状态 1未使用 2当前回合使用 3冷却中      |  int32   |      |
            

**战斗角色**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| atk         |     攻击力      |  int32   |      |
            | attrType         |     元素标识      |  int32   |      |
            | blood         |     当前血量      |  int32   |      |
            | bloodLimit         |     生命上限      |  int32   |      |
            | defense         |     防御力      |  int32   |      |
            | endPlotId         |     endPlotId结束剧情id      |  int64   |      |
            | equipages         |     装备      |  array   | 玩家装备     |
            | evolutionType         |     等阶类型（冗余）      |  int32   |      |
            | icon         |     图标      |  string   |      |
            | level         |     等级      |  int32   |      |
            | model         |     模型      |  string   |      |
            | modelTurn         |     模型翻转类型 0 不翻转 1翻转      |  int32   |      |
            | name         |     名称      |  string   |      |
            | pets         |     名将      |  array   | 战斗名将     |
            | roleType         |     角色类型 1玩家 2NPC      |  int32   |      |
            | score         |     评分      |  int32   |      |
            | startPlotId         |     startPlotId开始剧情id      |  int64   |      |
            

**战斗名将**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| evolutionType         |     evolutionType等阶类型（冗余）      |  int32   |      |
            | icon         |     图标      |  string   |      |
            | model         |     模型      |  string   |      |
            | name         |     名称      |  string   |      |
            | seq         |     排序      |  int32   |      |
            | species         |     种类 1陆地 2飞行      |  int32   |      |
            

**战斗角色缩略图**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| icon         |     图标      |  string   |      |
            | level         |     等级      |  int32   |      |
            | name         |     名称      |  string   |      |
            | status         |     状态 1战斗中 2未开始 3已战胜      |  int32   |      |
            

**关卡目标**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| reach         |     reach达标值      |  int32   |      |
            | reachContent         |     reachContent达标内容      |  string   |      |
            | reachType         |     reachType达标类型 1战斗回合数      |  int32   |      |
            | sectionStarId         |     关卡目标id      |  int64   |      |
            | sourceId         |     sourceId      |  int64   |      |
            | sourceType         |     sourceType来源类型 1关卡      |  int32   |      |
            | status         |     状态 1未达标 2已达标      |  int32   |      |
            | type         |     type类型 1级 2级 3级      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "awardMenuList": [
            {
                "code": "",
                "name": "",
                "sourceId": 0,
                "sourceType": 0,
                "type": 0,
                "upgrade": {
                    "coinType": 0,
                    "coinVal": 0,
                    "drops": [
                        {}
                    ],
                    "level": 0,
                    "player": {
                        "attrs": [
                            {
                                "attrType": 0,
                                "attrValue": 0,
                                "type": 0
                            }
                        ],
                        "breed": {
                            "animalId": 0,
                            "breedTime": "",
                            "createTime": "",
                            "downTime": 0,
                            "modelSpine": "",
                            "petId": 0,
                            "pickStatus": 0,
                            "playerBreedId": 0,
                            "playerId": 0,
                            "seq": 0,
                            "status": 0,
                            "suspendTime": ""
                        },
                        "coin": 0,
                        "energy": 0,
                        "equipageScore": 0,
                        "equipages": [
                            {
                                "id": 0,
                                "model": "",
                                "modelSpine": "",
                                "name": "",
                                "type": 0
                            }
                        ],
                        "events": [
                            {
                                "coin": 0,
                                "createTime": "",
                                "detail": "",
                                "eventType": 0,
                                "eventVal": 0,
                                "finishVal": 0,
                                "icon": "",
                                "playerEventId": 0,
                                "playerId": 0
                            }
                        ],
                        "expValue": 0,
                        "followWx": 0,
                        "icon": "",
                        "interacts": [
                            {
                                "coin": 0,
                                "createTime": "",
                                "detail": "",
                                "eventType": 0,
                                "eventVal": 0,
                                "finishVal": 0,
                                "icon": "",
                                "playerEventId": 0,
                                "playerId": 0
                            }
                        ],
                        "isVip": 0,
                        "level": 0,
                        "levelName": "",
                        "model": "",
                        "nextExpValue": 0,
                        "nickname": "",
                        "ore": 0,
                        "playerId": 0,
                        "protectStatus": 0,
                        "rankScore": 0,
                        "score": 0,
                        "sex": 0,
                        "status": 0,
                        "userId": "",
                        "vim": 0,
                        "vimLimit": 0,
                        "vipOre": 0,
                        "vipStatus": 0,
                        "wish": 0
                    },
                    "score": 0
                },
                "value": 0
            }
        ],
        "boutNum": 0,
        "combatResult": 0,
        "dbzResult": {
            "levelName": "",
            "remind": "",
            "status": 0
        },
        "drops": [
            {}
        ],
        "exp": 0,
        "link": "",
        "playerInfo": {
            "expValue": 0,
            "icon": "",
            "level": 0,
            "model": "",
            "nextExpValue": 0,
            "nickname": "",
            "playerId": 0
        },
        "rankResult": {
            "beforeRank": {
                "currentStar": 0,
                "icon": "",
                "needStar": 0,
                "rank": 0,
                "rankLevel": "",
                "rankName": "",
                "seasonLevel": 0
            },
            "rank": {
                "currentStar": 0,
                "icon": "",
                "needStar": 0,
                "rank": 0,
                "rankLevel": "",
                "rankName": "",
                "seasonLevel": 0
            },
            "status": 0
        },
        "sessions": [
            {
                "bouts": [
                    {
                        "boutLimit": 0,
                        "boutNum": 0,
                        "fight": {
                            "bloodLimit": 0,
                            "endBlood": 0,
                            "harms": [
                                {
                                    "harm": 0,
                                    "type": 0
                                }
                            ],
                            "isRestrain": 0,
                            "isStrike": 0,
                            "skills": [
                                {
                                    "cooling": 0,
                                    "effectBout": 0,
                                    "fullLevel": 0,
                                    "icon": "",
                                    "id": 0,
                                    "intervalBout": 0,
                                    "intervalLevel": 0,
                                    "level": 0,
                                    "magic": "",
                                    "name": "",
                                    "reachLevel": 0,
                                    "status": 0
                                }
                            ],
                            "startBlood": 0
                        },
                        "watch": {}
                    }
                ],
                "fightRole": {
                    "atk": 0,
                    "attrType": 0,
                    "blood": 0,
                    "bloodLimit": 0,
                    "defense": 0,
                    "endPlotId": 0,
                    "equipages": [
                        {
                            "id": 0,
                            "model": "",
                            "modelSpine": "",
                            "name": "",
                            "type": 0
                        }
                    ],
                    "evolutionType": 0,
                    "icon": "",
                    "level": 0,
                    "model": "",
                    "modelTurn": 0,
                    "name": "",
                    "pets": [
                        {
                            "evolutionType": 0,
                            "icon": "",
                            "model": "",
                            "name": "",
                            "seq": 0,
                            "species": 0
                        }
                    ],
                    "roleType": 0,
                    "score": 0,
                    "startPlotId": 0
                },
                "sessionNum": 0,
                "sessionResult": true,
                "thumbnails": [
                    {
                        "icon": "",
                        "level": 0,
                        "name": "",
                        "status": 0
                    }
                ],
                "watchFirst": true,
                "watchRole": {}
            }
        ],
        "showDrops": [
            {}
        ],
        "stars": [
            {
                "reach": 0,
                "reachContent": "",
                "reachType": 0,
                "sectionStarId": 0,
                "sourceId": 0,
                "sourceType": 0,
                "status": 0,
                "type": 0
            }
        ],
        "suggest": ""
    },
    "message": "",
    "msg": ""
}
```



-----------分割线
每日领奖(type 1:领奖 2：3倍领奖)

**接口地址** `/pasture/player/tower/api/dayAword`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            | type         |      type   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«掉落物品明细»»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   掉落物品明细    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**掉落物品明细**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| code         |     图标      |  string   |      |
            | name         |     名称      |  string   |      |
            | sourceId         |     sourceId道具id      |  int64   |      |
            | sourceType         |     sourceType类型 1背包方案 2背包道具 3主角经验 4能量币 5体力 6矿石 7宠物 8装备 9排位积分 10 金币      |  int32   |      |
            | type         |     类型 1普通掉落 2首次掉落      |  int32   |      |
            | upgrade         |     人物升级信息      |  升级后玩家信息   | 升级后玩家信息     |
            | value         |     num数量      |  int32   |      |
            

**升级后玩家信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coinType         |     收获代币类型： 1-金币，2-能量      |  int32   |      |
            | coinVal         |     收获代币值      |  int32   |      |
            | drops         |     物品掉落      |  array   | 掉落物品明细     |
            | level         |     人物等级      |  int32   |      |
            | player         |     玩家      |  玩家详情   | 玩家详情     |
            | score         |     评分      |  int32   |      |
            

**玩家详情**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | breed         |     动物养殖      |  动物养殖   | 动物养殖     |
            | coin         |     金币      |  int32   |      |
            | energy         |     能量      |  int32   |      |
            | equipageScore         |     装备评分      |  int32   |      |
            | equipages         |     装备      |  array   | 玩家装备     |
            | events         |     互动事件      |  array   | 互动事件     |
            | expValue         |     expValue当前经验值      |  int64   |      |
            | followWx         |     是否关注微信公众号 1-已关注 0-未关注      |  int32   |      |
            | icon         |     玩家头像图标地址      |  string   |      |
            | interacts         |     互动      |  array   | 互动事件     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | level         |     人物等级      |  int32   |      |
            | levelName         |     官职      |  string   |      |
            | model         |     角色模型      |  string   |      |
            | nextExpValue         |     nextExpValue下一个等级所需经验值      |  int64   |      |
            | nickname         |     玩家昵称      |  string   |      |
            | ore         |     粮草      |  int32   |      |
            | playerId         |     玩家id      |  int64   |      |
            | protectStatus         |     保护状态 1有保护 2无保护      |  int32   |      |
            | rankScore         |     排位积分      |  int32   |      |
            | score         |     总评分      |  int32   |      |
            | sex         |     玩家性别 1-男 2-女      |  int32   |      |
            | status         |     玩家状态 0-不可用 1-正常      |  int32   |      |
            | userId         |     用户id      |  string   |      |
            | vim         |     活力      |  int32   |      |
            | vimLimit         |     活力上限      |  int32   |      |
            | vipOre         |     vip粮草      |  int32   |      |
            | vipStatus         |     vip状态      |  int32   |      |
            | wish         |     许愿值      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**动物养殖**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| animalId         |     animalId动物id      |  int64   |      |
            | breedTime         |     breedTime养殖时间      |  date-time   |      |
            | createTime         |     createTime      |  date-time   |      |
            | downTime         |     倒计时      |  int64   |      |
            | modelSpine         |     动效      |  string   |      |
            | petId         |     petId宠物id      |  int64   |      |
            | pickStatus         |     是否可以采摘 1可以 2自己不能偷 3好友不能偷      |  int32   |      |
            | playerBreedId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态1 成长中 2待收获 3已收获      |  int32   |      |
            | suspendTime         |     暂停时间 不为空就是被暂停      |  date-time   |      |
            

**玩家装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| id         |     id      |  int64   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | type         |     类型 1神兵 2宝马 3皮肤 4兵书 5虎符      |  int32   |      |
            

**互动事件**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coin         |     金币      |  int32   |      |
            | createTime         |     createTime      |  date-time   |      |
            | detail         |     详情      |  string   |      |
            | eventType         |     操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽  8便便 9蚊子 10老鼠      |  int32   |      |
            | eventVal         |     eventVal事件值      |  int32   |      |
            | finishVal         |     eventVal事件完成值      |  int32   |      |
            | icon         |     图片      |  string   |      |
            | playerEventId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "code": "",
            "name": "",
            "sourceId": 0,
            "sourceType": 0,
            "type": 0,
            "upgrade": {
                "coinType": 0,
                "coinVal": 0,
                "drops": [
                    {}
                ],
                "level": 0,
                "player": {
                    "attrs": [
                        {
                            "attrType": 0,
                            "attrValue": 0,
                            "type": 0
                        }
                    ],
                    "breed": {
                        "animalId": 0,
                        "breedTime": "",
                        "createTime": "",
                        "downTime": 0,
                        "modelSpine": "",
                        "petId": 0,
                        "pickStatus": 0,
                        "playerBreedId": 0,
                        "playerId": 0,
                        "seq": 0,
                        "status": 0,
                        "suspendTime": ""
                    },
                    "coin": 0,
                    "energy": 0,
                    "equipageScore": 0,
                    "equipages": [
                        {
                            "id": 0,
                            "model": "",
                            "modelSpine": "",
                            "name": "",
                            "type": 0
                        }
                    ],
                    "events": [
                        {
                            "coin": 0,
                            "createTime": "",
                            "detail": "",
                            "eventType": 0,
                            "eventVal": 0,
                            "finishVal": 0,
                            "icon": "",
                            "playerEventId": 0,
                            "playerId": 0
                        }
                    ],
                    "expValue": 0,
                    "followWx": 0,
                    "icon": "",
                    "interacts": [
                        {
                            "coin": 0,
                            "createTime": "",
                            "detail": "",
                            "eventType": 0,
                            "eventVal": 0,
                            "finishVal": 0,
                            "icon": "",
                            "playerEventId": 0,
                            "playerId": 0
                        }
                    ],
                    "isVip": 0,
                    "level": 0,
                    "levelName": "",
                    "model": "",
                    "nextExpValue": 0,
                    "nickname": "",
                    "ore": 0,
                    "playerId": 0,
                    "protectStatus": 0,
                    "rankScore": 0,
                    "score": 0,
                    "sex": 0,
                    "status": 0,
                    "userId": "",
                    "vim": 0,
                    "vimLimit": 0,
                    "vipOre": 0,
                    "vipStatus": 0,
                    "wish": 0
                },
                "score": 0
            },
            "value": 0
        }
    ],
    "message": "",
    "msg": ""
}
```



-----------分割线
获取知识塔详情

**接口地址** `/pasture/player/tower/api/detail`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«知识塔»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    知识塔   |   知识塔    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**知识塔**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| awordStatus         |     每日领奖状态 1未领取 2已领取      |  int32   |      |
            | dayDrops         |     上一个等级每日奖励      |  array   | 掉落物品明细     |
            | drops         |     首次通关奖励      |  array   | 掉落物品明细     |
            | level         |     level      |  int32   |      |
            | name         |     name名称      |  string   |      |
            | npcs         |     NPC      |  array   | Npc     |
            | playerTowerId         |     id      |  int64   |      |
            | suggest         |     suggest挑战建议      |  string   |      |
            

**掉落物品明细**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| code         |     图标      |  string   |      |
            | name         |     名称      |  string   |      |
            | sourceId         |     sourceId道具id      |  int64   |      |
            | sourceType         |     sourceType类型 1背包方案 2背包道具 3主角经验 4能量币 5体力 6矿石 7宠物 8装备 9排位积分 10 金币      |  int32   |      |
            | type         |     类型 1普通掉落 2首次掉落      |  int32   |      |
            | upgrade         |     人物升级信息      |  升级后玩家信息   | 升级后玩家信息     |
            | value         |     num数量      |  int32   |      |
            

**升级后玩家信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coinType         |     收获代币类型： 1-金币，2-能量      |  int32   |      |
            | coinVal         |     收获代币值      |  int32   |      |
            | drops         |     物品掉落      |  array   | 掉落物品明细     |
            | level         |     人物等级      |  int32   |      |
            | player         |     玩家      |  玩家详情   | 玩家详情     |
            | score         |     评分      |  int32   |      |
            

**玩家详情**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | breed         |     动物养殖      |  动物养殖   | 动物养殖     |
            | coin         |     金币      |  int32   |      |
            | energy         |     能量      |  int32   |      |
            | equipageScore         |     装备评分      |  int32   |      |
            | equipages         |     装备      |  array   | 玩家装备     |
            | events         |     互动事件      |  array   | 互动事件     |
            | expValue         |     expValue当前经验值      |  int64   |      |
            | followWx         |     是否关注微信公众号 1-已关注 0-未关注      |  int32   |      |
            | icon         |     玩家头像图标地址      |  string   |      |
            | interacts         |     互动      |  array   | 互动事件     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | level         |     人物等级      |  int32   |      |
            | levelName         |     官职      |  string   |      |
            | model         |     角色模型      |  string   |      |
            | nextExpValue         |     nextExpValue下一个等级所需经验值      |  int64   |      |
            | nickname         |     玩家昵称      |  string   |      |
            | ore         |     粮草      |  int32   |      |
            | playerId         |     玩家id      |  int64   |      |
            | protectStatus         |     保护状态 1有保护 2无保护      |  int32   |      |
            | rankScore         |     排位积分      |  int32   |      |
            | score         |     总评分      |  int32   |      |
            | sex         |     玩家性别 1-男 2-女      |  int32   |      |
            | status         |     玩家状态 0-不可用 1-正常      |  int32   |      |
            | userId         |     用户id      |  string   |      |
            | vim         |     活力      |  int32   |      |
            | vimLimit         |     活力上限      |  int32   |      |
            | vipOre         |     vip粮草      |  int32   |      |
            | vipStatus         |     vip状态      |  int32   |      |
            | wish         |     许愿值      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**动物养殖**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| animalId         |     animalId动物id      |  int64   |      |
            | breedTime         |     breedTime养殖时间      |  date-time   |      |
            | createTime         |     createTime      |  date-time   |      |
            | downTime         |     倒计时      |  int64   |      |
            | modelSpine         |     动效      |  string   |      |
            | petId         |     petId宠物id      |  int64   |      |
            | pickStatus         |     是否可以采摘 1可以 2自己不能偷 3好友不能偷      |  int32   |      |
            | playerBreedId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态1 成长中 2待收获 3已收获      |  int32   |      |
            | suspendTime         |     暂停时间 不为空就是被暂停      |  date-time   |      |
            

**玩家装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| id         |     id      |  int64   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | type         |     类型 1神兵 2宝马 3皮肤 4兵书 5虎符      |  int32   |      |
            

**互动事件**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coin         |     金币      |  int32   |      |
            | createTime         |     createTime      |  date-time   |      |
            | detail         |     详情      |  string   |      |
            | eventType         |     操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽  8便便 9蚊子 10老鼠      |  int32   |      |
            | eventVal         |     eventVal事件值      |  int32   |      |
            | finishVal         |     eventVal事件完成值      |  int32   |      |
            | icon         |     图片      |  string   |      |
            | playerEventId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            

**Npc**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     attrType属性类型 1草 2火 3水 4光 5暗      |  int32   |      |
            | drops         |     掉落物品      |  array   | 掉落物品明细     |
            | icon         |     icon图标      |  string   |      |
            | level         |     level等级      |  int32   |      |
            | model         |     model模型      |  string   |      |
            | name         |     name名称      |  string   |      |
            | npcId         |     Npcid      |  int64   |      |
            | score         |     分      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "awordStatus": 0,
        "dayDrops": [
            {
                "code": "",
                "name": "",
                "sourceId": 0,
                "sourceType": 0,
                "type": 0,
                "upgrade": {
                    "coinType": 0,
                    "coinVal": 0,
                    "drops": [
                        {}
                    ],
                    "level": 0,
                    "player": {
                        "attrs": [
                            {
                                "attrType": 0,
                                "attrValue": 0,
                                "type": 0
                            }
                        ],
                        "breed": {
                            "animalId": 0,
                            "breedTime": "",
                            "createTime": "",
                            "downTime": 0,
                            "modelSpine": "",
                            "petId": 0,
                            "pickStatus": 0,
                            "playerBreedId": 0,
                            "playerId": 0,
                            "seq": 0,
                            "status": 0,
                            "suspendTime": ""
                        },
                        "coin": 0,
                        "energy": 0,
                        "equipageScore": 0,
                        "equipages": [
                            {
                                "id": 0,
                                "model": "",
                                "modelSpine": "",
                                "name": "",
                                "type": 0
                            }
                        ],
                        "events": [
                            {
                                "coin": 0,
                                "createTime": "",
                                "detail": "",
                                "eventType": 0,
                                "eventVal": 0,
                                "finishVal": 0,
                                "icon": "",
                                "playerEventId": 0,
                                "playerId": 0
                            }
                        ],
                        "expValue": 0,
                        "followWx": 0,
                        "icon": "",
                        "interacts": [
                            {
                                "coin": 0,
                                "createTime": "",
                                "detail": "",
                                "eventType": 0,
                                "eventVal": 0,
                                "finishVal": 0,
                                "icon": "",
                                "playerEventId": 0,
                                "playerId": 0
                            }
                        ],
                        "isVip": 0,
                        "level": 0,
                        "levelName": "",
                        "model": "",
                        "nextExpValue": 0,
                        "nickname": "",
                        "ore": 0,
                        "playerId": 0,
                        "protectStatus": 0,
                        "rankScore": 0,
                        "score": 0,
                        "sex": 0,
                        "status": 0,
                        "userId": "",
                        "vim": 0,
                        "vimLimit": 0,
                        "vipOre": 0,
                        "vipStatus": 0,
                        "wish": 0
                    },
                    "score": 0
                },
                "value": 0
            }
        ],
        "drops": [
            {}
        ],
        "level": 0,
        "name": "",
        "npcs": [
            {
                "attrType": 0,
                "drops": [
                    {}
                ],
                "icon": "",
                "level": 0,
                "model": "",
                "name": "",
                "npcId": 0,
                "score": 0
            }
        ],
        "playerTowerId": 0,
        "suggest": ""
    },
    "message": "",
    "msg": ""
}
```



-----------分割线
进入战斗(手动)

**接口地址** `/pasture/player/tower/api/enterTower`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«消耗操作结果信息«(手动)进入战斗»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    消耗操作结果信息«(手动)进入战斗»   |   消耗操作结果信息«(手动)进入战斗»    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**消耗操作结果信息«(手动)进入战斗»**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| bizData         |     业务相关数据      |  (手动)进入战斗   | (手动)进入战斗     |
            | coins         |     能量币数值变化      |  array   | AwardCoinDTO     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | remind         |     提醒内容      |  string   |      |
            | status         |     1-成功 2-代币不足 3-会员级别不足 4当日次数已用完 5积分不足 6事件已存在无效操作 7道具不足       |  int32   |      |
            

**(手动)进入战斗**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| fightRole         |     对战方角色      |  战斗角色   | 战斗角色     |
            | thumbnails         |           |  array   | 战斗角色缩略图     |
            | watchRole         |     观战方角色      |  战斗角色   | 战斗角色     |
            

**战斗角色**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| atk         |     攻击力      |  int32   |      |
            | attrType         |     元素标识      |  int32   |      |
            | blood         |     当前血量      |  int32   |      |
            | bloodLimit         |     生命上限      |  int32   |      |
            | defense         |     防御力      |  int32   |      |
            | endPlotId         |     endPlotId结束剧情id      |  int64   |      |
            | equipages         |     装备      |  array   | 玩家装备     |
            | evolutionType         |     等阶类型（冗余）      |  int32   |      |
            | icon         |     图标      |  string   |      |
            | level         |     等级      |  int32   |      |
            | model         |     模型      |  string   |      |
            | modelTurn         |     模型翻转类型 0 不翻转 1翻转      |  int32   |      |
            | name         |     名称      |  string   |      |
            | pets         |     名将      |  array   | 战斗名将     |
            | roleType         |     角色类型 1玩家 2NPC      |  int32   |      |
            | score         |     评分      |  int32   |      |
            | startPlotId         |     startPlotId开始剧情id      |  int64   |      |
            

**玩家装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| id         |     id      |  int64   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | type         |     类型 1神兵 2宝马 3皮肤 4兵书 5虎符      |  int32   |      |
            

**战斗名将**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| evolutionType         |     evolutionType等阶类型（冗余）      |  int32   |      |
            | icon         |     图标      |  string   |      |
            | model         |     模型      |  string   |      |
            | name         |     名称      |  string   |      |
            | seq         |     排序      |  int32   |      |
            | species         |     种类 1陆地 2飞行      |  int32   |      |
            

**战斗角色缩略图**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| icon         |     图标      |  string   |      |
            | level         |     等级      |  int32   |      |
            | name         |     名称      |  string   |      |
            | status         |     状态 1战斗中 2未开始 3已战胜      |  int32   |      |
            

**AwardCoinDTO**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| name         |           |  string   |      |
            | num         |           |  int32   |      |
            | type         |           |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "bizData": {
            "fightRole": {
                "atk": 0,
                "attrType": 0,
                "blood": 0,
                "bloodLimit": 0,
                "defense": 0,
                "endPlotId": 0,
                "equipages": [
                    {
                        "id": 0,
                        "model": "",
                        "modelSpine": "",
                        "name": "",
                        "type": 0
                    }
                ],
                "evolutionType": 0,
                "icon": "",
                "level": 0,
                "model": "",
                "modelTurn": 0,
                "name": "",
                "pets": [
                    {
                        "evolutionType": 0,
                        "icon": "",
                        "model": "",
                        "name": "",
                        "seq": 0,
                        "species": 0
                    }
                ],
                "roleType": 0,
                "score": 0,
                "startPlotId": 0
            },
            "thumbnails": [
                {
                    "icon": "",
                    "level": 0,
                    "name": "",
                    "status": 0
                }
            ],
            "watchRole": {}
        },
        "coins": [
            {
                "name": "",
                "num": 0,
                "type": 0
            }
        ],
        "isVip": 0,
        "remind": "",
        "status": 0
    },
    "message": "",
    "msg": ""
}
```


