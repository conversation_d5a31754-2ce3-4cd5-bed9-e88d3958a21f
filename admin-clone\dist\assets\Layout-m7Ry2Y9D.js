import{d as a,f as e,Z as s,w as l,o as t,U as o,_ as u,S as d,$ as n,V as c,k as r,a0 as i,a1 as _,G as p,u as y,B as f}from"./vendor-Dhi-hotu.js";import{u as m}from"./auth-Jnmp4-ZM.js";import{D as v,U as h,S as k,a as g,B as w,M as b,b as C,c as j,L as U}from"./antd-DSyIu0Jx.js";import{_ as $}from"./_plugin-vue_export-helper-xcdYWU4n.js";import"./auth-CDe9Kw3v.js";const K={class:"logo"},L={key:0},x={key:1},B={class:"header-left"},M={class:"header-right"},S={class:"page"},z=$(a({__name:"Layout",setup(a){const $=n(),z=s(),D=m(),G=e(!1),I=e([z.path]);l(()=>z.path,a=>{I.value=[a]});const V=async({key:a})=>{try{console.log(`导航到: ${a}`),await $.push(a),console.log(`导航成功: ${a}`)}catch(e){console.error("路由导航失败:",e),window.location.href=`#${a}`}},Z=()=>{D.logout()};return t(async()=>{0===D.menuList.length&&await D.getMenu()}),(a,e)=>{const s=d("a-menu-item"),l=d("a-menu"),t=d("a-layout-sider"),n=d("a-avatar"),m=d("a-dropdown"),$=d("a-layout-header"),z=d("router-view"),D=d("a-layout-content"),q=d("a-layout");return c(),o(q,{class:"layout"},{default:u(()=>[r(t,{collapsed:G.value,"onUpdate:collapsed":e[1]||(e[1]=a=>G.value=a),trigger:null,collapsible:"",theme:"dark",width:"150","collapsed-width":80,class:"custom-sider"},{default:u(()=>[i("div",K,[G.value?(c(),_("span",x,"后台")):(c(),_("span",L,"管理后台"))]),r(l,{selectedKeys:I.value,"onUpdate:selectedKeys":e[0]||(e[0]=a=>I.value=a),theme:"dark",mode:"inline",onClick:V,class:"custom-menu"},{default:u(()=>[r(s,{key:"/dashboard"},{icon:u(()=>[r(y(v))]),default:u(()=>[e[5]||(e[5]=p(" 实时统计 ",-1))]),_:1,__:[5]}),r(s,{key:"/users"},{icon:u(()=>[r(y(h))]),default:u(()=>[e[6]||(e[6]=p(" 用户列表 ",-1))]),_:1,__:[6]}),r(s,{key:"/orders"},{icon:u(()=>[r(y(k))]),default:u(()=>[e[7]||(e[7]=p(" 订单列表 ",-1))]),_:1,__:[7]}),r(s,{key:"/refunds"},{icon:u(()=>[r(y(g))]),default:u(()=>[e[8]||(e[8]=p(" 退款订单 ",-1))]),_:1,__:[8]}),r(s,{key:"/statistics"},{icon:u(()=>[r(y(w))]),default:u(()=>[e[9]||(e[9]=p(" 数据看板 ",-1))]),_:1,__:[9]})]),_:1},8,["selectedKeys"])]),_:1},8,["collapsed"]),r(q,null,{default:u(()=>[r($,{class:"layout-header"},{default:u(()=>[i("div",B,[G.value?(c(),o(y(b),{key:0,class:"trigger",onClick:e[2]||(e[2]=()=>G.value=!G.value)})):(c(),o(y(C),{key:1,class:"trigger",onClick:e[3]||(e[3]=()=>G.value=!G.value)}))]),i("div",M,[r(m,null,{overlay:u(()=>[r(l,null,{default:u(()=>[r(s,{onClick:Z},{default:u(()=>[r(y(U)),e[11]||(e[11]=p(" 退出登录 ",-1))]),_:1,__:[11]})]),_:1})]),default:u(()=>[i("a",{class:"ant-dropdown-link",onClick:e[4]||(e[4]=f(()=>{},["prevent"]))},[r(n,{class:"userhead",size:32,style:{backgroundColor:"#667eea"}},{icon:u(()=>[r(y(h))]),_:1}),e[10]||(e[10]=i("span",{class:"username"},"admin1",-1)),r(y(j))])]),_:1})])]),_:1}),r(D,{class:"layout-content"},{default:u(()=>[i("div",S,[r(z)])]),_:1})]),_:1})]),_:1})}}}),[["__scopeId","data-v-53f6db67"]]);export{z as default};
