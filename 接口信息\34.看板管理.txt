新增看板

**接口地址** `/pasture/admin/spectaculars/add`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| spectaculars         |      spectaculars   |     body        |       true      | com.qimingxing.journey.model.Spectaculars   | com.qimingxing.journey.model.Spectaculars     |
            



**schema属性说明**
  
**com.qimingxing.journey.model.Spectaculars**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| dimensions  | 关联纬度 |   body    |   false   |array  | com.qimingxing.journey.model.SpectacularsDimension      |
| id  | id |   body    |   false   |int32  |       |
| name  | name |   body    |   false   |string  |       |
| userId  | userId |   body    |   false   |int32  |       |

**com.qimingxing.journey.model.SpectacularsDimension**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| dimensionId  | dimensionId |   body    |   false   |int64  |       |
| id  | id |   body    |   false   |int32  |       |
| spectId  | spectId |   body    |   false   |int32  |       |
| viewType  | viewType显示类型 line:折线图 bar:圆饼图 pie:柱状图 |   body    |   false   |string  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------------分割线
新增维度

**接口地址** `/pasture/admin/spectaculars/addDimension`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| spectaculars         |      spectaculars   |     body        |       true      | com.qimingxing.journey.model.Spectaculars   | com.qimingxing.journey.model.Spectaculars     |
            



**schema属性说明**
  
**com.qimingxing.journey.model.Spectaculars**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| dimensions  | 关联纬度 |   body    |   false   |array  | com.qimingxing.journey.model.SpectacularsDimension      |
| id  | id |   body    |   false   |int32  |       |
| name  | name |   body    |   false   |string  |       |
| userId  | userId |   body    |   false   |int32  |       |

**com.qimingxing.journey.model.SpectacularsDimension**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| dimensionId  | dimensionId |   body    |   false   |int64  |       |
| id  | id |   body    |   false   |int32  |       |
| spectId  | spectId |   body    |   false   |int32  |       |
| viewType  | viewType显示类型 line:折线图 bar:圆饼图 pie:柱状图 |   body    |   false   |string  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------------分割线
新增维度视图

**接口地址** `/pasture/admin/spectaculars/addDimensionView`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| spectId         |      spectId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«纬度»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   纬度    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**纬度**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| createName         |     创建人      |  string   |      |
            | createTime         |     创建时间      |  date-time   |      |
            | id         |           |  int64   |      |
            | memo         |     备注      |  string   |      |
            | name         |     名称      |  string   |      |
            | sections         |     纬度区间      |  array   | 纬度区间     |
            | selected         |     是否选中      |  boolean   |      |
            | spectacularsDimensionId         |     纬度关系id      |  int32   |      |
            | unitType         |     单位类型：1秒 2个      |  int32   |      |
            | updateName         |     更新人      |  string   |      |
            | updateTime         |     更新时间      |  date-time   |      |
            | viewType         |     显示类型 line:折线图 bar:圆饼图 pie:柱状图      |  string   |      |
            

**纬度区间**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| cutDay         |     日切时间      |  string   |      |
            | id         |           |  int64   |      |
            | proportion         |     当日占比      |  number   |      |
            | sectionName         |     名称      |  string   |      |
            | statisticsValue         |     统计值      |  number   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "createName": "",
            "createTime": "",
            "id": 0,
            "memo": "",
            "name": "",
            "sections": [
                {
                    "cutDay": "",
                    "id": 0,
                    "proportion": 0,
                    "sectionName": "",
                    "statisticsValue": 0
                }
            ],
            "selected": true,
            "spectacularsDimensionId": 0,
            "unitType": 0,
            "updateName": "",
            "updateTime": "",
            "viewType": ""
        }
    ],
    "message": "",
    "msg": ""
}
```



----------------分割线
spectaculars删除

**接口地址** `/pasture/admin/spectaculars/delete`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------------分割线
删除维度

**接口地址** `/pasture/admin/spectaculars/deleteDimension`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------------分割线
看板详情

**接口地址** `/pasture/admin/spectaculars/detail`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| param         |      param   |     body        |       true      | 看板详情参数   | 看板详情参数     |
            



**schema属性说明**
  
**看板详情参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| endTime  | 结束时间 |   body    |   false   |string  |       |
| id  | id |   body    |   false   |int32  |       |
| startTime  | 开始时间 |   body    |   false   |string  |       |
| timeInterval  | 时间间隔 |   body    |   false   |int32  |       |
| type  | 统计类型 1按日统计 2按周统计 3按月统计 4按年统计 |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«看板»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    看板   |   看板    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**看板**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| dimensions         |     纬度      |  array   | 纬度     |
            | id         |     看板id      |  int32   |      |
            | name         |     名称      |  string   |      |
            | userId         |     用户id      |  int32   |      |
            

**纬度**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| createName         |     创建人      |  string   |      |
            | createTime         |     创建时间      |  date-time   |      |
            | id         |           |  int64   |      |
            | memo         |     备注      |  string   |      |
            | name         |     名称      |  string   |      |
            | sections         |     纬度区间      |  array   | 纬度区间     |
            | selected         |     是否选中      |  boolean   |      |
            | spectacularsDimensionId         |     纬度关系id      |  int32   |      |
            | unitType         |     单位类型：1秒 2个      |  int32   |      |
            | updateName         |     更新人      |  string   |      |
            | updateTime         |     更新时间      |  date-time   |      |
            | viewType         |     显示类型 line:折线图 bar:圆饼图 pie:柱状图      |  string   |      |
            

**纬度区间**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| cutDay         |     日切时间      |  string   |      |
            | id         |           |  int64   |      |
            | proportion         |     当日占比      |  number   |      |
            | sectionName         |     名称      |  string   |      |
            | statisticsValue         |     统计值      |  number   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "dimensions": [
            {
                "createName": "",
                "createTime": "",
                "id": 0,
                "memo": "",
                "name": "",
                "sections": [
                    {
                        "cutDay": "",
                        "id": 0,
                        "proportion": 0,
                        "sectionName": "",
                        "statisticsValue": 0
                    }
                ],
                "selected": true,
                "spectacularsDimensionId": 0,
                "unitType": 0,
                "updateName": "",
                "updateTime": "",
                "viewType": ""
            }
        ],
        "id": 0,
        "name": "",
        "userId": 0
    },
    "message": "",
    "msg": ""
}
```



----------------分割线
获取维度详情

**接口地址** `/pasture/admin/spectaculars/dimensionDetail`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| param         |      param   |     body        |       true      | 维度详情参数   | 维度详情参数     |
            



**schema属性说明**
  
**维度详情参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| appCode  | 应用编号 |   body    |   false   |string  |       |
| endTime  | 结束时间 |   body    |   false   |string  |       |
| granularity  | 颗粒度 1全部应用 2应用级 （默认） 3渠道级 |   body    |   false   |int32  |       |
| id  | id |   body    |   false   |int32  |       |
| startTime  | 开始时间 |   body    |   false   |string  |       |
| timeInterval  | 时间间隔 |   body    |   false   |int32  |       |
| type  | 统计类型 1按日统计 2按周统计 3按月统计 4按年统计 |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«纬度»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    纬度   |   纬度    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**纬度**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| createName         |     创建人      |  string   |      |
            | createTime         |     创建时间      |  date-time   |      |
            | id         |           |  int64   |      |
            | memo         |     备注      |  string   |      |
            | name         |     名称      |  string   |      |
            | sections         |     纬度区间      |  array   | 纬度区间     |
            | selected         |     是否选中      |  boolean   |      |
            | spectacularsDimensionId         |     纬度关系id      |  int32   |      |
            | unitType         |     单位类型：1秒 2个      |  int32   |      |
            | updateName         |     更新人      |  string   |      |
            | updateTime         |     更新时间      |  date-time   |      |
            | viewType         |     显示类型 line:折线图 bar:圆饼图 pie:柱状图      |  string   |      |
            

**纬度区间**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| cutDay         |     日切时间      |  string   |      |
            | id         |           |  int64   |      |
            | proportion         |     当日占比      |  number   |      |
            | sectionName         |     名称      |  string   |      |
            | statisticsValue         |     统计值      |  number   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "createName": "",
        "createTime": "",
        "id": 0,
        "memo": "",
        "name": "",
        "sections": [
            {
                "cutDay": "",
                "id": 0,
                "proportion": 0,
                "sectionName": "",
                "statisticsValue": 0
            }
        ],
        "selected": true,
        "spectacularsDimensionId": 0,
        "unitType": 0,
        "updateName": "",
        "updateTime": "",
        "viewType": ""
    },
    "message": "",
    "msg": ""
}
```



----------------分割线
spectaculars获取列表

**接口地址** `/pasture/admin/spectaculars/list`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| spectaculars         |      spectaculars   |     body        |       true      | 看板列表参数   | 看板列表参数     |
            



**schema属性说明**
  
**看板列表参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| page  | 页码 |   body    |   false   |int32  |       |
| size  | 页码 |   body    |   false   |int32  |       |
| userId  | 用户id |   body    |   false   |string  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«com.qimingxing.journey.model.Spectaculars»»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   com.qimingxing.journey.model.Spectaculars    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qimingxing.journey.model.Spectaculars**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| dimensions         |     关联纬度      |  array   | com.qimingxing.journey.model.SpectacularsDimension     |
            | id         |     id      |  int32   |      |
            | name         |     name      |  string   |      |
            | userId         |     userId      |  int32   |      |
            

**com.qimingxing.journey.model.SpectacularsDimension**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| dimensionId         |     dimensionId      |  int64   |      |
            | id         |     id      |  int32   |      |
            | spectId         |     spectId      |  int32   |      |
            | viewType         |     viewType显示类型 line:折线图 bar:圆饼图 pie:柱状图      |  string   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "dimensions": [
                {
                    "dimensionId": 0,
                    "id": 0,
                    "spectId": 0,
                    "viewType": ""
                }
            ],
            "id": 0,
            "name": "",
            "userId": 0
        }
    ],
    "message": "",
    "msg": ""
}
```



----------------分割线
导出维度

**接口地址** `/pasture/admin/spectaculars/report`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| endTime         |      endTime   |     query        |       false      | string   |      |
            | id         |      id   |     query        |       true      | integer   |      |
            | startTime         |      startTime   |     query        |       false      | string   |      |
            | timeInterval         |      timeInterval   |     query        |       false      | string   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

暂无




**响应示例**


```json

```



----------------分割线
spectaculars更新

**接口地址** `/pasture/admin/spectaculars/update`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| spectaculars         |      spectaculars   |     body        |       true      | com.qimingxing.journey.model.Spectaculars   | com.qimingxing.journey.model.Spectaculars     |
            



**schema属性说明**
  
**com.qimingxing.journey.model.Spectaculars**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| dimensions  | 关联纬度 |   body    |   false   |array  | com.qimingxing.journey.model.SpectacularsDimension      |
| id  | id |   body    |   false   |int32  |       |
| name  | name |   body    |   false   |string  |       |
| userId  | userId |   body    |   false   |int32  |       |

**com.qimingxing.journey.model.SpectacularsDimension**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| dimensionId  | dimensionId |   body    |   false   |int64  |       |
| id  | id |   body    |   false   |int32  |       |
| spectId  | spectId |   body    |   false   |int32  |       |
| viewType  | viewType显示类型 line:折线图 bar:圆饼图 pie:柱状图 |   body    |   false   |string  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------------分割线
更新维度

**接口地址** `/pasture/admin/spectaculars/updateDimension`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| param         |      param   |     body        |       true      | 维度关系   | 维度关系     |
            



**schema属性说明**
  
**维度关系**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id  | id |   body    |   false   |int32  |       |
| viewType  | 显示类型 line:折线图 bar:圆饼图 pie:柱状图 |   body    |   false   |string  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------------分割线
获取昨日数据

**接口地址** `/pasture/admin/spectaculars/yesterdayData`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«纬度»»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   纬度    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**纬度**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| createName         |     创建人      |  string   |      |
            | createTime         |     创建时间      |  date-time   |      |
            | id         |           |  int64   |      |
            | memo         |     备注      |  string   |      |
            | name         |     名称      |  string   |      |
            | sections         |     纬度区间      |  array   | 纬度区间     |
            | selected         |     是否选中      |  boolean   |      |
            | spectacularsDimensionId         |     纬度关系id      |  int32   |      |
            | unitType         |     单位类型：1秒 2个      |  int32   |      |
            | updateName         |     更新人      |  string   |      |
            | updateTime         |     更新时间      |  date-time   |      |
            | viewType         |     显示类型 line:折线图 bar:圆饼图 pie:柱状图      |  string   |      |
            

**纬度区间**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| cutDay         |     日切时间      |  string   |      |
            | id         |           |  int64   |      |
            | proportion         |     当日占比      |  number   |      |
            | sectionName         |     名称      |  string   |      |
            | statisticsValue         |     统计值      |  number   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "createName": "",
            "createTime": "",
            "id": 0,
            "memo": "",
            "name": "",
            "sections": [
                {
                    "cutDay": "",
                    "id": 0,
                    "proportion": 0,
                    "sectionName": "",
                    "statisticsValue": 0
                }
            ],
            "selected": true,
            "spectacularsDimensionId": 0,
            "unitType": 0,
            "updateName": "",
            "updateTime": "",
            "viewType": ""
        }
    ],
    "message": "",
    "msg": ""
}
```


