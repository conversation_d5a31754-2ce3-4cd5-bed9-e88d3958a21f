appMoudle获取列表

**接口地址** `/pasture/app/module/list`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«com.qimingxing.journey.model.AppModule»»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   com.qimingxing.journey.model.AppModule    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qimingxing.journey.model.AppModule**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| appCode         |     appCode应用编码      |  string   |      |
            | checkVersion         |     checkVersion是否检查版本 1检查 2不检查      |  int32   |      |
            | code         |     code模块编码      |  string   |      |
            | content         |     content携带内容      |  string   |      |
            | id         |     id      |  int64   |      |
            | name         |     name模块名称      |  string   |      |
            | status         |     status状态 1开放 2关闭      |  int32   |      |
            | version         |     version开放版本 版本控制高于用户分群控制      |  string   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "appCode": "",
            "checkVersion": 0,
            "code": "",
            "content": "",
            "id": 0,
            "name": "",
            "status": 0,
            "version": ""
        }
    ],
    "message": "",
    "msg": ""
}
```


