import{d as e,f as a,r as l,o as t,a1 as u,a0 as o,k as r,_ as n,S as d,V as i,G as s,a2 as c,U as p,a3 as v,F as f}from"./vendor-Dhi-hotu.js";import{a as _,_ as y}from"./_plugin-vue_export-helper-xcdYWU4n.js";import{u as m}from"./app-C5BK_7TP.js";import{d as b}from"./antd-DSyIu0Jx.js";import"./auth-CDe9Kw3v.js";const g={class:"user-management"},k=["title"],h=["title"],I=y(e({__name:"UserManagement",setup(e){const y=a(!1),I=a(!1),x=a(!1),w=a([]),C=a(null),S=a(null),j=l({appCode:void 0,playerId:void 0,account:void 0,name:void 0,page:1,size:10}),z=l({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`}),V=[{title:"ID",dataIndex:"id",key:"id",width:80},{title:"账号",dataIndex:"account",key:"account",width:120},{title:"昵称",dataIndex:"nickname",key:"nickname",width:100},{title:"渠道",dataIndex:"appCode",key:"appCode",width:100},{title:"等级",dataIndex:"level",key:"level",width:60},{title:"游戏币",dataIndex:"coin",key:"coin",width:80},{title:"VIP状态",dataIndex:"vipStatus",key:"vipStatus",width:80},{title:"状态",dataIndex:"status",key:"status",width:60},{title:"创建时间",dataIndex:"createTime",key:"createTime",width:140},{title:"操作",key:"action",width:150,fixed:"right"}],D=async()=>{y.value=!0;try{const e={page:z.current,size:z.pageSize};j.playerId&&(e.playerId=j.playerId),j.account?.trim()&&(e.account=j.account.trim()),j.name?.trim()&&(e.name=j.name.trim()),console.log("搜索玩家参数:",e);const a=await(e=>_.post("/admin/player/list",e))(e);console.log("玩家列表响应:",a),a.data&&(w.value=a.data.list||[],z.total=a.data.total||0)}catch(e){console.error("获取玩家列表失败:",e),b.error("获取玩家列表失败")}finally{y.value=!1}},U=()=>{Object.assign(j,{appCode:void 0,playerId:void 0,account:void 0,name:void 0,page:1,size:10}),z.current=1},P=e=>{z.current=e.current||1,z.pageSize=e.pageSize||10,D()},$=async e=>{var a;if(e.id)try{const l=await(a=e.id,_.get("/player/playerDetail",{params:{playerId:a}}));l.data&&(C.value=l.data.player||e,I.value=!0)}catch(l){console.error("获取玩家详情失败:",l),C.value=e,I.value=!0}else b.error("玩家ID不能为空")},T=async e=>{var a;if(e.id)try{const l=await(a=e.id,_.get("/player/coin",{params:{playerId:a}}));l.data&&(S.value=l.data,x.value=!0)}catch(l){console.error("获取玩家币信息失败:",l),b.error("获取玩家币信息失败")}else b.error("玩家ID不能为空")},L=e=>{if(!e)return"";const a=(e=>{if(!e)return"";try{const a=atob(e);if(a&&/^[\x20-\x7E\u4e00-\u9fa5]*$/.test(a))return a}catch(a){}return e})(e);return a.length<=12?a:`${a.substring(0,8)}...${a.substring(a.length-4)}`},O=m(),A=e=>{if(!e)return"未知";const a=O.getAppName(e);if(a!==e)return a;return{pasture:"神器农场","pasture-xiaomi":"神器农场-小米",journey:"喵喵学园",journey_zy:"喵喵学园-掌育","journey-360":"喵喵学园-360",1:"神器农场",2:"喵喵学园",3:"神器农场-小米"}[e]||(e.length>12?`${e.substring(0,10)}...`:e)},E=e=>{if(!e)return"default";switch(e){case"pasture":return"purple";case"pasture-360":return"magenta";case"journey":return"blue";case"journey_zy":return"green";case"journey-360":return"orange";default:return"geekblue"}};return t(async()=>{const e=m();await e.initAppList(),console.log("UserManagement初始化完成，直接搜索用户"),D()}),(e,a)=>{const l=d("a-input-number"),t=d("a-form-item"),_=d("a-input"),m=d("a-button"),b=d("a-form"),O=d("a-card"),M=d("a-tag"),N=d("a-space"),F=d("a-table"),G=d("a-descriptions-item"),J=d("a-descriptions"),Q=d("a-modal");return i(),u("div",g,[a[9]||(a[9]=o("div",{class:"page-header"},[o("h2",null,"玩家管理")],-1)),r(O,{class:"search-card",style:{"margin-bottom":"16px"}},{default:n(()=>[r(b,{layout:"inline",model:j},{default:n(()=>[r(t,{label:"玩家ID"},{default:n(()=>[r(l,{value:j.playerId,"onUpdate:value":a[0]||(a[0]=e=>j.playerId=e),placeholder:"请输入玩家ID",style:{width:"150px"}},null,8,["value"])]),_:1}),r(t,{label:"账号"},{default:n(()=>[r(_,{value:j.account,"onUpdate:value":a[1]||(a[1]=e=>j.account=e),placeholder:"请输入账号"},null,8,["value"])]),_:1}),r(t,{label:"昵称"},{default:n(()=>[r(_,{value:j.name,"onUpdate:value":a[2]||(a[2]=e=>j.name=e),placeholder:"请输入昵称"},null,8,["value"])]),_:1}),r(t,null,{default:n(()=>[r(m,{type:"primary",onClick:D,loading:y.value},{default:n(()=>a[5]||(a[5]=[s(" 搜索 ",-1)])),_:1,__:[5]},8,["loading"]),r(m,{onClick:U,style:{"margin-left":"8px"}},{default:n(()=>a[6]||(a[6]=[s(" 重置 ",-1)])),_:1,__:[6]})]),_:1})]),_:1},8,["model"])]),_:1}),r(O,null,{default:n(()=>[r(F,{columns:V,"data-source":w.value,loading:y.value,pagination:z,onChange:P,"row-key":"id"},{bodyCell:n(({column:e,record:l})=>["account"===e.key?(i(),u("span",{key:0,title:l.account},v(L(l.account)),9,k)):c("",!0),"appCode"===e.key?(i(),p(M,{key:1,color:E(l.appCode)},{default:n(()=>[s(v(A(l.appCode)),1)]),_:2},1032,["color"])):c("",!0),"vipStatus"===e.key?(i(),p(M,{key:2,color:1===l.vipStatus?"gold":"default"},{default:n(()=>[s(v(1===l.vipStatus?"VIP":"普通"),1)]),_:2},1032,["color"])):c("",!0),"status"===e.key?(i(),p(M,{key:3,color:0===l.status?"green":"red"},{default:n(()=>[s(v(0===l.status?"正常":"禁用"),1)]),_:2},1032,["color"])):c("",!0),"coin"===e.key?(i(),u(f,{key:4},[s(v(l.coin||0),1)],64)):c("",!0),"action"===e.key?(i(),p(N,{key:5},{default:n(()=>[r(m,{type:"link",size:"small",onClick:e=>$(l)},{default:n(()=>a[7]||(a[7]=[s(" 查看详情 ",-1)])),_:2,__:[7]},1032,["onClick"]),r(m,{type:"link",size:"small",onClick:e=>T(l)},{default:n(()=>a[8]||(a[8]=[s(" 查看币信息 ",-1)])),_:2,__:[8]},1032,["onClick"])]),_:2},1024)):c("",!0)]),_:1},8,["data-source","loading","pagination"])]),_:1}),r(Q,{open:I.value,"onUpdate:open":a[3]||(a[3]=e=>I.value=e),title:"玩家详情",footer:null,width:"800px"},{default:n(()=>[C.value?(i(),p(J,{key:0,column:2,bordered:""},{default:n(()=>[r(G,{label:"玩家ID"},{default:n(()=>[s(v(C.value.id),1)]),_:1}),r(G,{label:"用户ID"},{default:n(()=>[s(v(C.value.userId),1)]),_:1}),r(G,{label:"账号"},{default:n(()=>[o("span",{title:C.value.account},v(L(C.value.account||"")),9,h)]),_:1}),r(G,{label:"渠道"},{default:n(()=>[r(M,{color:E(C.value.appCode||"")},{default:n(()=>[s(v(A(C.value.appCode||"")),1)]),_:1},8,["color"])]),_:1}),r(G,{label:"昵称"},{default:n(()=>[s(v(C.value.nickname),1)]),_:1}),r(G,{label:"性别"},{default:n(()=>[s(v(1===C.value.sex?"男":"女"),1)]),_:1}),r(G,{label:"等级"},{default:n(()=>[s(v(C.value.level),1)]),_:1}),r(G,{label:"等级名称"},{default:n(()=>[s(v(C.value.levelName),1)]),_:1}),r(G,{label:"经验值"},{default:n(()=>[s(v(C.value.expValue||C.value.exp),1)]),_:1}),r(G,{label:"下级经验"},{default:n(()=>[s(v(C.value.nextExpValue),1)]),_:1}),r(G,{label:"游戏币"},{default:n(()=>[s(v(C.value.coin),1)]),_:1}),r(G,{label:"矿石"},{default:n(()=>[s(v(C.value.ore),1)]),_:1}),r(G,{label:"VIP矿石"},{default:n(()=>[s(v(C.value.vipOre),1)]),_:1}),r(G,{label:"体力"},{default:n(()=>[s(v(C.value.vim),1)]),_:1}),r(G,{label:"体力上限"},{default:n(()=>[s(v(C.value.vimLimit),1)]),_:1}),r(G,{label:"许愿币"},{default:n(()=>[s(v(C.value.wish),1)]),_:1}),r(G,{label:"积分"},{default:n(()=>[s(v(C.value.score),1)]),_:1}),r(G,{label:"排行积分"},{default:n(()=>[s(v(C.value.rankScore),1)]),_:1}),r(G,{label:"VIP状态"},{default:n(()=>[r(M,{color:1===C.value.vipStatus?"gold":"default"},{default:n(()=>[s(v(1===C.value.vipStatus?"VIP":"普通"),1)]),_:1},8,["color"])]),_:1}),r(G,{label:"状态"},{default:n(()=>[r(M,{color:0===C.value.status?"green":"red"},{default:n(()=>[s(v(0===C.value.status?"正常":"禁用"),1)]),_:1},8,["color"])]),_:1}),r(G,{label:"创建时间",span:2},{default:n(()=>[s(v(C.value.createTime),1)]),_:1})]),_:1})):c("",!0)]),_:1},8,["open"]),r(Q,{open:x.value,"onUpdate:open":a[4]||(a[4]=e=>x.value=e),title:"玩家币信息",footer:null,width:"600px"},{default:n(()=>[S.value?(i(),p(J,{key:0,column:2,bordered:""},{default:n(()=>[r(G,{label:"游戏币"},{default:n(()=>[s(v(S.value.coin||0),1)]),_:1}),r(G,{label:"矿石"},{default:n(()=>[s(v(S.value.ore||0),1)]),_:1}),r(G,{label:"VIP矿石"},{default:n(()=>[s(v(S.value.vipOre||0),1)]),_:1}),r(G,{label:"体力"},{default:n(()=>[s(v(S.value.vim||0),1)]),_:1}),r(G,{label:"体力上限"},{default:n(()=>[s(v(S.value.vimLimit||0),1)]),_:1}),r(G,{label:"许愿币"},{default:n(()=>[s(v(S.value.wish||0),1)]),_:1})]),_:1})):c("",!0)]),_:1},8,["open"])])}}}),[["__scopeId","data-v-b439645a"]]);export{I as default};
