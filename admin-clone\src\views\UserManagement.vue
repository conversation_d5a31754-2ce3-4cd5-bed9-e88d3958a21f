<template>
  <div class="user-management">
    <div class="page-header">
      <h2>玩家管理</h2>
    </div>

    <!-- 搜索表单 -->
    <a-card class="search-card" style="margin-bottom: 16px;">
      <a-form layout="inline" :model="searchForm">

        <a-form-item label="玩家ID">
          <a-input-number v-model:value="searchForm.playerId" placeholder="请输入玩家ID" style="width: 150px;" />
        </a-form-item>
        <a-form-item label="账号">
          <a-input v-model:value="searchForm.account" placeholder="请输入账号" />
        </a-form-item>
        <a-form-item label="昵称">
          <a-input v-model:value="searchForm.name" placeholder="请输入昵称" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="searchPlayers" :loading="loading">
            搜索
          </a-button>
          <a-button @click="resetSearch" style="margin-left: 8px;">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 玩家列表 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="players"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'account'">
            <span :title="record.account">{{ formatAccount(record.account) }}</span>
          </template>
          <template v-if="column.key === 'appCode'">
            <a-tag :color="getChannelColor(record.appCode)">
              {{ formatChannel(record.appCode) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'vipStatus'">
            <a-tag :color="record.vipStatus === 1 ? 'gold' : 'default'">
              {{ record.vipStatus === 1 ? 'VIP' : '普通' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 0 ? 'green' : 'red'">
              {{ record.status === 0 ? '正常' : '禁用' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'coin'">
            {{ record.coin || 0 }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewPlayer(record)">
                查看详情
              </a-button>
              <a-button type="link" size="small" @click="viewPlayerCoin(record)">
                查看币信息
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 玩家详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="玩家详情"
      :footer="null"
      width="800px"
    >
      <a-descriptions :column="2" bordered v-if="currentPlayer">
        <a-descriptions-item label="玩家ID">{{ currentPlayer.id }}</a-descriptions-item>
        <a-descriptions-item label="用户ID">{{ currentPlayer.userId }}</a-descriptions-item>
        <a-descriptions-item label="账号">
          <span :title="currentPlayer.account">{{ formatAccount(currentPlayer.account || '') }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="渠道">
          <a-tag :color="getChannelColor(currentPlayer.appCode || '')">
            {{ formatChannel(currentPlayer.appCode || '') }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="昵称">{{ currentPlayer.nickname }}</a-descriptions-item>
        <a-descriptions-item label="性别">{{ currentPlayer.sex === 1 ? '男' : '女' }}</a-descriptions-item>
        <a-descriptions-item label="等级">{{ currentPlayer.level }}</a-descriptions-item>
        <a-descriptions-item label="等级名称">{{ currentPlayer.levelName }}</a-descriptions-item>
        <a-descriptions-item label="经验值">{{ currentPlayer.expValue || currentPlayer.exp }}</a-descriptions-item>
        <a-descriptions-item label="下级经验">{{ currentPlayer.nextExpValue }}</a-descriptions-item>
        <a-descriptions-item label="游戏币">{{ currentPlayer.coin }}</a-descriptions-item>
        <a-descriptions-item label="矿石">{{ currentPlayer.ore }}</a-descriptions-item>
        <a-descriptions-item label="VIP矿石">{{ currentPlayer.vipOre }}</a-descriptions-item>
        <a-descriptions-item label="体力">{{ currentPlayer.vim }}</a-descriptions-item>
        <a-descriptions-item label="体力上限">{{ currentPlayer.vimLimit }}</a-descriptions-item>
        <a-descriptions-item label="许愿币">{{ currentPlayer.wish }}</a-descriptions-item>
        <a-descriptions-item label="积分">{{ currentPlayer.score }}</a-descriptions-item>
        <a-descriptions-item label="排行积分">{{ currentPlayer.rankScore }}</a-descriptions-item>
        <a-descriptions-item label="VIP状态">
          <a-tag :color="currentPlayer.vipStatus === 1 ? 'gold' : 'default'">
            {{ currentPlayer.vipStatus === 1 ? 'VIP' : '普通' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="currentPlayer.status === 0 ? 'green' : 'red'">
            {{ currentPlayer.status === 0 ? '正常' : '禁用' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间" :span="2">{{ currentPlayer.createTime }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 币信息弹窗 -->
    <a-modal
      v-model:open="coinModalVisible"
      title="玩家币信息"
      :footer="null"
      width="600px"
    >
      <a-descriptions :column="2" bordered v-if="playerCoin">
        <a-descriptions-item label="游戏币">{{ playerCoin.coin || 0 }}</a-descriptions-item>
        <a-descriptions-item label="矿石">{{ playerCoin.ore || 0 }}</a-descriptions-item>
        <a-descriptions-item label="VIP矿石">{{ playerCoin.vipOre || 0 }}</a-descriptions-item>
        <a-descriptions-item label="体力">{{ playerCoin.vim || 0 }}</a-descriptions-item>
        <a-descriptions-item label="体力上限">{{ playerCoin.vimLimit || 0 }}</a-descriptions-item>
        <a-descriptions-item label="许愿币">{{ playerCoin.wish || 0 }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import {
  getPlayerList,
  getPlayerDetail,
  getPlayerCoin,
  type PlayerQueryParams,
  type Player,
  type PlayerCoin
} from '../api/player'
import { useAppStore } from '../stores/app'


// 响应式数据
const loading = ref(false)
const detailModalVisible = ref(false)
const coinModalVisible = ref(false)
const players = ref<Player[]>([])
const currentPlayer = ref<Player | null>(null)
const playerCoin = ref<PlayerCoin | null>(null)



// 搜索表单
const searchForm = reactive<PlayerQueryParams>({
  appCode: undefined,
  playerId: undefined,
  account: undefined,
  name: undefined,
  page: 1,
  size: 10
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80
  },
  {
    title: '账号',
    dataIndex: 'account',
    key: 'account',
    width: 120
  },
  {
    title: '昵称',
    dataIndex: 'nickname',
    key: 'nickname',
    width: 100
  },
  {
    title: '渠道',
    dataIndex: 'appCode',
    key: 'appCode',
    width: 100
  },
  {
    title: '等级',
    dataIndex: 'level',
    key: 'level',
    width: 60
  },
  {
    title: '游戏币',
    dataIndex: 'coin',
    key: 'coin',
    width: 80
  },
  {
    title: 'VIP状态',
    dataIndex: 'vipStatus',
    key: 'vipStatus',
    width: 80
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 60
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 140
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
]



// 搜索玩家
const searchPlayers = async () => {
  loading.value = true
  try {
    // 只发送admin接口需要的参数
    const params: any = {
      page: pagination.current,
      size: pagination.pageSize
    }

    // 不传递appCode参数，查询所有应用的用户

    // 只添加有值的搜索参数
    if (searchForm.playerId) params.playerId = searchForm.playerId
    if (searchForm.account?.trim()) params.account = searchForm.account.trim()
    if (searchForm.name?.trim()) params.name = searchForm.name.trim()

    console.log('搜索玩家参数:', params)
    const response = await getPlayerList(params)
    console.log('玩家列表响应:', response)

    if (response.data) {
      players.value = response.data.list || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取玩家列表失败:', error)
    message.error('获取玩家列表失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    appCode: undefined,
    playerId: undefined,
    account: undefined,
    name: undefined,
    page: 1,
    size: 10
  })
  pagination.current = 1
  // 重置后不自动搜索，需要用户选择应用渠道
}

// 处理表格变化
const handleTableChange: TableProps['onChange'] = (pag) => {
  pagination.current = pag.current || 1
  pagination.pageSize = pag.pageSize || 10
  searchPlayers()
}

// 查看玩家详情
const viewPlayer = async (player: Player) => {
  if (!player.id) {
    message.error('玩家ID不能为空')
    return
  }

  try {
    const response = await getPlayerDetail(player.id)
    if (response.data) {
      currentPlayer.value = response.data.player || player
      detailModalVisible.value = true
    }
  } catch (error) {
    console.error('获取玩家详情失败:', error)
    currentPlayer.value = player
    detailModalVisible.value = true
  }
}

// 查看玩家币信息
const viewPlayerCoin = async (player: Player) => {
  if (!player.id) {
    message.error('玩家ID不能为空')
    return
  }

  try {
    const response = await getPlayerCoin(player.id)
    if (response.data) {
      playerCoin.value = response.data
      coinModalVisible.value = true
    }
  } catch (error) {
    console.error('获取玩家币信息失败:', error)
    message.error('获取玩家币信息失败')
  }
}



// 尝试解密账号 - 支持Base64解码
const decryptAccount = (account: string) => {
  if (!account) return ''

  try {
    // 尝试Base64解码
    const decoded = atob(account)
    // 检查解码后是否是有效的字符串（包含可打印字符）
    if (decoded && /^[\x20-\x7E\u4e00-\u9fa5]*$/.test(decoded)) {
      return decoded
    }
  } catch (e) {
    // Base64解码失败，可能不是Base64编码
  }

  // 如果不是Base64或解码失败，返回原值
  return account
}

// 格式化账号显示 - 先解密再截取
const formatAccount = (account: string) => {
  if (!account) return ''

  // 先尝试解密
  const decrypted = decryptAccount(account)

  // 如果解密后的内容过长，进行截取显示
  if (decrypted.length <= 12) return decrypted
  return `${decrypted.substring(0, 8)}...${decrypted.substring(decrypted.length - 4)}`
}

// 使用应用store
const appStore = useAppStore()

// 格式化渠道显示 - 使用store获取应用名称
const formatChannel = (appCode: string) => {
  if (!appCode) return '未知'

  // 首先尝试从store获取应用名称
  const appName = appStore.getAppName(appCode)

  // 如果store中找到了对应的名称，直接返回
  if (appName !== appCode) {
    return appName
  }

  // 如果store中没有找到，使用硬编码的映射作为备选
  const channelMap: Record<string, string> = {
    'pasture': '神器农场',
    'pasture-xiaomi': '神器农场-小米',
    'journey': '喵喵学园',
    'journey_zy': '喵喵学园-掌育',
    'journey-360': '喵喵学园-360',
    '1': '神器农场',
    '2': '喵喵学园',
    '3': '神器农场-小米'
  }

  return channelMap[appCode] || (appCode.length > 12 ? `${appCode.substring(0, 10)}...` : appCode)
}

// 获取渠道颜色 - 基于实际appCode
const getChannelColor = (appCode: string) => {
  if (!appCode) return 'default'

  // 根据实际的appCode值设置不同颜色
  switch (appCode) {
    case 'pasture':
      return 'purple'      // 牧场学习 - 紫色
    case 'pasture-360':
      return 'magenta'     // 牧场学习-360 - 洋红色
    case 'journey':
      return 'blue'        // 喵喵学园 - 蓝色
    case 'journey_zy':
      return 'green'       // 喵喵学园-掌育 - 绿色
    case 'journey-360':
      return 'orange'      // 喵喵学园-360 - 橙色
    default:
      return 'geekblue'    // 未知渠道 - 默认蓝色
  }
}





// 组件挂载时初始化
onMounted(async () => {
  // 等待应用列表加载完成
  const appStore = useAppStore()
  await appStore.initAppList()

  console.log('UserManagement初始化完成，直接搜索用户')
  // 直接搜索用户，不需要选择应用
  searchPlayers()
})
</script>

<style scoped>
.user-management h1 {
  margin-bottom: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-header h2 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}
</style>
