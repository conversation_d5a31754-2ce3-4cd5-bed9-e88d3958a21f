playerBreed新增

**接口地址** `/pasture/player/breed/add`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerBreed         |      playerBreed   |     body        |       true      | com.qihui.pasture.model.PlayerBreed   | com.qihui.pasture.model.PlayerBreed     |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerBreed**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| animalId  | animalId动物id |   body    |   false   |int64  |       |
| breedTime  | breedTime养殖时间 |   body    |   false   |date-time  |       |
| createTime  | createTime |   body    |   false   |date-time  |       |
| id  | id |   body    |   false   |int64  |       |
| petId  | petId宠物id |   body    |   false   |int64  |       |
| playerId  | playerId |   body    |   false   |int64  |       |
| ratio  | 收获比例 |   body    |   false   |number  |       |
| seq  | seq排序 |   body    |   false   |int32  |       |
| status  | status状态1 成长中 2待收获 3已收获 |   body    |   false   |int32  |       |
| suspendTime  | 暂停 |   body    |   false   |date-time  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------------分割线
playerBreed删除

**接口地址** `/pasture/player/breed/delete`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------------分割线
playerBreed获取详情

**接口地址** `/pasture/player/breed/detail`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«com.qihui.pasture.model.PlayerBreed»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    com.qihui.pasture.model.PlayerBreed   |   com.qihui.pasture.model.PlayerBreed    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerBreed**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| animalId         |     animalId动物id      |  int64   |      |
            | breedTime         |     breedTime养殖时间      |  date-time   |      |
            | createTime         |     createTime      |  date-time   |      |
            | id         |     id      |  int64   |      |
            | petId         |     petId宠物id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            | ratio         |     收获比例      |  number   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态1 成长中 2待收获 3已收获      |  int32   |      |
            | suspendTime         |     暂停      |  date-time   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "animalId": 0,
        "breedTime": "",
        "createTime": "",
        "id": 0,
        "petId": 0,
        "playerId": 0,
        "ratio": 0,
        "seq": 0,
        "status": 0,
        "suspendTime": ""
    },
    "message": "",
    "msg": ""
}
```



----------------分割线
playerBreed获取列表

**接口地址** `/pasture/player/breed/list`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| page         |      page   |     query        |       false      | integer   |      |
            | playerBreed         |      playerBreed   |     body        |       true      | com.qihui.pasture.model.PlayerBreed   | com.qihui.pasture.model.PlayerBreed     |
            | size         |      size   |     query        |       false      | integer   |      |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerBreed**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| animalId  | animalId动物id |   body    |   false   |int64  |       |
| breedTime  | breedTime养殖时间 |   body    |   false   |date-time  |       |
| createTime  | createTime |   body    |   false   |date-time  |       |
| id  | id |   body    |   false   |int64  |       |
| petId  | petId宠物id |   body    |   false   |int64  |       |
| playerId  | playerId |   body    |   false   |int64  |       |
| ratio  | 收获比例 |   body    |   false   |number  |       |
| seq  | seq排序 |   body    |   false   |int32  |       |
| status  | status状态1 成长中 2待收获 3已收获 |   body    |   false   |int32  |       |
| suspendTime  | 暂停 |   body    |   false   |date-time  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«com.qihui.pasture.model.PlayerBreed»»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   com.qihui.pasture.model.PlayerBreed    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerBreed**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| animalId         |     animalId动物id      |  int64   |      |
            | breedTime         |     breedTime养殖时间      |  date-time   |      |
            | createTime         |     createTime      |  date-time   |      |
            | id         |     id      |  int64   |      |
            | petId         |     petId宠物id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            | ratio         |     收获比例      |  number   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态1 成长中 2待收获 3已收获      |  int32   |      |
            | suspendTime         |     暂停      |  date-time   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "animalId": 0,
            "breedTime": "",
            "createTime": "",
            "id": 0,
            "petId": 0,
            "playerId": 0,
            "ratio": 0,
            "seq": 0,
            "status": 0,
            "suspendTime": ""
        }
    ],
    "message": "",
    "msg": ""
}
```



----------------分割线
playerBreed更新

**接口地址** `/pasture/player/breed/update`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerBreed         |      playerBreed   |     body        |       true      | com.qihui.pasture.model.PlayerBreed   | com.qihui.pasture.model.PlayerBreed     |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerBreed**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| animalId  | animalId动物id |   body    |   false   |int64  |       |
| breedTime  | breedTime养殖时间 |   body    |   false   |date-time  |       |
| createTime  | createTime |   body    |   false   |date-time  |       |
| id  | id |   body    |   false   |int64  |       |
| petId  | petId宠物id |   body    |   false   |int64  |       |
| playerId  | playerId |   body    |   false   |int64  |       |
| ratio  | 收获比例 |   body    |   false   |number  |       |
| seq  | seq排序 |   body    |   false   |int32  |       |
| status  | status状态1 成长中 2待收获 3已收获 |   body    |   false   |int32  |       |
| suspendTime  | 暂停 |   body    |   false   |date-time  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```


