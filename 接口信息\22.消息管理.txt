获取消息列表(type消息类型 1宠物互动消息 2抽奖消息) data.data中返回true false 字符串

**接口地址** `/pasture/message/list`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            | type         |      type   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«MyPageInfo«消息»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    MyPageInfo«消息»   |   MyPageInfo«消息»    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**MyPageInfo«消息»**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| data         |           |  string   |      |
            | endRow         |           |  int32   |      |
            | firstPage         |           |  int32   |      |
            | hasNextPage         |           |  boolean   |      |
            | hasPreviousPage         |           |  boolean   |      |
            | isFirstPage         |           |  boolean   |      |
            | isLastPage         |           |  boolean   |      |
            | lastPage         |           |  int32   |      |
            | list         |           |  array   | 消息     |
            | navigateFirstPage         |           |  int32   |      |
            | navigateLastPage         |           |  int32   |      |
            | navigatePages         |           |  int32   |      |
            | navigatepageNums         |           |  array   |      |
            | nextPage         |           |  int32   |      |
            | orderBy         |           |  string   |      |
            | pageNum         |           |  int32   |      |
            | pageSize         |           |  int32   |      |
            | pages         |           |  int32   |      |
            | prePage         |           |  int32   |      |
            | size         |           |  int32   |      |
            | startRow         |           |  int32   |      |
            | total         |           |  int64   |      |
            

**消息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| content         |     content消息内容      |  string   |      |
            | createTime         |     创建时间      |  date-time   |      |
            | friendId         |     friendId好友玩家id      |  int64   |      |
            | id         |     id      |  int64   |      |
            | playerId         |     playerId玩家id      |  int64   |      |
            | status         |     status状态 1未读 2已读      |  int32   |      |
            | title         |     title标题      |  string   |      |
            | type         |     type消息类型 1智慧树互动消息       |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "data": "",
        "endRow": 0,
        "firstPage": 0,
        "hasNextPage": true,
        "hasPreviousPage": true,
        "isFirstPage": true,
        "isLastPage": true,
        "lastPage": 0,
        "list": [
            {
                "content": "",
                "createTime": "",
                "friendId": 0,
                "id": 0,
                "playerId": 0,
                "status": 0,
                "title": "",
                "type": 0
            }
        ],
        "navigateFirstPage": 0,
        "navigateLastPage": 0,
        "navigatePages": 0,
        "navigatepageNums": [],
        "nextPage": 0,
        "orderBy": "",
        "pageNum": 0,
        "pageSize": 0,
        "pages": 0,
        "prePage": 0,
        "size": 0,
        "startRow": 0,
        "total": 0
    },
    "message": "",
    "msg": ""
}
```



----------分割线
读活动消息

**接口地址** `/pasture/message/readMessage`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            | type         |      type   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«boolean»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    boolean   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": true,
    "message": "",
    "msg": ""
}
```


