获取登录公告列表

**接口地址** `/pasture/notice/queryLoginNotice`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«com.qimingxing.journey.model.Notice»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   com.qimingxing.journey.model.Notice    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qimingxing.journey.model.Notice**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| appCode         |     appCode应用编码      |  string   |      |
            | appVersion         |     appVersionapp版本号      |  string   |      |
            | clusterIds         |     clusterIds用户分群id：多个使用，分割      |  string   |      |
            | content         |     content文字内容      |  string   |      |
            | createTime         |     createTime创建时间      |  date-time   |      |
            | createUser         |     createUser创建人      |  string   |      |
            | endTime         |     endTime结束时间      |  date-time   |      |
            | id         |     id      |  int32   |      |
            | inType         |     inType准入类型 1所有玩家 2非关玩家 3非VIP玩家 4非关非VIP玩家      |  int32   |      |
            | intervalTime         |     intervalTime间隔时间 （滚屏公告 单位 秒）（按钮公告 单位 天）      |  int32   |      |
            | link         |     link跳转链接      |  string   |      |
            | num         |     num播放次数      |  int32   |      |
            | pic         |     pic图片内容      |  string   |      |
            | seq         |     seq排序      |  int32   |      |
            | startTime         |     startTime开始时间      |  date-time   |      |
            | status         |     status状态 1正常 2禁用      |  int32   |      |
            | type         |     type类型 1滚屏公告 2登录公告 3按钮公告       |  int32   |      |
            | updateTime         |     updateTime更新时间      |  date-time   |      |
            | updateUser         |     updateUser更新人      |  string   |      |
            | voice         |     voice音频内容      |  string   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "appCode": "",
            "appVersion": "",
            "clusterIds": "",
            "content": "",
            "createTime": "",
            "createUser": "",
            "endTime": "",
            "id": 0,
            "inType": 0,
            "intervalTime": 0,
            "link": "",
            "num": 0,
            "pic": "",
            "seq": 0,
            "startTime": "",
            "status": 0,
            "type": 0,
            "updateTime": "",
            "updateUser": "",
            "voice": ""
        }
    ],
    "message": "",
    "msg": ""
}
```



--------分割线
获取滚屏公告列表

**接口地址** `/pasture/notice/queryScrollNotice`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«com.qimingxing.journey.model.Notice»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   com.qimingxing.journey.model.Notice    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qimingxing.journey.model.Notice**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| appCode         |     appCode应用编码      |  string   |      |
            | appVersion         |     appVersionapp版本号      |  string   |      |
            | clusterIds         |     clusterIds用户分群id：多个使用，分割      |  string   |      |
            | content         |     content文字内容      |  string   |      |
            | createTime         |     createTime创建时间      |  date-time   |      |
            | createUser         |     createUser创建人      |  string   |      |
            | endTime         |     endTime结束时间      |  date-time   |      |
            | id         |     id      |  int32   |      |
            | inType         |     inType准入类型 1所有玩家 2非关玩家 3非VIP玩家 4非关非VIP玩家      |  int32   |      |
            | intervalTime         |     intervalTime间隔时间 （滚屏公告 单位 秒）（按钮公告 单位 天）      |  int32   |      |
            | link         |     link跳转链接      |  string   |      |
            | num         |     num播放次数      |  int32   |      |
            | pic         |     pic图片内容      |  string   |      |
            | seq         |     seq排序      |  int32   |      |
            | startTime         |     startTime开始时间      |  date-time   |      |
            | status         |     status状态 1正常 2禁用      |  int32   |      |
            | type         |     type类型 1滚屏公告 2登录公告 3按钮公告       |  int32   |      |
            | updateTime         |     updateTime更新时间      |  date-time   |      |
            | updateUser         |     updateUser更新人      |  string   |      |
            | voice         |     voice音频内容      |  string   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "appCode": "",
            "appVersion": "",
            "clusterIds": "",
            "content": "",
            "createTime": "",
            "createUser": "",
            "endTime": "",
            "id": 0,
            "inType": 0,
            "intervalTime": 0,
            "link": "",
            "num": 0,
            "pic": "",
            "seq": 0,
            "startTime": "",
            "status": 0,
            "type": 0,
            "updateTime": "",
            "updateUser": "",
            "voice": ""
        }
    ],
    "message": "",
    "msg": ""
}
```


