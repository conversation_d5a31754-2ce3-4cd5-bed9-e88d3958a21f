<template>
  <div class="refund-management">
    <div class="page-header">
      <h2>退款订单管理</h2>
      <div class="header-actions">
        <a-button type="primary" @click="showExportModal" :loading="exportLoading">
          <template #icon>
            <DownloadOutlined />
          </template>
          导出退款数据
        </a-button>
      </div>
    </div>

    <!-- 搜索表单 -->
    <a-card class="search-card" style="margin-bottom: 16px;">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="应用渠道">
          <AppSelector
            v-model="searchForm.appCode"
            width="150px"
          />
        </a-form-item>
        <a-form-item label="退款编号">
          <a-input v-model:value="searchForm.refundSn" placeholder="请输入退款编号" />
        </a-form-item>
        <a-form-item label="订单编号">
          <a-input v-model:value="searchForm.flowNo" placeholder="请输入订单编号" />
        </a-form-item>
        <a-form-item label="退款时间">
          <a-range-picker
            v-model:value="dateRange"
            format="YYYY-MM-DD"
            placeholder="['开始时间', '结束时间']"
            style="width: 240px;"
            @change="handleDateChange"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="searchRefunds" :loading="loading">
            搜索
          </a-button>
          <a-button @click="resetSearch" style="margin-left: 8px;">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 退款列表 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="refunds"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'payType'">
            <a-tag :color="getPayTypeColor(record.payType)">
              {{ getPayTypeName(record.payType) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'refundStatus'">
            <a-tag :color="getRefundStatusColor(record.refundStatus)">
              {{ getRefundStatusName(record.refundStatus) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'type'">
            <a-tag :color="record.type === 1 ? 'blue' : 'orange'">
              {{ record.type === 1 ? '全部退款' : '部分退款' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'amount'">
            ¥{{ record.amount?.toFixed(2) || '0.00' }}
          </template>

        </template>
      </a-table>
    </a-card>

    <!-- 导出弹窗 -->
    <a-modal
      v-model:open="exportModalVisible"
      title="数据导出"
      @ok="handleExport"
      @cancel="exportModalVisible = false"
      :confirm-loading="exportLoading"
      width="500px"
    >
      <a-form :model="exportForm" layout="vertical">
        <a-form-item label="应用渠道">
          <AppSelector
            v-model="exportForm.appCode"
            width="100%"
          />
        </a-form-item>
        <a-form-item label="退款时间" required>
          <a-range-picker
            v-model:value="exportDateRange"
            format="YYYY-MM-DD"
            style="width: 100%;"
            placeholder="['开始日期', '结束日期']"
          />
        </a-form-item>
        <a-form-item label="订单类型">
          <a-radio-group v-model:value="exportForm.type">
            <a-radio :value="1">所有订单</a-radio>
            <a-radio :value="2">出账订单</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { DownloadOutlined } from '@ant-design/icons-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  getRefundList,
  type RefundQueryParams,
  type Refund,
  type ExportRefundParams
} from '../api/order'
import AppSelector from '../components/AppSelector.vue'
import { useAppStore } from '../stores/app'
import { handleDateRangeChange } from '../utils/dateFormat'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const refunds = ref<Refund[]>([])
const dateRange = ref<[Dayjs, Dayjs] | null>(null)
const exportModalVisible = ref(false)
const exportDateRange = ref<[Dayjs, Dayjs] | null>(null)

// 搜索表单
const searchForm = reactive<RefundQueryParams>({
  page: 1,
  size: 10
})

// 导出表单
const exportForm = reactive({
  appCode: '',
  endTime: '',
  startTime: '',
  type: 1 // 默认所有订单
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '退款订单号',
    dataIndex: 'refundSn',
    key: 'refundSn',
    width: 180
  },
  {
    title: '原订单号',
    dataIndex: 'flowNo',
    key: 'flowNo',
    width: 180
  },
  {
    title: '退款金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 100
  },
  {
    title: '退款类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '支付方式',
    dataIndex: 'payType',
    key: 'payType',
    width: 120
  },
  {
    title: '退款状态',
    dataIndex: 'refundStatus',
    key: 'refundStatus',
    width: 120
  },
  {
    title: '操作人',
    dataIndex: 'createName',
    key: 'createName',
    width: 100
  },
  {
    title: '申请时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 160
  },

]

// 获取支付方式名称
const getPayTypeName = (payType: number) => {
  const names: Record<number, string> = {
    1: '微信公众号',
    2: '小天才服务号',
    3: '线下支付',
    4: '小天才表端支付',
    5: '小天才H5支付',
    6: '华为支付'
  }
  return names[payType] || '未知'
}

// 获取支付方式颜色
const getPayTypeColor = (payType: number) => {
  const colors: Record<number, string> = {
    1: 'green',
    2: 'blue',
    3: 'orange',
    4: 'purple',
    5: 'cyan',
    6: 'red'
  }
  return colors[payType] || 'default'
}

// 获取退款状态名称
const getRefundStatusName = (status: number) => {
  const names: Record<number, string> = {
    0: '失败',
    1: '发起成功',
    2: '实际到账'
  }
  return names[status] || '未知'
}

// 获取退款状态颜色
const getRefundStatusColor = (status: number) => {
  const colors: Record<number, string> = {
    0: 'red',
    1: 'orange',
    2: 'green'
  }
  return colors[status] || 'default'
}

// 搜索退款
const searchRefunds = async () => {
  loading.value = true
  try {
    const params: any = {
      page: pagination.current,
      size: pagination.pageSize,
      payType: 1 // 必需参数，默认微信支付
    }

    // 只有选择了具体应用渠道才添加appCode参数
    if (searchForm.appCode && searchForm.appCode.trim()) {
      params.appCode = searchForm.appCode
    }

    // 只添加有值的搜索参数
    if (searchForm.refundSn?.trim()) params.refundSn = searchForm.refundSn.trim()
    if (searchForm.flowNo?.trim()) params.flowNo = searchForm.flowNo.trim()
    if (searchForm.startTime) params.startTime = searchForm.startTime
    if (searchForm.endTime) params.endTime = searchForm.endTime

    const response = await getRefundList(params)

    if (response.data) {
      // 根据接口文档，data直接是数组
      if (Array.isArray(response.data)) {
        refunds.value = response.data
        pagination.total = response.data.length
      } else if (response.data.list) {
        // 如果是分页格式
        refunds.value = response.data.list || []
        pagination.total = response.data.total || 0
      } else {
        refunds.value = []
        pagination.total = 0
      }
    }
  } catch (error) {
    console.error('获取退款列表失败:', error)
    message.error('获取退款列表失败')
    refunds.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    appCode: undefined,
    flowNo: undefined,
    refundSn: undefined,
    startTime: undefined,
    endTime: undefined,
    page: 1,
    size: 10
  })
  dateRange.value = null
  pagination.current = 1
  searchRefunds()
}

// 处理日期范围变化
const handleDateChange = (dates: [Dayjs, Dayjs] | null) => {
  handleDateRangeChange(dates, (startTime, endTime) => {
    searchForm.startTime = startTime
    searchForm.endTime = endTime
  })
}

// 处理表格变化
const handleTableChange: TableProps['onChange'] = (pag) => {
  pagination.current = pag.current || 1
  pagination.pageSize = pag.pageSize || 10
  searchRefunds()
}



// 显示导出弹窗
const showExportModal = () => {
  exportModalVisible.value = true
  // 重置导出表单
  exportForm.appCode = ''
  exportForm.type = 1
  exportDateRange.value = null
}

// 处理导出 - 超链接方式，无需token认证
const handleExport = async () => {
  try {
    // 验证必填项
    if (!exportDateRange.value || exportDateRange.value.length !== 2) {
      message.error('请选择退款时间范围')
      return
    }

    exportLoading.value = true

    // 格式化日期 - 根据接口文档，时间格式应该是 YYYY-MM-DD HH:mm:ss
    const startTime = exportDateRange.value[0].format('YYYY-MM-DD') + ' 00:00:00'
    const endTime = exportDateRange.value[1].format('YYYY-MM-DD') + ' 23:59:59'

    // 构建查询参数 - 根据接口文档的必填参数
    const params = new URLSearchParams({
      appCode: exportForm.appCode || '', // 应用编码，必填
      startTime, // 开始时间，必填
      endTime, // 结束时间，必填
      type: exportForm.type.toString() // 订单类型，必填
    })

    // 构建导出URL - 注意：退款导出接口路径是 /pasture/order/refund/export
    const exportUrl = `https://api.zj7hui.com/pasture/order/refund/export?${params.toString()}`

    console.log('退款导出URL:', exportUrl)

    // 创建a标签并直接跳转到服务器下载地址
    const link = document.createElement('a')
    link.href = exportUrl
    link.target = '_blank' // 在新窗口打开，避免影响当前页面

    // 添加到DOM，触发跳转，然后移除
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    message.success('正在导出退款数据，请稍候...')
    exportModalVisible.value = false
  } catch (error: any) {
    console.error('导出退款数据失败:', error)
    message.error('导出退款数据失败')
  } finally {
    exportLoading.value = false
  }
}

// 组件挂载时初始化
onMounted(async () => {
  // 等待应用列表加载完成后设置默认为"所有"
  const appStore = useAppStore()
  await appStore.initAppList()

  // 默认选择"所有"（空字符串）
  searchForm.appCode = ""
  console.log('RefundManagement设置默认为所有应用')
  searchRefunds()
})

// 组件卸载时清理数据，防止影响其他页面
onUnmounted(() => {
  // 清理数据
  refunds.value = []
  pagination.total = 0
  loading.value = false

  // 清理搜索表单
  Object.assign(searchForm, {
    appCode: undefined,
    flowNo: undefined,
    refundSn: undefined,
    startTime: undefined,
    endTime: undefined,
    page: 1,
    size: 10
  })
  dateRange.value = null

  console.log('退款管理页面已清理')
})
</script>

<style scoped>
.refund-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.search-card .ant-form-item {
  margin-bottom: 16px;
}

.export-link {
  text-decoration: none;
}

.export-link:hover {
  text-decoration: none;
}
</style>
