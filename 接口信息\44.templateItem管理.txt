templateItem新增

**接口地址** `/pasture/template/item/add`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| templateItem         |      templateItem   |     body        |       true      | com.qihui.pasture.model.TemplateItem   | com.qihui.pasture.model.TemplateItem     |
            



**schema属性说明**
  
**com.qihui.pasture.model.TemplateItem**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id  | id |   body    |   false   |int64  |       |
| name  | name名称 |   body    |   false   |string  |       |
| num  | num个数 |   body    |   false   |int32  |       |
| propId  | propId道具id |   body    |   false   |int64  |       |
| templateId  | templateId模板id |   body    |   false   |int64  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------------分割线
templateItem删除

**接口地址** `/pasture/template/item/delete`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------------分割线
templateItem获取详情

**接口地址** `/pasture/template/item/detail`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«com.qihui.pasture.model.TemplateItem»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    com.qihui.pasture.model.TemplateItem   |   com.qihui.pasture.model.TemplateItem    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.TemplateItem**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| id         |     id      |  int64   |      |
            | name         |     name名称      |  string   |      |
            | num         |     num个数      |  int32   |      |
            | propId         |     propId道具id      |  int64   |      |
            | templateId         |     templateId模板id      |  int64   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "id": 0,
        "name": "",
        "num": 0,
        "propId": 0,
        "templateId": 0
    },
    "message": "",
    "msg": ""
}
```



--------------分割线
templateItem获取列表

**接口地址** `/pasture/template/item/list`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| page         |      page   |     query        |       false      | integer   |      |
            | size         |      size   |     query        |       false      | integer   |      |
            | templateItem         |      templateItem   |     body        |       true      | com.qihui.pasture.model.TemplateItem   | com.qihui.pasture.model.TemplateItem     |
            



**schema属性说明**
  
**com.qihui.pasture.model.TemplateItem**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id  | id |   body    |   false   |int64  |       |
| name  | name名称 |   body    |   false   |string  |       |
| num  | num个数 |   body    |   false   |int32  |       |
| propId  | propId道具id |   body    |   false   |int64  |       |
| templateId  | templateId模板id |   body    |   false   |int64  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«com.qihui.pasture.model.TemplateItem»»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   com.qihui.pasture.model.TemplateItem    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.TemplateItem**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| id         |     id      |  int64   |      |
            | name         |     name名称      |  string   |      |
            | num         |     num个数      |  int32   |      |
            | propId         |     propId道具id      |  int64   |      |
            | templateId         |     templateId模板id      |  int64   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "id": 0,
            "name": "",
            "num": 0,
            "propId": 0,
            "templateId": 0
        }
    ],
    "message": "",
    "msg": ""
}
```



--------------分割线
templateItem更新

**接口地址** `/pasture/template/item/update`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| templateItem         |      templateItem   |     body        |       true      | com.qihui.pasture.model.TemplateItem   | com.qihui.pasture.model.TemplateItem     |
            



**schema属性说明**
  
**com.qihui.pasture.model.TemplateItem**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id  | id |   body    |   false   |int64  |       |
| name  | name名称 |   body    |   false   |string  |       |
| num  | num个数 |   body    |   false   |int32  |       |
| propId  | propId道具id |   body    |   false   |int64  |       |
| templateId  | templateId模板id |   body    |   false   |int64  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```


