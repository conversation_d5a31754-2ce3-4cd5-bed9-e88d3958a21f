#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const envPath = path.join(__dirname, '../.env.development')

function switchToProxy() {
  const content = `# 开发环境配置
NODE_ENV=development

# API配置
# 设置为 'proxy' 使用本地代理，设置为 'direct' 直接访问服务器API
VITE_API_MODE=proxy

# 直接访问服务器API的地址
VITE_API_BASE_URL=https://api.zj7hui.com/pasture

# 代理配置（当VITE_API_MODE=proxy时使用）
VITE_PROXY_TARGET=https://api.zj7hui.com`

  fs.writeFileSync(envPath, content)
  console.log('✅ 已切换到代理模式 (proxy)')
  console.log('📝 开发环境将使用 /pasture 代理到 https://api.zj7hui.com')
}

function switchToDirect() {
  const content = `# 开发环境配置
NODE_ENV=development

# API配置
# 设置为 'proxy' 使用本地代理，设置为 'direct' 直接访问服务器API
VITE_API_MODE=direct

# 直接访问服务器API的地址
VITE_API_BASE_URL=https://api.zj7hui.com/pasture

# 代理配置（当VITE_API_MODE=proxy时使用）
VITE_PROXY_TARGET=https://api.zj7hui.com`

  fs.writeFileSync(envPath, content)
  console.log('✅ 已切换到直接访问模式 (direct)')
  console.log('📝 开发环境将直接访问 https://api.zj7hui.com/pasture')
}

const mode = process.argv[2]

if (mode === 'proxy') {
  switchToProxy()
} else if (mode === 'direct') {
  switchToDirect()
} else {
  console.log('使用方法:')
  console.log('  npm run api:proxy  - 切换到代理模式')
  console.log('  npm run api:direct - 切换到直接访问模式')
  console.log('')
  console.log('当前配置:')
  if (fs.existsSync(envPath)) {
    const content = fs.readFileSync(envPath, 'utf8')
    const match = content.match(/VITE_API_MODE=(\w+)/)
    if (match) {
      console.log(`  API模式: ${match[1]}`)
    }
  }
}
