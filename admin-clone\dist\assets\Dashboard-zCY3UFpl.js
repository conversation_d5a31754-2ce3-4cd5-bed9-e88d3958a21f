import{d as a,f as e,c as s,o as t,b as l,a1 as n,a0 as r,U as u,a2 as i,k as c,_ as d,a3 as o,S as p,V as v,G as g,u as y,F as _,a4 as m,a5 as f}from"./vendor-Dhi-hotu.js";import{g as h}from"./statistics-KYvH5j6t.js";import{d as k,R as w}from"./antd-DSyIu0Jx.js";import{_ as T}from"./_plugin-vue_export-helper-xcdYWU4n.js";const x={class:"dashboard"},$={class:"page-header"},R={class:"header-actions"},b={key:0,class:"last-update",style:{"margin-left":"16px",color:"#666"}},z={key:0,class:"loading-container"},j={key:1,class:"apps-container"},I={class:"app-section"},A={class:"stat-content"},D={class:"stat-title"},F={class:"stat-value"},M={class:"stat-detail"},P={class:"app-section"},S={class:"stat-content"},C={class:"stat-title"},G={class:"stat-value"},L={class:"stat-detail"},U={class:"app-section"},V={class:"stat-content"},q={class:"stat-title"},B={class:"stat-value"},E={class:"stat-detail"},H=T(a({__name:"Dashboard",setup(a){const T=e(!1),H=e(""),J=e(null),K=e({pasture:[],pasture_zy:[],pasture_xm:[]}),N=["今日订单数","今日付款金额（元）","今日新增用户数"],O=s(()=>K.value.pasture.length>0||K.value.pasture_zy.length>0||K.value.pasture_xm.length>0),Q=async a=>{try{console.log(`加载 ${a} 的实时维度数据`);const e=await h(a);if(e.data&&Array.isArray(e.data)){const s=e.data.filter(a=>N.includes(a.name||""));return console.log(`${a} 数据加载成功，过滤后维度数量:`,s.length),s}return console.warn(`${a} API返回的数据为空`),[]}catch(e){return console.error(`加载 ${a} 维度数据失败:`,e),[]}},W=async()=>{T.value=!0;try{console.log("开始加载所有应用的实时维度数据");const[a,e,s]=await Promise.all([Q("pasture"),Q("pasture_zy"),Q("pasture_xm")]);K.value={pasture:a,pasture_zy:e,pasture_xm:s},H.value=(new Date).toLocaleTimeString(),console.log("所有应用数据加载完成:",K.value)}catch(a){console.error("加载应用数据失败:",a),k.error("加载数据失败")}finally{T.value=!1}},X=(a,e,s)=>{if(null==a)return"0";const t=Z(e,s);if(s){if(s.includes("金额")||s.includes("收入")||s.includes("营收")||s.includes("付费"))return`¥${a.toFixed(2)}`;if(s.includes("订单")||s.includes("用户")||s.includes("人数"))return`${Math.round(a)}${t}`}return`${Math.round(a)}${t}`},Y=a=>null==a?"0%":`${Math.abs(a).toFixed(2)}%`,Z=(a,e)=>{if(e){if(e.includes("用户")||e.includes("人数")||e.includes("新增用户"))return"人";if(e.includes("订单"))return"个"}switch(a){case 1:return"秒";case 2:return"个";default:return""}},aa=a=>1===a?"trend-up":"trend-down",ea=a=>1===a?"↑":"↓",sa=()=>{J.value&&(clearInterval(J.value),J.value=null)};return t(async()=>{console.log("Dashboard初始化，加载所有应用数据"),await W(),sa(),J.value=setInterval(()=>{W()},3e5)}),l(()=>{sa()}),(a,e)=>{const s=p("a-button"),t=p("a-spin"),l=p("a-card"),h=p("a-col"),k=p("a-row"),J=p("a-empty");return v(),n("div",x,[r("div",$,[e[1]||(e[1]=r("h2",null,"实时统计",-1)),r("div",R,[c(s,{type:"primary",onClick:W,loading:T.value},{icon:d(()=>[c(y(w))]),default:d(()=>[e[0]||(e[0]=g(" 刷新数据 ",-1))]),_:1,__:[0]},8,["loading"]),H.value?(v(),n("span",b," 最后更新："+o(H.value),1)):i("",!0)])]),T.value?(v(),n("div",z,[c(t,{size:"large",tip:"正在加载统计数据..."},{default:d(()=>e[2]||(e[2]=[r("div",{class:"loading-placeholder"},null,-1)])),_:1,__:[2]})])):(v(),n("div",j,[r("div",I,[e[5]||(e[5]=r("h3",{class:"app-title"},"神奇农场",-1)),c(k,{gutter:[16,16]},{default:d(()=>[(v(!0),n(_,null,m(K.value.pasture,a=>(v(),u(h,{xs:24,sm:8,lg:8,key:`pasture-${a.id}`},{default:d(()=>[c(l,{class:"stat-card"},{default:d(()=>[r("div",A,[r("div",D,o(a.name),1),r("div",F,o(X(a.total,a.unitType,a.name)),1),r("div",M,[r("div",null,[e[3]||(e[3]=g("较昨日同期： ",-1)),r("span",{class:f(aa(a.rangeType))},o(Y(a.range))+" "+o(ea(a.rangeType)),3)]),r("div",null,[e[4]||(e[4]=g("较上周同期： ",-1)),r("span",{class:f(aa(a.weekRangeType))},o(Y(a.weekRange))+" "+o(ea(a.weekRangeType)),3)])])])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),r("div",P,[e[8]||(e[8]=r("h3",{class:"app-title"},"神奇农场-掌育",-1)),c(k,{gutter:[16,16]},{default:d(()=>[(v(!0),n(_,null,m(K.value.pasture_zy,a=>(v(),u(h,{xs:24,sm:8,lg:8,key:`pasture_zy-${a.id}`},{default:d(()=>[c(l,{class:"stat-card"},{default:d(()=>[r("div",S,[r("div",C,o(a.name),1),r("div",G,o(X(a.total,a.unitType,a.name)),1),r("div",L,[r("div",null,[e[6]||(e[6]=g("较昨日同期： ",-1)),r("span",{class:f(aa(a.rangeType))},o(Y(a.range))+" "+o(ea(a.rangeType)),3)]),r("div",null,[e[7]||(e[7]=g("较上周同期： ",-1)),r("span",{class:f(aa(a.weekRangeType))},o(Y(a.weekRange))+" "+o(ea(a.weekRangeType)),3)])])])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),r("div",U,[e[11]||(e[11]=r("h3",{class:"app-title"},"神奇农场-小米",-1)),c(k,{gutter:[16,16]},{default:d(()=>[(v(!0),n(_,null,m(K.value.pasture_xm,a=>(v(),u(h,{xs:24,sm:8,lg:8,key:`pasture_xm-${a.id}`},{default:d(()=>[c(l,{class:"stat-card"},{default:d(()=>[r("div",V,[r("div",q,o(a.name),1),r("div",B,o(X(a.total,a.unitType,a.name)),1),r("div",E,[r("div",null,[e[9]||(e[9]=g("较昨日同期： ",-1)),r("span",{class:f(aa(a.rangeType))},o(Y(a.range))+" "+o(ea(a.rangeType)),3)]),r("div",null,[e[10]||(e[10]=g("较上周同期： ",-1)),r("span",{class:f(aa(a.weekRangeType))},o(Y(a.weekRange))+" "+o(ea(a.weekRangeType)),3)])])])]),_:2},1024)]),_:2},1024))),128))]),_:1})])])),T.value||O.value?i("",!0):(v(),u(J,{key:2,description:"暂无统计数据"}))])}}}),[["__scopeId","data-v-31b64e11"]]);export{H as default};
