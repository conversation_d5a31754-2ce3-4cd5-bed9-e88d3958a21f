<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f2f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #1890ff;
            text-align: center;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 基础连接测试</h1>
        
        <div class="status success">
            ✅ HTML页面加载成功
        </div>
        
        <div class="status success">
            ✅ CSS样式正常工作
        </div>
        
        <div id="js-test" class="status error">
            ❌ JavaScript测试中...
        </div>
        
        <div id="fetch-test" class="status error">
            ❌ 网络请求测试中...
        </div>
        
        <h3>📋 测试信息</h3>
        <ul>
            <li><strong>当前时间:</strong> <span id="current-time"></span></li>
            <li><strong>用户代理:</strong> <span id="user-agent"></span></li>
            <li><strong>页面地址:</strong> <span id="page-url"></span></li>
            <li><strong>本地存储:</strong> <span id="storage-test"></span></li>
        </ul>
        
        <h3>🌐 网络测试</h3>
        <button onclick="testAPI()" style="padding: 10px 20px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;">
            测试API连接
        </button>
        <div id="api-result" style="margin-top: 10px;"></div>
    </div>

    <script>
        console.log('🚀 JavaScript开始执行')
        
        // 测试JavaScript
        try {
            document.getElementById('js-test').className = 'status success'
            document.getElementById('js-test').innerHTML = '✅ JavaScript正常工作'
            console.log('✅ JavaScript测试通过')
        } catch (error) {
            console.error('❌ JavaScript测试失败:', error)
        }
        
        // 更新页面信息
        function updateInfo() {
            document.getElementById('current-time').textContent = new Date().toLocaleString()
            document.getElementById('user-agent').textContent = navigator.userAgent
            document.getElementById('page-url').textContent = window.location.href
            
            // 测试本地存储
            try {
                localStorage.setItem('test', 'ok')
                localStorage.removeItem('test')
                document.getElementById('storage-test').textContent = '✅ 正常'
            } catch (error) {
                document.getElementById('storage-test').textContent = '❌ 失败'
            }
        }
        
        // 测试网络请求
        async function testAPI() {
            const resultDiv = document.getElementById('api-result')
            resultDiv.innerHTML = '<div class="status">🔄 测试中...</div>'
            
            try {
                const response = await fetch('https://api.zj7hui.com/pasture/admin/user/list', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        page: 1,
                        size: 1
                    })
                })
                
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="status success">✅ API连接成功 (' + response.status + ')</div>'
                } else {
                    resultDiv.innerHTML = '<div class="status error">❌ API响应错误: ' + response.status + '</div>'
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="status error">❌ 网络错误: ' + error.message + '</div>'
            }
        }
        
        // 测试fetch功能
        try {
            if (typeof fetch !== 'undefined') {
                document.getElementById('fetch-test').className = 'status success'
                document.getElementById('fetch-test').innerHTML = '✅ 网络请求功能正常'
            } else {
                document.getElementById('fetch-test').innerHTML = '❌ 浏览器不支持fetch'
            }
        } catch (error) {
            document.getElementById('fetch-test').innerHTML = '❌ 网络功能测试失败'
        }
        
        // 初始化
        updateInfo()
        setInterval(updateInfo, 1000)
        
        console.log('✅ 测试页面初始化完成')
    </script>
</body>
</html>
