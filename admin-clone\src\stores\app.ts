import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getAppList } from '../api/auth'
import { message } from 'ant-design-vue'

// 应用渠道接口
export interface AppItem {
  code: string
  name: string
  [key: string]: any
}

export const useAppStore = defineStore('app', () => {
  // 状态
  const appList = ref<AppItem[]>([])
  const loading = ref(false)
  const initialized = ref(false)

  // 计算属性
  const appOptions = computed(() => {
    return appList.value.map(app => ({
      label: app.name,
      value: app.code
    }))
  })

  // 根据code获取应用名称
  const getAppName = computed(() => {
    return (code: string) => {
      const app = appList.value.find(item => item.code === code)
      return app?.name || code
    }
  })

  // 获取应用列表
  const fetchAppList = async () => {
    if (loading.value) return

    loading.value = true
    try {
      console.log('开始获取应用列表...')
      const response = await getAppList()
      console.log('应用列表API响应:', response)

      if (response.data) {
        let rawData = null

        // 如果API返回的是数组，直接使用
        if (Array.isArray(response.data)) {
          rawData = response.data
          console.log('使用数组格式的应用列表:', rawData)
        }
        // 如果API返回的是对象，可能需要从某个字段获取数组
        else if (response.data.list && Array.isArray(response.data.list)) {
          rawData = response.data.list
          console.log('使用对象.list格式的应用列表:', rawData)
        }
        // 如果没有数据，使用默认的应用列表
        else {
          console.warn('API返回的数据格式不符合预期，使用默认应用列表')
          console.warn('实际数据:', response.data)
          setDefaultAppList()
          return
        }

        // 处理应用数据，确保格式正确
        appList.value = rawData.map((item: any) => {
          console.log('处理应用项详细信息:', JSON.stringify(item, null, 2))

          // 如果item是字符串，说明只有名称，需要生成code
          if (typeof item === 'string') {
            // 根据实际的应用名称生成code
            let code = item
            if (item === '神奇农场') code = 'pasture'
            else if (item === '神奇农场-掌育') code = 'pasture_zy'
            else if (item === '神奇农场-小米') code = 'pasture_xm'
            else code = item.toLowerCase().replace(/[^a-z0-9]/g, '-')

            return { code, name: item }
          }

          // 如果item是对象，检查字段
          if (typeof item === 'object' && item !== null) {
            // 标准格式：{code: "xxx", name: "xxx"}
            if (item.code && item.name) {
              return { code: String(item.code), name: item.name }
            }
            // 其他可能的字段名
            if (item.appCode && item.name) {
              // 直接使用API返回的appCode
              return { code: String(item.appCode), name: item.name }
            }
            if (item.id && item.name) {
              // 根据实际API数据，直接使用appCode字段
              if (item.appCode) {
                return { code: String(item.appCode), name: item.name }
              }
              // 兜底：将数字ID转换为英文代码（已废弃，应该使用appCode）
              let code = String(item.id)
              if (code === '1') code = 'pasture'
              else if (code === '2') code = 'pasture_zy'
              else if (code === '3') code = 'pasture_xm'

              return { code, name: item.name }
            }
            // 如果只有name字段
            if (item.name) {
              return { code: item.name, name: item.name }
            }
          }

          // 兜底处理
          return { code: String(item), name: String(item) }
        })

        initialized.value = true
        console.log('应用列表最终结果:', appList.value)
        console.log('应用选项:', appOptions.value)
      } else {
        console.warn('API返回数据为空，使用默认应用列表')
        setDefaultAppList()
      }
    } catch (error) {
      console.error('获取应用列表失败:', error)
      message.error('获取应用列表失败，使用默认列表')
      setDefaultAppList()
    } finally {
      loading.value = false
    }
  }

  // 设置默认应用列表（作为备用方案）
  const setDefaultAppList = () => {
    appList.value = [
      { code: 'pasture', name: '神奇农场' },
      { code: 'pasture_zy', name: '神奇农场-掌育' },
      { code: 'pasture_xm', name: '神奇农场-小米' }
    ]
    initialized.value = true
  }

  // 初始化应用列表（如果还没有初始化）
  const initAppList = async () => {
    if (!initialized.value) {
      await fetchAppList()
    }
  }

  // 重置状态
  const reset = () => {
    appList.value = []
    loading.value = false
    initialized.value = false
  }

  return {
    // 状态
    appList,
    loading,
    initialized,
    
    // 计算属性
    appOptions,
    getAppName,
    
    // 方法
    fetchAppList,
    setDefaultAppList,
    initAppList,
    reset
  }
})
