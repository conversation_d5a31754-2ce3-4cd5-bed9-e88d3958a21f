const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./Layout-m7Ry2Y9D.js","./vendor-Dhi-hotu.js","./auth-Jnmp4-ZM.js","./auth-CDe9Kw3v.js","./_plugin-vue_export-helper-xcdYWU4n.js","./antd-DSyIu0Jx.js","./Layout-CMM9OQLZ.css","./Dashboard-zCY3UFpl.js","./statistics-KYvH5j6t.js","./Dashboard-C2YcKUO_.css","./UserManagement-BsdMsLBG.js","./app-C5BK_7TP.js","./UserManagement-CE--A6GW.css","./OrderManagement-VTvGvY_2.js","./AppSelector-CdXIhBuS.js","./AppSelector-tn0RQdqM.css","./dateFormat-DmnmjEpg.js","./OrderManagement-C6s9u1ZX.css","./RefundManagement-DfOxlg-E.js","./RefundManagement-D1CYbYFH.css","./StatisticsBoard-Dq9D7bIs.js","./StatisticsBoard-Du6UYBo5.css","./Login-B5wf1gyj.js","./Login-CJJeGLyz.css"])))=>i.map(i=>d[i]);
import{d as e,S as t,U as r,V as o,W as n,X as s,Y as a,R as i}from"./vendor-Dhi-hotu.js";import{A as l}from"./antd-DSyIu0Jx.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const c={},m=function(e,t,r){let o=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map(e=>Promise.resolve(e).then(e=>({status:"fulfilled",value:e}),e=>({status:"rejected",reason:e}))))};const n=document.getElementsByTagName("link"),s=document.querySelector("meta[property=csp-nonce]"),a=s?.nonce||s?.getAttribute("nonce");o=e(t.map(e=>{if(e=function(e,t){return new URL(e,t).href}(e,r),e in c)return;c[e]=!0;const t=e.endsWith(".css"),o=t?'[rel="stylesheet"]':"";if(!!r)for(let r=n.length-1;r>=0;r--){const o=n[r];if(o.href===e&&(!t||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${e}"]${o}`))return;const s=document.createElement("link");return s.rel=t?"stylesheet":"modulepreload",t||(s.as="script"),s.crossOrigin="",s.href=e,a&&s.setAttribute("nonce",a),document.head.appendChild(s),t?new Promise((t,r)=>{s.addEventListener("load",t),s.addEventListener("error",()=>r(new Error(`Unable to preload CSS for ${e}`)))}):void 0}))}function n(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return o.then(t=>{for(const e of t||[])"rejected"===e.status&&n(e.reason);return e().catch(n)})},u=e({__name:"App",setup:e=>(console.log("🚀 管理后台应用启动..."),(e,n)=>{const s=t("router-view");return o(),r(s)})});console.log("🚀 开始加载管理后台应用...");const d=n({history:s(),routes:[{path:"/",component:()=>m(()=>import("./Layout-m7Ry2Y9D.js"),__vite__mapDeps([0,1,2,3,4,5,6]),import.meta.url),redirect:"/dashboard",children:[{path:"dashboard",name:"Dashboard",component:()=>m(()=>import("./Dashboard-zCY3UFpl.js"),__vite__mapDeps([7,1,8,4,5,9]),import.meta.url),meta:{title:"实时统计"}},{path:"users",name:"UserManagement",component:()=>m(()=>import("./UserManagement-BsdMsLBG.js"),__vite__mapDeps([10,1,4,11,3,5,12]),import.meta.url),meta:{title:"用户管理"}},{path:"orders",name:"OrderManagement",component:()=>m(()=>import("./OrderManagement-VTvGvY_2.js"),__vite__mapDeps([13,1,5,14,4,11,3,15,16,17]),import.meta.url),meta:{title:"订单管理"}},{path:"refunds",name:"RefundManagement",component:()=>m(()=>import("./RefundManagement-DfOxlg-E.js"),__vite__mapDeps([18,1,5,14,4,11,3,15,16,19]),import.meta.url),meta:{title:"退款管理"}},{path:"statistics",name:"StatisticsBoard",component:()=>m(()=>import("./StatisticsBoard-Dq9D7bIs.js"),__vite__mapDeps([20,1,8,4,16,5,21]),import.meta.url),meta:{title:"数据看板"}}]},{path:"/login",name:"Login",component:()=>m(()=>import("./Login-B5wf1gyj.js"),__vite__mapDeps([22,1,2,3,4,5,23]),import.meta.url)}]}),p=a(),f=i(u);f.use(d),f.use(p),f.use(l),console.log("🎯 挂载管理后台应用..."),f.mount("#app"),console.log("🎉 管理后台应用启动成功！");export{m as _};
