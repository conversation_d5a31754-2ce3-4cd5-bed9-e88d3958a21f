import{d as a,f as s,r as e,a1 as l,a8 as i,a0 as t,k as d,u as n,_ as r,S as o,$ as u,V as c,G as p,a3 as v}from"./vendor-Dhi-hotu.js";import{u as g}from"./auth-Jnmp4-ZM.js";import{H as m,C as f,U as b,g as h}from"./antd-DSyIu0Jx.js";import{_}from"./_plugin-vue_export-helper-xcdYWU4n.js";import"./auth-CDe9Kw3v.js";const w={class:"login-container"},y={class:"login-left"},j={class:"brand-info"},x={class:"brand-icon"},k={class:"feature-list"},z={class:"feature-item"},U={class:"feature-item"},q={class:"feature-item"},C={class:"login-right"},F={class:"login-box"},G=_(a({__name:"Login",setup(a){const _=u(),G=g(),H=s(!1),I=e({account:"admin",pwd:"123456"}),L={account:[{required:!0,message:"请输入账号",trigger:"blur"}],pwd:[{required:!0,message:"请输入密码",trigger:"blur"}]},M=async()=>{H.value=!0;try{await G.login(I)&&(await G.getMenu(),_.push("/"))}finally{H.value=!1}};return(a,s)=>{const e=o("a-input"),u=o("a-form-item"),g=o("a-input-password"),_=o("a-button"),G=o("a-form");return c(),l("div",w,[s[9]||(s[9]=i('<div class="background-decoration" data-v-611c045d><div class="bg-shape shape-1" data-v-611c045d></div><div class="bg-shape shape-2" data-v-611c045d></div><div class="bg-shape shape-3" data-v-611c045d></div><div class="bg-shape shape-4" data-v-611c045d></div></div>',1)),t("div",y,[t("div",j,[t("div",x,[d(n(m))]),s[5]||(s[5]=t("h1",{class:"brand-title"},"管理后台",-1)),s[6]||(s[6]=t("p",{class:"brand-subtitle"},"智能化管理平台",-1)),t("div",k,[t("div",z,[d(n(f)),s[2]||(s[2]=t("span",null,"实时监控状态",-1))]),t("div",U,[d(n(f)),s[3]||(s[3]=t("span",null,"智能数据分析",-1))]),t("div",q,[d(n(f)),s[4]||(s[4]=t("span",null,"高效管理流程",-1))])])])]),t("div",C,[t("div",F,[s[7]||(s[7]=t("div",{class:"login-header"},[t("h2",null,"欢迎回来"),t("p",null,"请输入您的账号和密码登录系统")],-1)),d(G,{model:I,rules:L,onFinish:M,layout:"vertical",class:"login-form"},{default:r(()=>[d(u,{label:"账号",name:"account"},{default:r(()=>[d(e,{value:I.account,"onUpdate:value":s[0]||(s[0]=a=>I.account=a),placeholder:"请输入账号",size:"large"},{prefix:r(()=>[d(n(b))]),_:1},8,["value"])]),_:1}),d(u,{label:"密码",name:"pwd"},{default:r(()=>[d(g,{value:I.pwd,"onUpdate:value":s[1]||(s[1]=a=>I.pwd=a),placeholder:"请输入密码",size:"large"},{prefix:r(()=>[d(n(h))]),_:1},8,["value"])]),_:1}),d(u,null,{default:r(()=>[d(_,{type:"primary","html-type":"submit",size:"large",block:"",loading:H.value,class:"login-button"},{default:r(()=>[p(v(H.value?"登录中...":"立即登录"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"]),s[8]||(s[8]=t("div",{class:"login-footer"},[t("p",null,"© 2025 管理后台 - 专业的数字化解决方案")],-1))])])])}}}),[["__scopeId","data-v-611c045d"]]);export{G as default};
