Goods获取详情

**接口地址** `/pasture/goods/detail`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«com.qimingxing.journey.model.Goods»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    com.qimingxing.journey.model.Goods   |   com.qimingxing.journey.model.Goods    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qimingxing.journey.model.Goods**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| amount         |     amount定价      |  number   |      |
            | appCode         |     appCode应用编号      |  string   |      |
            | avgPriceIntro         |     avgPriceIntro均价说明      |  string   |      |
            | bigPic         |     bigPic      |  string   |      |
            | createTime         |     createTime      |  date-time   |      |
            | detailPic         |     detailPic详情页地址列表       |  string   |      |
            | discountAmount         |     discountAmount折扣价      |  number   |      |
            | dropId         |     dropId      |  int64   |      |
            | effectiveValue         |     effectiveValue会员有效时间      |  int32   |      |
            | id         |     id      |  int32   |      |
            | isRecommend         |     isRecommend是否推荐商品      |  int32   |      |
            | lastBayTime         |     lastBayTime有效期      |  date-time   |      |
            | memberType         |     memberType类型 year 年会员 month 月会员 season 季会员 perpetual 永久会员      |  string   |      |
            | memo         |     memo备注      |  string   |      |
            | name         |     name商品名称      |  string   |      |
            | outProductId         |     outProductId第三方商品id      |  string   |      |
            | pic         |     pic前端样式表      |  string   |      |
            | seq         |     seq      |  int32   |      |
            | status         |     status状态 0未启用 1正常 2停用      |  int32   |      |
            | updateTime         |     updateTime      |  date-time   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "amount": 0,
        "appCode": "",
        "avgPriceIntro": "",
        "bigPic": "",
        "createTime": "",
        "detailPic": "",
        "discountAmount": 0,
        "dropId": 0,
        "effectiveValue": 0,
        "id": 0,
        "isRecommend": 0,
        "lastBayTime": "",
        "memberType": "",
        "memo": "",
        "name": "",
        "outProductId": "",
        "pic": "",
        "seq": 0,
        "status": 0,
        "updateTime": ""
    },
    "message": "",
    "msg": ""
}
```



--------分割线
获取会员商品列表

**接口地址** `/pasture/goods/list`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| page         |      page   |     body        |       true      | 分页参数   | 分页参数     |
            



**schema属性说明**
  
**分页参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| page  | 当前页 |   body    |   true   |int32  |       |
| playerId  | 玩家id |   body    |   true   |int64  |       |
| size  | 每页大小 |   body    |   true   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«MemberGoods»»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   MemberGoods    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**MemberGoods**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| amount         |     amount定价      |  number   |      |
            | appCode         |     appCode应用编号      |  string   |      |
            | avgPriceIntro         |     说明折合xx/月      |  string   |      |
            | bigPic         |     bigPic大图      |  string   |      |
            | buyStatus         |     购买状态 1未购买 2已购买      |  int32   |      |
            | couponAwardStatus         |     优惠券奖励状态 1未领奖 2已领奖      |  int32   |      |
            | couponId         |     couponId优惠券id      |  int32   |      |
            | couponName         |     优惠券名称      |  string   |      |
            | couponTime         |     lastBayTime有效期      |  date-time   |      |
            | createTime         |     createTime      |  date-time   |      |
            | detailPic         |     detailPic详情页地址列表       |  string   |      |
            | discountAmount         |     discountAmount折扣价      |  number   |      |
            | effectiveValue         |     effectiveValue会员有效时间      |  int32   |      |
            | hasStikynot         |     是否有便签标记      |  int32   |      |
            | id         |     id      |  int32   |      |
            | introList         |     商品详情介绍图片列表      |  array   |      |
            | isRecommend         |     是否推荐商品      |  int32   |      |
            | lastBayTime         |     lastBayTime有效期      |  date-time   |      |
            | memberType         |     类型 year 年会员 month 月会员 season 季会员 gift 礼物 trial 体验课  course 课程包 perpetual 永久会员      |  string   |      |
            | memo         |     memo备注      |  string   |      |
            | name         |     name商品名称      |  string   |      |
            | otherProductId         |     第三方商品id      |  string   |      |
            | pic         |     pic商品图片      |  string   |      |
            | sortNum         |     排序      |  int32   |      |
            | status         |     status状态 0未启用 1正常 2停用      |  int32   |      |
            | styles         |     前端样式      |  array   |      |
            | updateTime         |     updateTime      |  date-time   |      |
            | userType         |     用户类型 0 1 2      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "amount": 0,
            "appCode": "",
            "avgPriceIntro": "",
            "bigPic": "",
            "buyStatus": 0,
            "couponAwardStatus": 0,
            "couponId": 0,
            "couponName": "",
            "couponTime": "",
            "createTime": "",
            "detailPic": "",
            "discountAmount": 0,
            "effectiveValue": 0,
            "hasStikynot": 0,
            "id": 0,
            "introList": [],
            "isRecommend": 0,
            "lastBayTime": "",
            "memberType": "",
            "memo": "",
            "name": "",
            "otherProductId": "",
            "pic": "",
            "sortNum": 0,
            "status": 0,
            "styles": [],
            "updateTime": "",
            "userType": 0
        }
    ],
    "message": "",
    "msg": ""
}
```


