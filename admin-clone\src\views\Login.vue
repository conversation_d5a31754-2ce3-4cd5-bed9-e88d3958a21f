<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="bg-shape shape-1"></div>
      <div class="bg-shape shape-2"></div>
      <div class="bg-shape shape-3"></div>
      <div class="bg-shape shape-4"></div>
    </div>

    <!-- 左侧信息区域 -->
    <div class="login-left">
      <div class="brand-info">
        <div class="brand-icon">
          <HomeOutlined />
        </div>
        <h1 class="brand-title">管理后台</h1>
        <p class="brand-subtitle">智能化管理平台</p>
        <div class="feature-list">
          <div class="feature-item">
            <CheckCircleOutlined />
            <span>实时监控状态</span>
          </div>
          <div class="feature-item">
            <CheckCircleOutlined />
            <span>智能数据分析</span>
          </div>
          <div class="feature-item">
            <CheckCircleOutlined />
            <span>高效管理流程</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧登录区域 -->
    <div class="login-right">
      <div class="login-box">
        <div class="login-header">
          <h2>欢迎回来</h2>
          <p>请输入您的账号和密码登录系统</p>
        </div>

        <a-form
          :model="loginForm"
          :rules="rules"
          @finish="handleLogin"
          layout="vertical"
          class="login-form"
        >
          <a-form-item label="账号" name="account">
            <a-input
              v-model:value="loginForm.account"
              placeholder="请输入账号"
              size="large"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item label="密码" name="pwd">
            <a-input-password
              v-model:value="loginForm.pwd"
              placeholder="请输入密码"
              size="large"
            >
              <template #prefix>
                <LockOutlined />
              </template>
            </a-input-password>
          </a-form-item>

          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              size="large"
              block
              :loading="loading"
              class="login-button"
            >
              {{ loading ? '登录中...' : '立即登录' }}
            </a-button>
          </a-form-item>
        </a-form>

        <div class="login-footer">
          <p>© 2025 管理后台 - 专业的数字化解决方案</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { UserOutlined, LockOutlined, HomeOutlined, CheckCircleOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useAuthStore } from '../stores/auth'
import type { LoginParams } from '../api/auth'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)

const loginForm = reactive<LoginParams>({
  account: 'admin',
  pwd: '123456'
})

const rules = {
  account: [
    { required: true, message: '请输入账号', trigger: 'blur' }
  ],
  pwd: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}



const handleLogin = async () => {
  loading.value = true
  try {
    const success = await authStore.login(loginForm)
    if (success) {
      await authStore.getMenu()
      router.push('/')
    }
  } finally {
    loading.value = false
  }
}


</script>

<style scoped>
.login-container {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.bg-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 80px;
  height: 80px;
  top: 30%;
  right: 40%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 左侧信息区域 */
.login-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px;
  position: relative;
  z-index: 2;
}

.brand-info {
  text-align: center;
  color: white;
  max-width: 500px;
}

.brand-icon {
  font-size: 80px;
  margin-bottom: 30px;
  opacity: 0.9;
}

.brand-title {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.brand-subtitle {
  font-size: 20px;
  margin-bottom: 40px;
  opacity: 0.9;
  font-weight: 300;
}

.feature-list {
  text-align: left;
  max-width: 300px;
  margin: 0 auto;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  opacity: 0.9;
}

.feature-item .anticon {
  margin-right: 12px;
  font-size: 18px;
  color: #52c41a;
}

/* 右侧登录区域 */
.login-right {
  width: 500px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.login-box {
  width: 100%;
  max-width: 400px;
  padding: 60px 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-header h2 {
  color: #333;
  margin-bottom: 12px;
  font-size: 32px;
  font-weight: 700;
}

.login-header p {
  color: #666;
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}



.login-form {
  margin-top: 30px;
}

.login-form .ant-form-item-label > label {
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.login-form .ant-input-affix-wrapper,
.login-form .ant-input {
  height: 50px;
  border-radius: 8px;
  border: 2px solid #e8e8e8;
  transition: all 0.3s ease;
}

.login-form .ant-input-affix-wrapper:hover,
.login-form .ant-input:hover {
  border-color: #667eea;
}

.login-form .ant-input-affix-wrapper:focus,
.login-form .ant-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.login-button {
  height: 50px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  margin-top: 20px;
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.login-footer {
  text-align: center;
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid #e8e8e8;
}

.login-footer p {
  color: #999;
  font-size: 14px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .login-left {
    display: none;
  }

  .login-right {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .login-box {
    padding: 40px 20px;
  }

  .login-header h2 {
    font-size: 28px;
  }

  .login-header p {
    font-size: 14px;
  }
}
</style>
