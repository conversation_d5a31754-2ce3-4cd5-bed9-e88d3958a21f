playerEventOptRecord新增

**接口地址** `/pasture/player/event/opt/record/add`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerEventOptRecord         |      playerEventOptRecord   |     body        |       true      | com.qihui.pasture.model.PlayerEventOptRecord   | com.qihui.pasture.model.PlayerEventOptRecord     |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerEventOptRecord**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| createTime  | createTime |   body    |   false   |date-time  |       |
| eventType  | eventType事件类型 |   body    |   false   |int32  |       |
| eventVal  | eventVal事件值 |   body    |   false   |int32  |       |
| id  | id |   body    |   false   |int64  |       |
| playerEventId  | playerEventId玩家事件id |   body    |   false   |int64  |       |
| playerId  | playerId |   body    |   false   |int64  |       |
| visualAngle  | visualAngle操作视角 1-玩家 2-好友 |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------------------分割线
playerEventOptRecord删除

**接口地址** `/pasture/player/event/opt/record/delete`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------------------分割线
playerEventOptRecord获取详情

**接口地址** `/pasture/player/event/opt/record/detail`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«com.qihui.pasture.model.PlayerEventOptRecord»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    com.qihui.pasture.model.PlayerEventOptRecord   |   com.qihui.pasture.model.PlayerEventOptRecord    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerEventOptRecord**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| createTime         |     createTime      |  date-time   |      |
            | eventType         |     eventType事件类型      |  int32   |      |
            | eventVal         |     eventVal事件值      |  int32   |      |
            | id         |     id      |  int64   |      |
            | playerEventId         |     playerEventId玩家事件id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            | visualAngle         |     visualAngle操作视角 1-玩家 2-好友      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "createTime": "",
        "eventType": 0,
        "eventVal": 0,
        "id": 0,
        "playerEventId": 0,
        "playerId": 0,
        "visualAngle": 0
    },
    "message": "",
    "msg": ""
}
```



--------------------分割线
playerEventOptRecord获取列表

**接口地址** `/pasture/player/event/opt/record/list`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| page         |      page   |     query        |       false      | integer   |      |
            | playerEventOptRecord         |      playerEventOptRecord   |     body        |       true      | com.qihui.pasture.model.PlayerEventOptRecord   | com.qihui.pasture.model.PlayerEventOptRecord     |
            | size         |      size   |     query        |       false      | integer   |      |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerEventOptRecord**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| createTime  | createTime |   body    |   false   |date-time  |       |
| eventType  | eventType事件类型 |   body    |   false   |int32  |       |
| eventVal  | eventVal事件值 |   body    |   false   |int32  |       |
| id  | id |   body    |   false   |int64  |       |
| playerEventId  | playerEventId玩家事件id |   body    |   false   |int64  |       |
| playerId  | playerId |   body    |   false   |int64  |       |
| visualAngle  | visualAngle操作视角 1-玩家 2-好友 |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«com.qihui.pasture.model.PlayerEventOptRecord»»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   com.qihui.pasture.model.PlayerEventOptRecord    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerEventOptRecord**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| createTime         |     createTime      |  date-time   |      |
            | eventType         |     eventType事件类型      |  int32   |      |
            | eventVal         |     eventVal事件值      |  int32   |      |
            | id         |     id      |  int64   |      |
            | playerEventId         |     playerEventId玩家事件id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            | visualAngle         |     visualAngle操作视角 1-玩家 2-好友      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "createTime": "",
            "eventType": 0,
            "eventVal": 0,
            "id": 0,
            "playerEventId": 0,
            "playerId": 0,
            "visualAngle": 0
        }
    ],
    "message": "",
    "msg": ""
}
```


	
--------------------分割线
playerEventOptRecord更新

**接口地址** `/pasture/player/event/opt/record/update`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerEventOptRecord         |      playerEventOptRecord   |     body        |       true      | com.qihui.pasture.model.PlayerEventOptRecord   | com.qihui.pasture.model.PlayerEventOptRecord     |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerEventOptRecord**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| createTime  | createTime |   body    |   false   |date-time  |       |
| eventType  | eventType事件类型 |   body    |   false   |int32  |       |
| eventVal  | eventVal事件值 |   body    |   false   |int32  |       |
| id  | id |   body    |   false   |int64  |       |
| playerEventId  | playerEventId玩家事件id |   body    |   false   |int64  |       |
| playerId  | playerId |   body    |   false   |int64  |       |
| visualAngle  | visualAngle操作视角 1-玩家 2-好友 |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```


