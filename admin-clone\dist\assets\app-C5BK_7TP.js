import{a7 as e,f as a,c as n}from"./vendor-Dhi-hotu.js";import{g as t}from"./auth-CDe9Kw3v.js";import{d as o}from"./antd-DSyIu0Jx.js";const r=e("app",()=>{const e=a([]),r=a(!1),l=a(!1),s=n(()=>e.value.map(e=>({label:e.name,value:e.code}))),i=n(()=>a=>{const n=e.value.find(e=>e.code===a);return n?.name||a}),p=async()=>{if(!r.value){r.value=!0;try{console.log("开始获取应用列表...");const a=await t();if(console.log("应用列表API响应:",a),a.data){let n=null;if(Array.isArray(a.data))n=a.data,console.log("使用数组格式的应用列表:",n);else{if(!a.data.list||!Array.isArray(a.data.list))return console.warn("API返回的数据格式不符合预期，使用默认应用列表"),console.warn("实际数据:",a.data),void u();n=a.data.list,console.log("使用对象.list格式的应用列表:",n)}e.value=n.map(e=>{if(console.log("处理应用项详细信息:",JSON.stringify(e,null,2)),"string"==typeof e){let a=e;return a="神奇农场"===e?"pasture":"神奇农场-掌育"===e?"pasture_zy":"神奇农场-小米"===e?"pasture_xm":e.toLowerCase().replace(/[^a-z0-9]/g,"-"),{code:a,name:e}}if("object"==typeof e&&null!==e){if(e.code&&e.name)return{code:String(e.code),name:e.name};if(e.appCode&&e.name)return{code:String(e.appCode),name:e.name};if(e.id&&e.name){if(e.appCode)return{code:String(e.appCode),name:e.name};let a=String(e.id);return"1"===a?a="pasture":"2"===a?a="pasture_zy":"3"===a&&(a="pasture_xm"),{code:a,name:e.name}}if(e.name)return{code:e.name,name:e.name}}return{code:String(e),name:String(e)}}),l.value=!0,console.log("应用列表最终结果:",e.value),console.log("应用选项:",s.value)}else console.warn("API返回数据为空，使用默认应用列表"),u()}catch(a){console.error("获取应用列表失败:",a),o.error("获取应用列表失败，使用默认列表"),u()}finally{r.value=!1}}},u=()=>{e.value=[{code:"pasture",name:"神奇农场"},{code:"pasture_zy",name:"神奇农场-掌育"},{code:"pasture_xm",name:"神奇农场-小米"}],l.value=!0};return{appList:e,loading:r,initialized:l,appOptions:s,getAppName:i,fetchAppList:p,setDefaultAppList:u,initAppList:async()=>{l.value||await p()},reset:()=>{e.value=[],r.value=!1,l.value=!1}}});export{r as u};
