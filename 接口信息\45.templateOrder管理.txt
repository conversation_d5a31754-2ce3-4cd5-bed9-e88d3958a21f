templateOrder新增

**接口地址** `/pasture/template/order/add`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| templateOrder         |      templateOrder   |     body        |       true      | com.qihui.pasture.model.TemplateOrder   | com.qihui.pasture.model.TemplateOrder     |
            



**schema属性说明**
  
**com.qihui.pasture.model.TemplateOrder**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| appCode  | appCode应用编号 |   body    |   false   |string  |       |
| createTime  | createTime创建时间 |   body    |   false   |date-time  |       |
| groupId  | groupId分组 |   body    |   false   |int32  |       |
| id  | id |   body    |   false   |int64  |       |
| level  | level任务等级 1高级 2中级 3低级 |   body    |   false   |int32  |       |
| maxPlayerLevel  | maxPlayerLevel最大用户等级 |   body    |   false   |int32  |       |
| minPlayerLevel  | minPlayerLevel最小用户等级 |   body    |   false   |int32  |       |
| status  | status状态 1启用 2禁用 |   body    |   false   |int32  |       |
| updateTime  | updateTime更新时间 |   body    |   false   |date-time  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------------分割线
templateOrder删除

**接口地址** `/pasture/template/order/delete`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------------分割线
templateOrder获取详情

**接口地址** `/pasture/template/order/detail`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«com.qihui.pasture.model.TemplateOrder»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    com.qihui.pasture.model.TemplateOrder   |   com.qihui.pasture.model.TemplateOrder    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.TemplateOrder**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| appCode         |     appCode应用编号      |  string   |      |
            | createTime         |     createTime创建时间      |  date-time   |      |
            | groupId         |     groupId分组      |  int32   |      |
            | id         |     id      |  int64   |      |
            | level         |     level任务等级 1高级 2中级 3低级      |  int32   |      |
            | maxPlayerLevel         |     maxPlayerLevel最大用户等级      |  int32   |      |
            | minPlayerLevel         |     minPlayerLevel最小用户等级      |  int32   |      |
            | status         |     status状态 1启用 2禁用      |  int32   |      |
            | updateTime         |     updateTime更新时间      |  date-time   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "appCode": "",
        "createTime": "",
        "groupId": 0,
        "id": 0,
        "level": 0,
        "maxPlayerLevel": 0,
        "minPlayerLevel": 0,
        "status": 0,
        "updateTime": ""
    },
    "message": "",
    "msg": ""
}
```



--------------分割线
templateOrder获取列表

**接口地址** `/pasture/template/order/list`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| page         |      page   |     query        |       false      | integer   |      |
            | size         |      size   |     query        |       false      | integer   |      |
            | templateOrder         |      templateOrder   |     body        |       true      | com.qihui.pasture.model.TemplateOrder   | com.qihui.pasture.model.TemplateOrder     |
            



**schema属性说明**
  
**com.qihui.pasture.model.TemplateOrder**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| appCode  | appCode应用编号 |   body    |   false   |string  |       |
| createTime  | createTime创建时间 |   body    |   false   |date-time  |       |
| groupId  | groupId分组 |   body    |   false   |int32  |       |
| id  | id |   body    |   false   |int64  |       |
| level  | level任务等级 1高级 2中级 3低级 |   body    |   false   |int32  |       |
| maxPlayerLevel  | maxPlayerLevel最大用户等级 |   body    |   false   |int32  |       |
| minPlayerLevel  | minPlayerLevel最小用户等级 |   body    |   false   |int32  |       |
| status  | status状态 1启用 2禁用 |   body    |   false   |int32  |       |
| updateTime  | updateTime更新时间 |   body    |   false   |date-time  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«com.qihui.pasture.model.TemplateOrder»»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   com.qihui.pasture.model.TemplateOrder    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.TemplateOrder**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| appCode         |     appCode应用编号      |  string   |      |
            | createTime         |     createTime创建时间      |  date-time   |      |
            | groupId         |     groupId分组      |  int32   |      |
            | id         |     id      |  int64   |      |
            | level         |     level任务等级 1高级 2中级 3低级      |  int32   |      |
            | maxPlayerLevel         |     maxPlayerLevel最大用户等级      |  int32   |      |
            | minPlayerLevel         |     minPlayerLevel最小用户等级      |  int32   |      |
            | status         |     status状态 1启用 2禁用      |  int32   |      |
            | updateTime         |     updateTime更新时间      |  date-time   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "appCode": "",
            "createTime": "",
            "groupId": 0,
            "id": 0,
            "level": 0,
            "maxPlayerLevel": 0,
            "minPlayerLevel": 0,
            "status": 0,
            "updateTime": ""
        }
    ],
    "message": "",
    "msg": ""
}
```



--------------分割线
templateOrder更新

**接口地址** `/pasture/template/order/update`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| templateOrder         |      templateOrder   |     body        |       true      | com.qihui.pasture.model.TemplateOrder   | com.qihui.pasture.model.TemplateOrder     |
            



**schema属性说明**
  
**com.qihui.pasture.model.TemplateOrder**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| appCode  | appCode应用编号 |   body    |   false   |string  |       |
| createTime  | createTime创建时间 |   body    |   false   |date-time  |       |
| groupId  | groupId分组 |   body    |   false   |int32  |       |
| id  | id |   body    |   false   |int64  |       |
| level  | level任务等级 1高级 2中级 3低级 |   body    |   false   |int32  |       |
| maxPlayerLevel  | maxPlayerLevel最大用户等级 |   body    |   false   |int32  |       |
| minPlayerLevel  | minPlayerLevel最小用户等级 |   body    |   false   |int32  |       |
| status  | status状态 1启用 2禁用 |   body    |   false   |int32  |       |
| updateTime  | updateTime更新时间 |   body    |   false   |date-time  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```


