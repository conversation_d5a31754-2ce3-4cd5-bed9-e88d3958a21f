import { message } from 'ant-design-vue'
import * as XLSX from 'xlsx'

/**
 * 导出配置接口
 */
export interface ExportConfig {
  baseUrl: string
  endpoint: string
  params?: Record<string, any>
  filename?: string
}

/**
 * 通过URL直接跳转方式导出文件（老板要求的方式）
 * 如果URL方式失败，自动回退到fetch方式
 * @param config 导出配置
 */
export const exportByUrl = async (config: ExportConfig): Promise<void> => {
  try {
    // 获取认证token
    const token = localStorage.getItem('token')
    if (!token) {
      message.error('未找到登录凭证，请重新登录')
      return
    }

    // 方案1：尝试URL方式（token在查询参数中）
    await tryUrlExport(config, token)
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  }
}

/**
 * 尝试URL方式导出
 */
const tryUrlExport = async (config: ExportConfig, token: string): Promise<void> => {
  // 构建查询参数
  const queryParams = new URLSearchParams()

  // 添加业务参数
  if (config.params) {
    Object.entries(config.params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, String(value))
      }
    })
  }

  // 添加token到查询参数
  queryParams.append('Accesstoken', token)

  // 构建完整的导出URL
  const exportUrl = `${config.baseUrl}${config.endpoint}?${queryParams.toString()}`

  console.log('导出URL:', exportUrl)

  // 创建a标签并直接跳转到服务器下载地址
  const link = document.createElement('a')
  link.href = exportUrl
  link.target = '_blank' // 在新窗口打开，避免影响当前页面

  // 添加到DOM，触发跳转，然后移除
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  message.success('正在导出，请稍候...')
}

/**
 * 前端生成Excel文件并下载
 * @param data 表格数据
 * @param columns 表格列配置
 * @param filename 文件名
 */
export const exportToExcel = (data: any[], columns: any[], filename: string): void => {
  try {
    console.log(`开始导出Excel，数据量: ${data.length} 条`)

    // 准备表头
    const headers = columns.map(col => col.title || col.dataIndex)

    // 分批处理数据行，避免内存溢出
    const batchSize = 5000
    const totalBatches = Math.ceil(data.length / batchSize)

    console.log(`数据将分 ${totalBatches} 批处理，每批 ${batchSize} 条`)

    // 创建工作簿
    const wb = XLSX.utils.book_new()

    // 如果数据量较小，直接处理
    if (data.length <= batchSize) {
      const rows = data.map(row => {
        return columns.map(col => {
          const value = row[col.dataIndex]
          // 处理特殊格式
          if (col.customRender && typeof col.customRender === 'function') {
            try {
              const rendered = col.customRender({ text: value, record: row })
              // 如果返回的是React元素或复杂对象，提取文本内容
              if (typeof rendered === 'object' && rendered !== null) {
                return String(value || '')
              }
              return rendered
            } catch {
              return value || ''
            }
          }
          return value || ''
        })
      })

      // 合并表头和数据
      const wsData = [headers, ...rows]
      const ws = XLSX.utils.aoa_to_sheet(wsData)
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')
    } else {
      // 大数据量分批处理
      for (let i = 0; i < totalBatches; i++) {
        const start = i * batchSize
        const end = Math.min(start + batchSize, data.length)
        const batchData = data.slice(start, end)

        console.log(`处理第 ${i + 1}/${totalBatches} 批数据 (${start + 1}-${end})`)

        const rows = batchData.map(row => {
          return columns.map(col => {
            const value = row[col.dataIndex]
            // 处理特殊格式
            if (col.customRender && typeof col.customRender === 'function') {
              try {
                const rendered = col.customRender({ text: value, record: row })
                if (typeof rendered === 'object' && rendered !== null) {
                  return String(value || '')
                }
                return rendered
              } catch {
                return value || ''
              }
            }
            return value || ''
          })
        })

        // 第一批包含表头
        const wsData = i === 0 ? [headers, ...rows] : rows

        if (i === 0) {
          const ws = XLSX.utils.aoa_to_sheet(wsData)
          XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')
        } else {
          // 追加数据到现有工作表
          const ws = wb.Sheets['Sheet1']
          XLSX.utils.sheet_add_aoa(ws, rows, { origin: -1 })
        }
      }
    }

    console.log('Excel数据处理完成，开始生成文件')

    // 生成Excel文件并下载
    XLSX.writeFile(wb, filename)

    message.success(`导出成功，共 ${data.length} 条数据`)
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  }
}

/**
 * 用户列表导出
 * @param data 用户数据
 * @param columns 表格列配置
 */
export const exportUserList = async (data: any[], columns: any[]): Promise<void> => {
  const filename = `用户列表_${new Date().toISOString().slice(0, 10)}.xlsx`
  exportToExcel(data, columns, filename)
}

/**
 * 订单列表导出
 * @param data 订单数据
 * @param columns 表格列配置
 */
export const exportOrderList = async (data: any[], columns: any[]): Promise<void> => {
  const filename = `订单列表_${new Date().toISOString().slice(0, 10)}.xlsx`
  exportToExcel(data, columns, filename)
}

/**
 * 退款列表导出
 * @param data 退款数据
 * @param columns 表格列配置
 */
export const exportRefundList = async (data: any[], columns: any[]): Promise<void> => {
  const filename = `退款列表_${new Date().toISOString().slice(0, 10)}.xlsx`
  exportToExcel(data, columns, filename)
}

/**
 * 获取当前搜索参数（移除分页相关参数）
 * @param searchForm 搜索表单
 * @returns 清理后的搜索参数
 */
export const getExportParams = (searchForm: Record<string, any>): Record<string, any> => {
  const params = { ...searchForm }
  
  // 移除分页相关参数
  delete params.page
  delete params.size
  delete params.current
  delete params.pageSize
  
  // 移除空值
  Object.keys(params).forEach(key => {
    if (params[key] === undefined || params[key] === null || params[key] === '') {
      delete params[key]
    }
  })
  
  return params
}
