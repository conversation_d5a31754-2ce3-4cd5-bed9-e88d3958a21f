# API接口文档

## 🌐 基础信息

- **Base URL**: `https://api.zj7hui.com`
- **认证方式**: Bearer Token
- **请求格式**: `application/json`
- **响应格式**: `application/json`

## 🔐 认证相关

### 登录接口
```
POST /pasture/admin/login/login
```

**请求参数**:
```typescript
{
  account: string  // 登录账号
  pwd: string      // 登录密码
}
```

**响应格式**:
```typescript
{
  code: string
  data: {
    token: string
    userInfo: object
  }
  message: string
  msg: string
}
```

### 获取应用列表
```
POST /pasture/admin/login/appList
```

**请求参数**: 无

**响应格式**:
```typescript
{
  code: string
  data: Array<{
    code: string    // 应用编码
    name: string    // 应用名称
  }>
  message: string
  msg: string
}
```

## 👥 用户管理

### 获取用户列表
```
POST /pasture/admin/user/list
```

**请求参数**:
```typescript
{
  page: number           // 页码，从1开始
  size: number           // 每页数量
  playerId?: number      // 玩家ID（可选）
  account?: string       // 账号（可选）
  appCode?: string       // 应用渠道（可选）
  startTime?: string     // 开始时间 yyyyMMdd HHmmss
  endTime?: string       // 结束时间 yyyyMMdd HHmmss
}
```

**响应格式**:
```typescript
{
  code: string
  data: {
    list: Array<{
      id: number
      userId: number
      account: string
      nickname: string
      appCode: string
      vipStatus: number    // 0=普通 1=VIP
      status: number       // 0=正常 1=禁用
      level: number
      sex: number         // 1=男 2=女
      createTime: string
    }>
    total: number
  }
  message: string
  msg: string
}
```

### 获取用户详情
```
POST /pasture/admin/user/detail
```

**请求参数**:
```typescript
{
  playerId: number  // 玩家ID
}
```

### 获取玩家币信息
```
POST /pasture/admin/user/coin
```

**请求参数**:
```typescript
{
  playerId: number  // 玩家ID
}
```

## 📦 订单管理

### 获取订单列表
```
POST /pasture/admin/order/list
```

**请求参数**:
```typescript
{
  page: number           // 页码
  size: number           // 每页数量
  appCode?: string       // 应用渠道
  orderNo?: string       // 订单编号
  payType?: number       // 支付方式
  status?: number        // 订单状态
  startTime?: string     // 开始时间 yyyyMMdd HHmmss
  endTime?: string       // 结束时间 yyyyMMdd HHmmss
}
```

**响应格式**:
```typescript
{
  code: string
  data: {
    list: Array<{
      id: number
      flowNo: string       // 订单号
      playerId: number     // 玩家ID
      playerName: string   // 玩家名称
      appCode: string      // 应用渠道
      amount: number       // 订单金额
      payType: number      // 支付方式
      status: number       // 订单状态
      createTime: string   // 创建时间
      payTime: string      // 支付时间
    }>
    total: number
  }
  message: string
  msg: string
}
```

### 订单退款
```
POST /pasture/admin/order/refund
```

**请求参数**:
```typescript
{
  orderId: number      // 订单ID
  type: number         // 退款类型
  amount: number       // 退款金额
  refundDesc: string   // 退款说明
  name: string         // 操作人
}
```

## 💰 退款管理

### 获取退款列表
```
POST /pasture/admin/refund/list
```

**请求参数**:
```typescript
{
  page: number           // 页码
  size: number           // 每页数量
  appCode?: string       // 应用渠道
  refundSn?: string      // 退款编号
  flowNo?: string        // 订单编号
  startTime?: string     // 开始时间 yyyyMMdd HHmmss
  endTime?: string       // 结束时间 yyyyMMdd HHmmss
}
```

**响应格式**:
```typescript
{
  code: string
  data: Array<{
    id: number
    refundSn: string     // 退款编号
    flowNo: string       // 订单编号
    playerId: number     // 玩家ID
    playerName: string   // 玩家名称
    appCode: string      // 应用渠道
    amount: number       // 退款金额
    payType: number      // 支付方式
    refundStatus: number // 退款状态
    createName: string   // 操作人
    createTime: string   // 申请时间
  }>
  message: string
  msg: string
}
```

## 📊 统计相关

### 获取维度统计
```
POST /pasture/admin/statistics/dimension
```

**请求参数**:
```typescript
{
  appCode: string  // 应用渠道
}
```

**响应格式**:
```typescript
{
  code: string
  data: Array<{
    id: number
    name: string         // 维度名称
    total: number        // 总数值
    range: number        // 日环比
    rangeType: number    // 趋势类型 1=上升 0=下降
    weekRange: number    // 周环比
    weekRangeType: number // 周趋势类型
    unitType: number     // 单位类型 1=秒 2=个 3=元
  }>
  message: string
  msg: string
}
```

## 📤 导出接口

### 用户数据导出
```
GET /pasture/admin/user/export?[params]
```

### 订单数据导出
```
GET /pasture/admin/order/export?[params]
```

### 退款数据导出
```
GET /pasture/admin/refund/export?[params]
```

**导出参数**: 与对应列表接口的查询参数相同，但移除分页参数

## 🔧 状态码说明

### 订单状态 (status)
- `0`: 待支付
- `1`: 已支付
- `2`: 支付失败
- `3`: 用户已支付,待确认
- `4`: 已退款
- `5`: 部分退款

### 支付方式 (payType)
- `1`: 微信
- `2`: 小天才服务号
- `3`: 线下支付
- `4`: 小天才手表
- `5`: 小天才H5
- `6`: 华为支付

### 退款状态 (refundStatus)
- `0`: 退款中
- `1`: 退款成功
- `2`: 退款失败

## ⚠️ 注意事项

1. **时间格式**: 所有时间参数使用 `yyyyMMdd HHmmss` 格式
2. **分页**: 页码从1开始，建议每页10-50条数据
3. **认证**: 所有接口都需要在Header中携带Token
4. **错误处理**: 根据code字段判断请求是否成功
5. **数据加密**: 部分敏感数据（如账号）可能经过Base64编码

## 🔄 错误码

- `200`: 成功
- `401`: 未授权，需要重新登录
- `403`: 权限不足
- `500`: 服务器内部错误

## 📝 更新记录

- **v2.0**: 添加应用渠道API，统一时间格式，新增导出接口
- **v1.0**: 基础CRUD接口实现
