获取宠物详情（获得宠物后调用）

**接口地址** `/pasture/player/pet/detail`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerPetId         |      playerPetId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«宠物或图鉴»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    宠物或图鉴   |   宠物或图鉴    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**宠物或图鉴**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | biography         |     传记      |  string   |      |
            | debris         |     孵化碎片      |  背包道具   | 背包道具     |
            | differenceAttrs         |     属性值差值      |  array   | 属性     |
            | differenceScore         |     评分差值      |  int32   |      |
            | evolutionId         |     evolutionId等阶id      |  int64   |      |
            | evolutionItems         |     进阶材料      |  array   | 背包道具     |
            | evolutionRedStatus         |     进阶红点状态 0 无红点 1有红点       |  int32   |      |
            | evolutionType         |     evolutionType等阶类型（冗余）      |  int32   |      |
            | expValue         |     exp当前经验      |  int64   |      |
            | holdStatus         |     打工状态 1空闲 2打工中      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | ingredients         |     升级所需材料      |  array   | 背包道具     |
            | inviteStatus         |     任务邀请状态 0未邀请 1已邀请       |  int32   |      |
            | isNeedEvolution         |     是否待进阶      |  boolean   |      |
            | journeyStatus         |     旅行状态 1空闲 2旅行中      |  int32   |      |
            | level         |     level当前等级      |  int32   |      |
            | link         |     link获取链接      |  string   |      |
            | model         |     模型      |  string   |      |
            | name         |     name      |  string   |      |
            | needCoin         |     升级所需金币      |  int32   |      |
            | needGrain         |     下级所需口粮      |  int64   |      |
            | nextEvolutionIcon         |     下一阶图标      |  string   |      |
            | nextExpValue         |     下级所需经验      |  int64   |      |
            | num         |     拥有个数      |  int32   |      |
            | patrolStatus         |     巡逻状态 0-未巡逻 1-巡逻中      |  int32   |      |
            | petId         |     petId      |  int64   |      |
            | playerPetId         |     玩家宠物id      |  int64   |      |
            | redStatus         |     列表红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | soliloquy         |     独白      |  string   |      |
            | species         |      种类 1宠物 2纪念品3特产4国宝5小伙伴      |  int32   |      |
            | status         |     status状态 0未拥有 1已拥有      |  int32   |      |
            | type         |     类型 1：A（紫色）,2：S（金色）,3：SS（红色）      |  int32   |      |
            | upgradeRedStatus         |     升一级红点状态 0 无红点 1有红点       |  int32   |      |
            | useNum         |     使用消耗个数      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**背包道具**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | bizType         |     类型 1宠物材料 2宠物蛋 3宠物道具 4人物升级材料 5互动道具 6外观碎片 7抽奖道具      |  int32   |      |
            | effect         |     effect作用      |  string   |      |
            | hatchStatus         |     状态 1可以孵化 2不可以      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | link         |     link获取链接      |  string   |      |
            | memo         |     memo备注      |  string   |      |
            | name         |     name名称      |  string   |      |
            | num         |     拥有个数      |  int32   |      |
            | packId         |     packId      |  int64   |      |
            | playerPackId         |     playerPackId      |  int64   |      |
            | price         |     price价值      |  int32   |      |
            | redStatus         |     红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | sellLevel         |     售卖等级 （小于玩家等级 可以售卖）      |  int32   |      |
            | source         |     source获取来源      |  string   |      |
            | status         |     status状态 0未用于  1已拥有 2已装备      |  int32   |      |
            | type         |     type类型 1宠物蛋 2材料      |  int32   |      |
            | useNum         |     使用消耗个数      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "attrs": [
            {
                "attrType": 0,
                "attrValue": 0,
                "type": 0
            }
        ],
        "biography": "",
        "debris": {
            "attrs": [
                {
                    "attrType": 0,
                    "attrValue": 0,
                    "type": 0
                }
            ],
            "bizType": 0,
            "effect": "",
            "hatchStatus": 0,
            "icon": "",
            "link": "",
            "memo": "",
            "name": "",
            "num": 0,
            "packId": 0,
            "playerPackId": 0,
            "price": 0,
            "redStatus": 0,
            "score": 0,
            "sellLevel": 0,
            "source": "",
            "status": 0,
            "type": 0,
            "useNum": 0
        },
        "differenceAttrs": [
            {
                "attrType": 0,
                "attrValue": 0,
                "type": 0
            }
        ],
        "differenceScore": 0,
        "evolutionId": 0,
        "evolutionItems": [
            {}
        ],
        "evolutionRedStatus": 0,
        "evolutionType": 0,
        "expValue": 0,
        "holdStatus": 0,
        "icon": "",
        "ingredients": [
            {}
        ],
        "inviteStatus": 0,
        "isNeedEvolution": true,
        "journeyStatus": 0,
        "level": 0,
        "link": "",
        "model": "",
        "name": "",
        "needCoin": 0,
        "needGrain": 0,
        "nextEvolutionIcon": "",
        "nextExpValue": 0,
        "num": 0,
        "patrolStatus": 0,
        "petId": 0,
        "playerPetId": 0,
        "redStatus": 0,
        "score": 0,
        "soliloquy": "",
        "species": 0,
        "status": 0,
        "type": 0,
        "upgradeRedStatus": 0,
        "useNum": 0
    },
    "message": "",
    "msg": ""
}
```



--------分割线
装备宠物

**接口地址** `/pasture/player/pet/equip`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerPetId         |      playerPetId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------分割线
宠物进阶

**接口地址** `/pasture/player/pet/evolution`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerPetId         |      playerPetId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«消耗操作结果信息«进阶后返回»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    消耗操作结果信息«进阶后返回»   |   消耗操作结果信息«进阶后返回»    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**消耗操作结果信息«进阶后返回»**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| bizData         |     业务相关数据      |  进阶后返回   | 进阶后返回     |
            | coins         |     能量币数值变化      |  array   | AwardCoinDTO     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | remind         |     提醒内容      |  string   |      |
            | status         |     1-成功 2-代币不足 3-会员级别不足 4当日次数已用完 5积分不足 6事件已存在无效操作 7道具不足       |  int32   |      |
            

**进阶后返回**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| evolutionLevel         |     等阶等级      |  int32   |      |
            | evolutionType         |     进价类型      |  int32   |      |
            | icon         |     图标      |  string   |      |
            | name         |     名称      |  string   |      |
            | score         |     分      |  int32   |      |
            | type         |     类型      |  int32   |      |
            

**AwardCoinDTO**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| name         |           |  string   |      |
            | num         |           |  int32   |      |
            | type         |           |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "bizData": {
            "evolutionLevel": 0,
            "evolutionType": 0,
            "icon": "",
            "name": "",
            "score": 0,
            "type": 0
        },
        "coins": [
            {
                "name": "",
                "num": 0,
                "type": 0
            }
        ],
        "isVip": 0,
        "remind": "",
        "status": 0
    },
    "message": "",
    "msg": ""
}
```



--------分割线
孵化

**接口地址** `/pasture/player/pet/hatch`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| packId         |      packId   |     query        |       true      | integer   |      |
            | playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«宠物»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    宠物   |   宠物    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**宠物**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     attrType属性类型 1草 2火 3水 4光 5暗（冗余）      |  int32   |      |
            | awardMenuList         |     掉落物品      |  array   | 掉落物品明细     |
            | dropType         |     类型 1宠物 2背包物品      |  int32   |      |
            | evolutionType         |     evolutionType等阶类型（冗余）      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | level         |     level当前等级      |  int32   |      |
            | name         |     name      |  string   |      |
            | petId         |     petId      |  int64   |      |
            | playerPetId         |     玩家宠物id      |  int64   |      |
            | score         |     score评分      |  int32   |      |
            | soliloquy         |     独白      |  string   |      |
            | type         |     类型 1普通 2稀有 3非凡 4完美      |  int32   |      |
            

**掉落物品明细**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| code         |     图标      |  string   |      |
            | name         |     名称      |  string   |      |
            | sourceId         |     sourceId道具id      |  int64   |      |
            | sourceType         |     sourceType类型 1背包方案 2背包道具 3主角经验 4能量币 5体力 6矿石 7宠物 8装备 9排位积分 10 金币      |  int32   |      |
            | type         |     类型 1普通掉落 2首次掉落      |  int32   |      |
            | upgrade         |     人物升级信息      |  升级后玩家信息   | 升级后玩家信息     |
            | value         |     num数量      |  int32   |      |
            

**升级后玩家信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coinType         |     收获代币类型： 1-金币，2-能量      |  int32   |      |
            | coinVal         |     收获代币值      |  int32   |      |
            | drops         |     物品掉落      |  array   | 掉落物品明细     |
            | level         |     人物等级      |  int32   |      |
            | player         |     玩家      |  玩家详情   | 玩家详情     |
            | score         |     评分      |  int32   |      |
            

**玩家详情**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | breed         |     动物养殖      |  动物养殖   | 动物养殖     |
            | coin         |     金币      |  int32   |      |
            | energy         |     能量      |  int32   |      |
            | equipageScore         |     装备评分      |  int32   |      |
            | equipages         |     装备      |  array   | 玩家装备     |
            | events         |     互动事件      |  array   | 互动事件     |
            | expValue         |     expValue当前经验值      |  int64   |      |
            | followWx         |     是否关注微信公众号 1-已关注 0-未关注      |  int32   |      |
            | icon         |     玩家头像图标地址      |  string   |      |
            | interacts         |     互动      |  array   | 互动事件     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | level         |     人物等级      |  int32   |      |
            | levelName         |     官职      |  string   |      |
            | model         |     角色模型      |  string   |      |
            | nextExpValue         |     nextExpValue下一个等级所需经验值      |  int64   |      |
            | nickname         |     玩家昵称      |  string   |      |
            | ore         |     粮草      |  int32   |      |
            | playerId         |     玩家id      |  int64   |      |
            | protectStatus         |     保护状态 1有保护 2无保护      |  int32   |      |
            | rankScore         |     排位积分      |  int32   |      |
            | score         |     总评分      |  int32   |      |
            | sex         |     玩家性别 1-男 2-女      |  int32   |      |
            | status         |     玩家状态 0-不可用 1-正常      |  int32   |      |
            | userId         |     用户id      |  string   |      |
            | vim         |     活力      |  int32   |      |
            | vimLimit         |     活力上限      |  int32   |      |
            | vipOre         |     vip粮草      |  int32   |      |
            | vipStatus         |     vip状态      |  int32   |      |
            | wish         |     许愿值      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**动物养殖**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| animalId         |     animalId动物id      |  int64   |      |
            | breedTime         |     breedTime养殖时间      |  date-time   |      |
            | createTime         |     createTime      |  date-time   |      |
            | downTime         |     倒计时      |  int64   |      |
            | modelSpine         |     动效      |  string   |      |
            | petId         |     petId宠物id      |  int64   |      |
            | pickStatus         |     是否可以采摘 1可以 2自己不能偷 3好友不能偷      |  int32   |      |
            | playerBreedId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态1 成长中 2待收获 3已收获      |  int32   |      |
            | suspendTime         |     暂停时间 不为空就是被暂停      |  date-time   |      |
            

**玩家装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| id         |     id      |  int64   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | type         |     类型 1神兵 2宝马 3皮肤 4兵书 5虎符      |  int32   |      |
            

**互动事件**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coin         |     金币      |  int32   |      |
            | createTime         |     createTime      |  date-time   |      |
            | detail         |     详情      |  string   |      |
            | eventType         |     操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽  8便便 9蚊子 10老鼠      |  int32   |      |
            | eventVal         |     eventVal事件值      |  int32   |      |
            | finishVal         |     eventVal事件完成值      |  int32   |      |
            | icon         |     图片      |  string   |      |
            | playerEventId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "attrType": 0,
        "awardMenuList": [
            {
                "code": "",
                "name": "",
                "sourceId": 0,
                "sourceType": 0,
                "type": 0,
                "upgrade": {
                    "coinType": 0,
                    "coinVal": 0,
                    "drops": [
                        {}
                    ],
                    "level": 0,
                    "player": {
                        "attrs": [
                            {
                                "attrType": 0,
                                "attrValue": 0,
                                "type": 0
                            }
                        ],
                        "breed": {
                            "animalId": 0,
                            "breedTime": "",
                            "createTime": "",
                            "downTime": 0,
                            "modelSpine": "",
                            "petId": 0,
                            "pickStatus": 0,
                            "playerBreedId": 0,
                            "playerId": 0,
                            "seq": 0,
                            "status": 0,
                            "suspendTime": ""
                        },
                        "coin": 0,
                        "energy": 0,
                        "equipageScore": 0,
                        "equipages": [
                            {
                                "id": 0,
                                "model": "",
                                "modelSpine": "",
                                "name": "",
                                "type": 0
                            }
                        ],
                        "events": [
                            {
                                "coin": 0,
                                "createTime": "",
                                "detail": "",
                                "eventType": 0,
                                "eventVal": 0,
                                "finishVal": 0,
                                "icon": "",
                                "playerEventId": 0,
                                "playerId": 0
                            }
                        ],
                        "expValue": 0,
                        "followWx": 0,
                        "icon": "",
                        "interacts": [
                            {
                                "coin": 0,
                                "createTime": "",
                                "detail": "",
                                "eventType": 0,
                                "eventVal": 0,
                                "finishVal": 0,
                                "icon": "",
                                "playerEventId": 0,
                                "playerId": 0
                            }
                        ],
                        "isVip": 0,
                        "level": 0,
                        "levelName": "",
                        "model": "",
                        "nextExpValue": 0,
                        "nickname": "",
                        "ore": 0,
                        "playerId": 0,
                        "protectStatus": 0,
                        "rankScore": 0,
                        "score": 0,
                        "sex": 0,
                        "status": 0,
                        "userId": "",
                        "vim": 0,
                        "vimLimit": 0,
                        "vipOre": 0,
                        "vipStatus": 0,
                        "wish": 0
                    },
                    "score": 0
                },
                "value": 0
            }
        ],
        "dropType": 0,
        "evolutionType": 0,
        "icon": "",
        "level": 0,
        "name": "",
        "petId": 0,
        "playerPetId": 0,
        "score": 0,
        "soliloquy": "",
        "type": 0
    },
    "message": "",
    "msg": ""
}
```



--------分割线
获取宠物列表（包含为获取宠物详情，宠物总属性）

**接口地址** `/pasture/player/pet/list`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| params         |      params   |     body        |       true      | 获取宠物栏参数   | 获取宠物栏参数     |
            



**schema属性说明**
  
**获取宠物栏参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId  | 玩家ID |   body    |   false   |int64  |       |
| species  | 种类 1宠物 2纪念品3特产4国宝5小伙伴 |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«玩家宠物列表»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    玩家宠物列表   |   玩家宠物列表    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**玩家宠物列表**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | playerPets         |     宠物列表      |  array   | 宠物或图鉴     |
            | score         |     score评分      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**宠物或图鉴**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | biography         |     传记      |  string   |      |
            | debris         |     孵化碎片      |  背包道具   | 背包道具     |
            | differenceAttrs         |     属性值差值      |  array   | 属性     |
            | differenceScore         |     评分差值      |  int32   |      |
            | evolutionId         |     evolutionId等阶id      |  int64   |      |
            | evolutionItems         |     进阶材料      |  array   | 背包道具     |
            | evolutionRedStatus         |     进阶红点状态 0 无红点 1有红点       |  int32   |      |
            | evolutionType         |     evolutionType等阶类型（冗余）      |  int32   |      |
            | expValue         |     exp当前经验      |  int64   |      |
            | holdStatus         |     打工状态 1空闲 2打工中      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | ingredients         |     升级所需材料      |  array   | 背包道具     |
            | inviteStatus         |     任务邀请状态 0未邀请 1已邀请       |  int32   |      |
            | isNeedEvolution         |     是否待进阶      |  boolean   |      |
            | journeyStatus         |     旅行状态 1空闲 2旅行中      |  int32   |      |
            | level         |     level当前等级      |  int32   |      |
            | link         |     link获取链接      |  string   |      |
            | model         |     模型      |  string   |      |
            | name         |     name      |  string   |      |
            | needCoin         |     升级所需金币      |  int32   |      |
            | needGrain         |     下级所需口粮      |  int64   |      |
            | nextEvolutionIcon         |     下一阶图标      |  string   |      |
            | nextExpValue         |     下级所需经验      |  int64   |      |
            | num         |     拥有个数      |  int32   |      |
            | patrolStatus         |     巡逻状态 0-未巡逻 1-巡逻中      |  int32   |      |
            | petId         |     petId      |  int64   |      |
            | playerPetId         |     玩家宠物id      |  int64   |      |
            | redStatus         |     列表红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | soliloquy         |     独白      |  string   |      |
            | species         |      种类 1宠物 2纪念品3特产4国宝5小伙伴      |  int32   |      |
            | status         |     status状态 0未拥有 1已拥有      |  int32   |      |
            | type         |     类型 1：A（紫色）,2：S（金色）,3：SS（红色）      |  int32   |      |
            | upgradeRedStatus         |     升一级红点状态 0 无红点 1有红点       |  int32   |      |
            | useNum         |     使用消耗个数      |  int32   |      |
            

**背包道具**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | bizType         |     类型 1宠物材料 2宠物蛋 3宠物道具 4人物升级材料 5互动道具 6外观碎片 7抽奖道具      |  int32   |      |
            | effect         |     effect作用      |  string   |      |
            | hatchStatus         |     状态 1可以孵化 2不可以      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | link         |     link获取链接      |  string   |      |
            | memo         |     memo备注      |  string   |      |
            | name         |     name名称      |  string   |      |
            | num         |     拥有个数      |  int32   |      |
            | packId         |     packId      |  int64   |      |
            | playerPackId         |     playerPackId      |  int64   |      |
            | price         |     price价值      |  int32   |      |
            | redStatus         |     红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | sellLevel         |     售卖等级 （小于玩家等级 可以售卖）      |  int32   |      |
            | source         |     source获取来源      |  string   |      |
            | status         |     status状态 0未用于  1已拥有 2已装备      |  int32   |      |
            | type         |     type类型 1宠物蛋 2材料      |  int32   |      |
            | useNum         |     使用消耗个数      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "attrs": [
            {
                "attrType": 0,
                "attrValue": 0,
                "type": 0
            }
        ],
        "playerPets": [
            {
                "attrs": [
                    {
                        "attrType": 0,
                        "attrValue": 0,
                        "type": 0
                    }
                ],
                "biography": "",
                "debris": {
                    "attrs": [
                        {
                            "attrType": 0,
                            "attrValue": 0,
                            "type": 0
                        }
                    ],
                    "bizType": 0,
                    "effect": "",
                    "hatchStatus": 0,
                    "icon": "",
                    "link": "",
                    "memo": "",
                    "name": "",
                    "num": 0,
                    "packId": 0,
                    "playerPackId": 0,
                    "price": 0,
                    "redStatus": 0,
                    "score": 0,
                    "sellLevel": 0,
                    "source": "",
                    "status": 0,
                    "type": 0,
                    "useNum": 0
                },
                "differenceAttrs": [
                    {
                        "attrType": 0,
                        "attrValue": 0,
                        "type": 0
                    }
                ],
                "differenceScore": 0,
                "evolutionId": 0,
                "evolutionItems": [
                    {}
                ],
                "evolutionRedStatus": 0,
                "evolutionType": 0,
                "expValue": 0,
                "holdStatus": 0,
                "icon": "",
                "ingredients": [
                    {}
                ],
                "inviteStatus": 0,
                "isNeedEvolution": true,
                "journeyStatus": 0,
                "level": 0,
                "link": "",
                "model": "",
                "name": "",
                "needCoin": 0,
                "needGrain": 0,
                "nextEvolutionIcon": "",
                "nextExpValue": 0,
                "num": 0,
                "patrolStatus": 0,
                "petId": 0,
                "playerPetId": 0,
                "redStatus": 0,
                "score": 0,
                "soliloquy": "",
                "species": 0,
                "status": 0,
                "type": 0,
                "upgradeRedStatus": 0,
                "useNum": 0
            }
        ],
        "score": 0
    },
    "message": "",
    "msg": ""
}
```



--------分割线
获取宠物列表

**接口地址** `/pasture/player/pet/listWithMissionStatus`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| params         |      params   |     body        |       true      | 获取宠物栏参数   | 获取宠物栏参数     |
            



**schema属性说明**
  
**获取宠物栏参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId  | 玩家ID |   body    |   false   |int64  |       |
| species  | 种类 1宠物 2纪念品3特产4国宝5小伙伴 |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«玩家宠物列表»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    玩家宠物列表   |   玩家宠物列表    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**玩家宠物列表**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | playerPets         |     宠物列表      |  array   | 宠物或图鉴     |
            | score         |     score评分      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**宠物或图鉴**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | biography         |     传记      |  string   |      |
            | debris         |     孵化碎片      |  背包道具   | 背包道具     |
            | differenceAttrs         |     属性值差值      |  array   | 属性     |
            | differenceScore         |     评分差值      |  int32   |      |
            | evolutionId         |     evolutionId等阶id      |  int64   |      |
            | evolutionItems         |     进阶材料      |  array   | 背包道具     |
            | evolutionRedStatus         |     进阶红点状态 0 无红点 1有红点       |  int32   |      |
            | evolutionType         |     evolutionType等阶类型（冗余）      |  int32   |      |
            | expValue         |     exp当前经验      |  int64   |      |
            | holdStatus         |     打工状态 1空闲 2打工中      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | ingredients         |     升级所需材料      |  array   | 背包道具     |
            | inviteStatus         |     任务邀请状态 0未邀请 1已邀请       |  int32   |      |
            | isNeedEvolution         |     是否待进阶      |  boolean   |      |
            | journeyStatus         |     旅行状态 1空闲 2旅行中      |  int32   |      |
            | level         |     level当前等级      |  int32   |      |
            | link         |     link获取链接      |  string   |      |
            | model         |     模型      |  string   |      |
            | name         |     name      |  string   |      |
            | needCoin         |     升级所需金币      |  int32   |      |
            | needGrain         |     下级所需口粮      |  int64   |      |
            | nextEvolutionIcon         |     下一阶图标      |  string   |      |
            | nextExpValue         |     下级所需经验      |  int64   |      |
            | num         |     拥有个数      |  int32   |      |
            | patrolStatus         |     巡逻状态 0-未巡逻 1-巡逻中      |  int32   |      |
            | petId         |     petId      |  int64   |      |
            | playerPetId         |     玩家宠物id      |  int64   |      |
            | redStatus         |     列表红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | soliloquy         |     独白      |  string   |      |
            | species         |      种类 1宠物 2纪念品3特产4国宝5小伙伴      |  int32   |      |
            | status         |     status状态 0未拥有 1已拥有      |  int32   |      |
            | type         |     类型 1：A（紫色）,2：S（金色）,3：SS（红色）      |  int32   |      |
            | upgradeRedStatus         |     升一级红点状态 0 无红点 1有红点       |  int32   |      |
            | useNum         |     使用消耗个数      |  int32   |      |
            

**背包道具**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | bizType         |     类型 1宠物材料 2宠物蛋 3宠物道具 4人物升级材料 5互动道具 6外观碎片 7抽奖道具      |  int32   |      |
            | effect         |     effect作用      |  string   |      |
            | hatchStatus         |     状态 1可以孵化 2不可以      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | link         |     link获取链接      |  string   |      |
            | memo         |     memo备注      |  string   |      |
            | name         |     name名称      |  string   |      |
            | num         |     拥有个数      |  int32   |      |
            | packId         |     packId      |  int64   |      |
            | playerPackId         |     playerPackId      |  int64   |      |
            | price         |     price价值      |  int32   |      |
            | redStatus         |     红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | sellLevel         |     售卖等级 （小于玩家等级 可以售卖）      |  int32   |      |
            | source         |     source获取来源      |  string   |      |
            | status         |     status状态 0未用于  1已拥有 2已装备      |  int32   |      |
            | type         |     type类型 1宠物蛋 2材料      |  int32   |      |
            | useNum         |     使用消耗个数      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "attrs": [
            {
                "attrType": 0,
                "attrValue": 0,
                "type": 0
            }
        ],
        "playerPets": [
            {
                "attrs": [
                    {
                        "attrType": 0,
                        "attrValue": 0,
                        "type": 0
                    }
                ],
                "biography": "",
                "debris": {
                    "attrs": [
                        {
                            "attrType": 0,
                            "attrValue": 0,
                            "type": 0
                        }
                    ],
                    "bizType": 0,
                    "effect": "",
                    "hatchStatus": 0,
                    "icon": "",
                    "link": "",
                    "memo": "",
                    "name": "",
                    "num": 0,
                    "packId": 0,
                    "playerPackId": 0,
                    "price": 0,
                    "redStatus": 0,
                    "score": 0,
                    "sellLevel": 0,
                    "source": "",
                    "status": 0,
                    "type": 0,
                    "useNum": 0
                },
                "differenceAttrs": [
                    {
                        "attrType": 0,
                        "attrValue": 0,
                        "type": 0
                    }
                ],
                "differenceScore": 0,
                "evolutionId": 0,
                "evolutionItems": [
                    {}
                ],
                "evolutionRedStatus": 0,
                "evolutionType": 0,
                "expValue": 0,
                "holdStatus": 0,
                "icon": "",
                "ingredients": [
                    {}
                ],
                "inviteStatus": 0,
                "isNeedEvolution": true,
                "journeyStatus": 0,
                "level": 0,
                "link": "",
                "model": "",
                "name": "",
                "needCoin": 0,
                "needGrain": 0,
                "nextEvolutionIcon": "",
                "nextExpValue": 0,
                "num": 0,
                "patrolStatus": 0,
                "petId": 0,
                "playerPetId": 0,
                "redStatus": 0,
                "score": 0,
                "soliloquy": "",
                "species": 0,
                "status": 0,
                "type": 0,
                "upgradeRedStatus": 0,
                "useNum": 0
            }
        ],
        "score": 0
    },
    "message": "",
    "msg": ""
}
```



--------分割线
宠物升级（type1升1级 （默认）2升10级）

**接口地址** `/pasture/player/pet/upgrade`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerPetId         |      playerPetId   |     query        |       true      | integer   |      |
            | type         |      type   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«消耗操作结果信息«int»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    消耗操作结果信息«int»   |   消耗操作结果信息«int»    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**消耗操作结果信息«int»**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| bizData         |     业务相关数据      |  int32   |      |
            | coins         |     能量币数值变化      |  array   | AwardCoinDTO     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | remind         |     提醒内容      |  string   |      |
            | status         |     1-成功 2-代币不足 3-会员级别不足 4当日次数已用完 5积分不足 6事件已存在无效操作 7道具不足       |  int32   |      |
            

**AwardCoinDTO**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| name         |           |  string   |      |
            | num         |           |  int32   |      |
            | type         |           |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "bizData": 0,
        "coins": [
            {
                "name": "",
                "num": 0,
                "type": 0
            }
        ],
        "isVip": 0,
        "remind": "",
        "status": 0
    },
    "message": "",
    "msg": ""
}
```


