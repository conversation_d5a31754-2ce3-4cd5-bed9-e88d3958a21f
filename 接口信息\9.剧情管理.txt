获取当前剧情

**接口地址** `/pasture/app/plot/currentPlot`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«剧情»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    剧情   |   剧情    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**剧情**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| dialogues         |     对话详情      |  array   | 剧情对话     |
            | icon         |     icon图标      |  string   |      |
            | memo         |     memo备注      |  string   |      |
            | name         |     name名称      |  string   |      |
            | plotId         |     id      |  int64   |      |
            | progress         |     当前进度      |  int32   |      |
            | seq         |     seq排序（主线剧情使用）      |  int32   |      |
            | speed         |     speed快进类型  1不可快进 2可加速 3可跳过       |  int32   |      |
            | status         |     状态 1未完成 2已完成       |  int32   |      |
            | totalProgress         |     总进度      |  int32   |      |
            | type         |     type类型 1主线剧情 2普通剧情       |  int32   |      |
            

**剧情对话**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| content         |     content内容      |  string   |      |
            | icon         |     icon图标      |  string   |      |
            | id         |     id      |  int64   |      |
            | name         |     name名字      |  string   |      |
            | plotId         |     plotId剧情id      |  int64   |      |
            | roleType         |     角色类型 1左侧 2右侧      |  int32   |      |
            | seq         |     seq排序      |  int32   |      |
            | speed         |     speed快进类型  1不可快进 2可加速 3可跳过       |  int32   |      |
            | type         |     类型 1、对话 2、弹窗（中间字幕） 3、弹窗（底部字幕）      |  int32   |      |
            | voice         |     voice语音      |  string   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "dialogues": [
            {
                "content": "",
                "icon": "",
                "id": 0,
                "name": "",
                "plotId": 0,
                "roleType": 0,
                "seq": 0,
                "speed": 0,
                "type": 0,
                "voice": ""
            }
        ],
        "icon": "",
        "memo": "",
        "name": "",
        "plotId": 0,
        "progress": 0,
        "seq": 0,
        "speed": 0,
        "status": 0,
        "totalProgress": 0,
        "type": 0
    },
    "message": "",
    "msg": ""
}
```



--------分割线
获取剧情详情

**接口地址** `/pasture/app/plot/detail`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            | plotId         |      plotId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«剧情»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    剧情   |   剧情    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**剧情**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| dialogues         |     对话详情      |  array   | 剧情对话     |
            | icon         |     icon图标      |  string   |      |
            | memo         |     memo备注      |  string   |      |
            | name         |     name名称      |  string   |      |
            | plotId         |     id      |  int64   |      |
            | progress         |     当前进度      |  int32   |      |
            | seq         |     seq排序（主线剧情使用）      |  int32   |      |
            | speed         |     speed快进类型  1不可快进 2可加速 3可跳过       |  int32   |      |
            | status         |     状态 1未完成 2已完成       |  int32   |      |
            | totalProgress         |     总进度      |  int32   |      |
            | type         |     type类型 1主线剧情 2普通剧情       |  int32   |      |
            

**剧情对话**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| content         |     content内容      |  string   |      |
            | icon         |     icon图标      |  string   |      |
            | id         |     id      |  int64   |      |
            | name         |     name名字      |  string   |      |
            | plotId         |     plotId剧情id      |  int64   |      |
            | roleType         |     角色类型 1左侧 2右侧      |  int32   |      |
            | seq         |     seq排序      |  int32   |      |
            | speed         |     speed快进类型  1不可快进 2可加速 3可跳过       |  int32   |      |
            | type         |     类型 1、对话 2、弹窗（中间字幕） 3、弹窗（底部字幕）      |  int32   |      |
            | voice         |     voice语音      |  string   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "dialogues": [
            {
                "content": "",
                "icon": "",
                "id": 0,
                "name": "",
                "plotId": 0,
                "roleType": 0,
                "seq": 0,
                "speed": 0,
                "type": 0,
                "voice": ""
            }
        ],
        "icon": "",
        "memo": "",
        "name": "",
        "plotId": 0,
        "progress": 0,
        "seq": 0,
        "speed": 0,
        "status": 0,
        "totalProgress": 0,
        "type": 0
    },
    "message": "",
    "msg": ""
}
```



--------分割线
完成剧情

**接口地址** `/pasture/app/plot/finish`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            | plotId         |      plotId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```


