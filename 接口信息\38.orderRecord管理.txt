orderRecord新增

**接口地址** `/pasture/order/record/add`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| orderRecord         |      orderRecord   |     body        |       true      | com.qihui.pasture.model.OrderRecord   | com.qihui.pasture.model.OrderRecord     |
            



**schema属性说明**
  
**com.qihui.pasture.model.OrderRecord**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| appCode  | appCode |   body    |   false   |string  |       |
| createTime  | createTime创建时间 |   body    |   false   |date-time  |       |
| finishTime  | finishTime完成时间 |   body    |   false   |date-time  |       |
| id  | id |   body    |   false   |int64  |       |
| level  | level任务等级 1高级 2中级 3低级 |   body    |   false   |int32  |       |
| playerId  | playerId |   body    |   false   |int64  |       |
| status  | status状态 1未接单 2未完成 3已完成 4已领奖 |   body    |   false   |int32  |       |
| takeTime  | takeTime接单时间 |   body    |   false   |date-time  |       |
| templateId  | templateId模板id |   body    |   false   |int64  |       |
| updateTime  | updateTime更新时间 |   body    |   false   |date-time  |       |
| userId  | userId用户id |   body    |   false   |string  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



---------------分割线
orderRecord删除

**接口地址** `/pasture/order/record/delete`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



---------------分割线
orderRecord获取详情

**接口地址** `/pasture/order/record/detail`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«com.qihui.pasture.model.OrderRecord»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    com.qihui.pasture.model.OrderRecord   |   com.qihui.pasture.model.OrderRecord    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.OrderRecord**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| appCode         |     appCode      |  string   |      |
            | createTime         |     createTime创建时间      |  date-time   |      |
            | finishTime         |     finishTime完成时间      |  date-time   |      |
            | id         |     id      |  int64   |      |
            | level         |     level任务等级 1高级 2中级 3低级      |  int32   |      |
            | playerId         |     playerId      |  int64   |      |
            | status         |     status状态 1未接单 2未完成 3已完成 4已领奖      |  int32   |      |
            | takeTime         |     takeTime接单时间      |  date-time   |      |
            | templateId         |     templateId模板id      |  int64   |      |
            | updateTime         |     updateTime更新时间      |  date-time   |      |
            | userId         |     userId用户id      |  string   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "appCode": "",
        "createTime": "",
        "finishTime": "",
        "id": 0,
        "level": 0,
        "playerId": 0,
        "status": 0,
        "takeTime": "",
        "templateId": 0,
        "updateTime": "",
        "userId": ""
    },
    "message": "",
    "msg": ""
}
```



---------------分割线
orderRecord获取列表

**接口地址** `/pasture/order/record/list`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| orderRecord         |      orderRecord   |     body        |       true      | com.qihui.pasture.model.OrderRecord   | com.qihui.pasture.model.OrderRecord     |
            | page         |      page   |     query        |       false      | integer   |      |
            | size         |      size   |     query        |       false      | integer   |      |
            



**schema属性说明**
  
**com.qihui.pasture.model.OrderRecord**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| appCode  | appCode |   body    |   false   |string  |       |
| createTime  | createTime创建时间 |   body    |   false   |date-time  |       |
| finishTime  | finishTime完成时间 |   body    |   false   |date-time  |       |
| id  | id |   body    |   false   |int64  |       |
| level  | level任务等级 1高级 2中级 3低级 |   body    |   false   |int32  |       |
| playerId  | playerId |   body    |   false   |int64  |       |
| status  | status状态 1未接单 2未完成 3已完成 4已领奖 |   body    |   false   |int32  |       |
| takeTime  | takeTime接单时间 |   body    |   false   |date-time  |       |
| templateId  | templateId模板id |   body    |   false   |int64  |       |
| updateTime  | updateTime更新时间 |   body    |   false   |date-time  |       |
| userId  | userId用户id |   body    |   false   |string  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«com.qihui.pasture.model.OrderRecord»»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   com.qihui.pasture.model.OrderRecord    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.OrderRecord**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| appCode         |     appCode      |  string   |      |
            | createTime         |     createTime创建时间      |  date-time   |      |
            | finishTime         |     finishTime完成时间      |  date-time   |      |
            | id         |     id      |  int64   |      |
            | level         |     level任务等级 1高级 2中级 3低级      |  int32   |      |
            | playerId         |     playerId      |  int64   |      |
            | status         |     status状态 1未接单 2未完成 3已完成 4已领奖      |  int32   |      |
            | takeTime         |     takeTime接单时间      |  date-time   |      |
            | templateId         |     templateId模板id      |  int64   |      |
            | updateTime         |     updateTime更新时间      |  date-time   |      |
            | userId         |     userId用户id      |  string   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "appCode": "",
            "createTime": "",
            "finishTime": "",
            "id": 0,
            "level": 0,
            "playerId": 0,
            "status": 0,
            "takeTime": "",
            "templateId": 0,
            "updateTime": "",
            "userId": ""
        }
    ],
    "message": "",
    "msg": ""
}
```



---------------分割线
orderRecordg更新

**接口地址** `/pasture/order/record/update`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| orderRecord         |      orderRecord   |     body        |       true      | com.qihui.pasture.model.OrderRecord   | com.qihui.pasture.model.OrderRecord     |
            



**schema属性说明**
  
**com.qihui.pasture.model.OrderRecord**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| appCode  | appCode |   body    |   false   |string  |       |
| createTime  | createTime创建时间 |   body    |   false   |date-time  |       |
| finishTime  | finishTime完成时间 |   body    |   false   |date-time  |       |
| id  | id |   body    |   false   |int64  |       |
| level  | level任务等级 1高级 2中级 3低级 |   body    |   false   |int32  |       |
| playerId  | playerId |   body    |   false   |int64  |       |
| status  | status状态 1未接单 2未完成 3已完成 4已领奖 |   body    |   false   |int32  |       |
| takeTime  | takeTime接单时间 |   body    |   false   |date-time  |       |
| templateId  | templateId模板id |   body    |   false   |int64  |       |
| updateTime  | updateTime更新时间 |   body    |   false   |date-time  |       |
| userId  | userId用户id |   body    |   false   |string  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```


