{"name": "admin-clone", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "build-fast": "vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "api:proxy": "node scripts/switch-api.js proxy", "api:direct": "node scripts/switch-api.js direct", "api:status": "node scripts/switch-api.js"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.6", "axios": "^1.10.0", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1", "xlsx": "^0.18.5"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^8.0.4", "terser": "^5.43.1", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}