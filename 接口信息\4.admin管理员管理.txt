新增
**接口地址** `/pasture/admin/admin/add`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| parameters         |      parameters   |     body        |       true      | 账号注册参数   | 账号注册参数     |
            



**schema属性说明**
  
**账号注册参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| account  | 登录账号 |   body    |   true   |string  |       |
| pwd  | 登录密码 |   body    |   true   |string  |       |
| roleIds  | 角色ids |   body    |   true   |string  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```
---------分割线
删除

**接口地址** `/pasture/admin/admin/delete`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```


--------分割线
获取详情

**接口地址** `/pasture/admin/admin/detail`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«AdminBo»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    AdminBo   |   AdminBo    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**AdminBo**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| adminRoles         |           |  array   | com.qimingxing.journey.model.AdminRole     |
            | email         |           |  string   |      |
            | id         |           |  int32   |      |
            | loginAccount         |           |  string   |      |
            | name         |           |  string   |      |
            | password         |           |  string   |      |
            | phoneNo         |           |  string   |      |
            | roleIds         |           |  string   |      |
            | roleName         |           |  string   |      |
            | salt         |           |  string   |      |
            | status         |           |  string   |      |
            | token         |           |  string   |      |
            

**com.qimingxing.journey.model.AdminRole**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| adminId         |     adminId      |  int32   |      |
            | id         |     id      |  int32   |      |
            | roleId         |     roleId      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "adminRoles": [
            {
                "adminId": 0,
                "id": 0,
                "roleId": 0
            }
        ],
        "email": "",
        "id": 0,
        "loginAccount": "",
        "name": "",
        "password": "",
        "phoneNo": "",
        "roleIds": "",
        "roleName": "",
        "salt": "",
        "status": "",
        "token": ""
    },
    "message": "",
    "msg": ""
}
```



--------分割线
获取管理员列表

**接口地址** `/pasture/admin/admin/list`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| params         |      params   |     body        |       true      | AdminParams   | AdminParams     |
            



**schema属性说明**
  
**AdminParams**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| adminRoles  |  |   body    |   false   |array  | com.qimingxing.journey.model.AdminRole      |
| email  |  |   body    |   false   |string  |       |
| endTime  |  |   body    |   false   |string  |       |
| id  |  |   body    |   false   |int32  |       |
| loginAccount  |  |   body    |   false   |string  |       |
| name  |  |   body    |   false   |string  |       |
| page  |  |   body    |   false   |int32  |       |
| password  |  |   body    |   false   |string  |       |
| phoneNo  |  |   body    |   false   |string  |       |
| roleIds  |  |   body    |   false   |string  |       |
| roleName  |  |   body    |   false   |string  |       |
| salt  |  |   body    |   false   |string  |       |
| size  |  |   body    |   false   |int32  |       |
| startTime  |  |   body    |   false   |string  |       |
| status  |  |   body    |   false   |string  |       |
| token  |  |   body    |   false   |string  |       |

**com.qimingxing.journey.model.AdminRole**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| adminId  | adminId |   body    |   false   |int32  |       |
| id  | id |   body    |   false   |int32  |       |
| roleId  | roleId |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«PageInfo«com.qimingxing.journey.model.Admin»»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    PageInfo«com.qimingxing.journey.model.Admin»   |   PageInfo«com.qimingxing.journey.model.Admin»    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**PageInfo«com.qimingxing.journey.model.Admin»**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| endRow         |           |  int32   |      |
            | firstPage         |           |  int32   |      |
            | hasNextPage         |           |  boolean   |      |
            | hasPreviousPage         |           |  boolean   |      |
            | isFirstPage         |           |  boolean   |      |
            | isLastPage         |           |  boolean   |      |
            | lastPage         |           |  int32   |      |
            | list         |           |  array   | com.qimingxing.journey.model.Admin     |
            | navigateFirstPage         |           |  int32   |      |
            | navigateLastPage         |           |  int32   |      |
            | navigatePages         |           |  int32   |      |
            | navigatepageNums         |           |  array   |      |
            | nextPage         |           |  int32   |      |
            | orderBy         |           |  string   |      |
            | pageNum         |           |  int32   |      |
            | pageSize         |           |  int32   |      |
            | pages         |           |  int32   |      |
            | prePage         |           |  int32   |      |
            | size         |           |  int32   |      |
            | startRow         |           |  int32   |      |
            | total         |           |  int64   |      |
            

**com.qimingxing.journey.model.Admin**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| companyId         |     companyId公司id      |  string   |      |
            | createTime         |     createTime创建时间      |  date-time   |      |
            | createUserId         |     createUserId创建人      |  string   |      |
            | email         |     email邮箱      |  string   |      |
            | id         |     id      |  int32   |      |
            | loginAccount         |     loginAccount登录账号      |  string   |      |
            | name         |     name姓名      |  string   |      |
            | password         |     password密码      |  string   |      |
            | phoneNo         |     phoneNo手机号码      |  string   |      |
            | roleName         |     roleName      |  string   |      |
            | salt         |     salt密码盐      |  string   |      |
            | status         |     status状态 0 正常 1注销       |  string   |      |
            | token         |     token      |  string   |      |
            | updateTime         |     updateTime更新时间      |  date-time   |      |
            | updateUserId         |     updateUserId更新人      |  string   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "endRow": 0,
        "firstPage": 0,
        "hasNextPage": true,
        "hasPreviousPage": true,
        "isFirstPage": true,
        "isLastPage": true,
        "lastPage": 0,
        "list": [
            {
                "companyId": "",
                "createTime": "",
                "createUserId": "",
                "email": "",
                "id": 0,
                "loginAccount": "",
                "name": "",
                "password": "",
                "phoneNo": "",
                "roleName": "",
                "salt": "",
                "status": "",
                "token": "",
                "updateTime": "",
                "updateUserId": ""
            }
        ],
        "navigateFirstPage": 0,
        "navigateLastPage": 0,
        "navigatePages": 0,
        "navigatepageNums": [],
        "nextPage": 0,
        "orderBy": "",
        "pageNum": 0,
        "pageSize": 0,
        "pages": 0,
        "prePage": 0,
        "size": 0,
        "startRow": 0,
        "total": 0
    },
    "message": "",
    "msg": ""
}
```



--------分割线
重置密码

**接口地址** `/pasture/admin/admin/restpwd`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| parameters         |      parameters   |     body        |       true      | 重置密码参数   | 重置密码参数     |
            



**schema属性说明**
  
**重置密码参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| adminId  | 管理员id |   body    |   true   |int32  |       |
| pwd  | 登录密码 |   body    |   true   |string  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------分割线
状态改变：启用禁用

**接口地址** `/pasture/admin/admin/status`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------分割线
更新

**接口地址** `/pasture/admin/admin/update`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| user         |      user   |     body        |       true      | AdminParams   | AdminParams     |
            



**schema属性说明**
  
**AdminParams**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| adminRoles  |  |   body    |   false   |array  | com.qimingxing.journey.model.AdminRole      |
| email  |  |   body    |   false   |string  |       |
| endTime  |  |   body    |   false   |string  |       |
| id  |  |   body    |   false   |int32  |       |
| loginAccount  |  |   body    |   false   |string  |       |
| name  |  |   body    |   false   |string  |       |
| page  |  |   body    |   false   |int32  |       |
| password  |  |   body    |   false   |string  |       |
| phoneNo  |  |   body    |   false   |string  |       |
| roleIds  |  |   body    |   false   |string  |       |
| roleName  |  |   body    |   false   |string  |       |
| salt  |  |   body    |   false   |string  |       |
| size  |  |   body    |   false   |int32  |       |
| startTime  |  |   body    |   false   |string  |       |
| status  |  |   body    |   false   |string  |       |
| token  |  |   body    |   false   |string  |       |

**com.qimingxing.journey.model.AdminRole**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| adminId  | adminId |   body    |   false   |int32  |       |
| id  | id |   body    |   false   |int32  |       |
| roleId  | roleId |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



