import{_ as a}from"./index-DvCt7Fuw.js";import{d as e,f as t,r as l,c as o,o as n,a1 as s,a0 as i,k as d,_ as u,S as r,V as c,G as m,u as v}from"./vendor-Dhi-hotu.js";import{a as y}from"./statistics-KYvH5j6t.js";import{h as p}from"./dateFormat-DmnmjEpg.js";import{f as g,d as _,R as k,e as f}from"./antd-DSyIu0Jx.js";import{_ as b}from"./_plugin-vue_export-helper-xcdYWU4n.js";const h={class:"statistics-board"},w={class:"page-header"},C={class:"header-actions"},x={class:"data-content"},z={class:"table-header"},T={class:"data-content"},S={class:"table-header"},I={class:"data-content"},$={class:"table-header"},H={class:"data-content"},U={class:"table-header"},A={class:"data-content"},D={class:"table-header"},j={class:"data-content"},Y={class:"table-header"},V={class:"data-content"},R={class:"table-header"},L={class:"data-content"},M={class:"table-header"},E={class:"data-content"},K={class:"table-header"},B={class:"data-content"},F={class:"table-header"},O=b(e({__name:"StatisticsBoard",setup(e){const b=t(!1),O=t(!1),P=t(null),G=t("userTotal"),J=t({}),N=l({startTime:"",endTime:"",type:1}),q={userTotal:{name:"用户总数",dimensionId:189},newUsers:{name:"新增用户数",dimensionId:190},activeUsers:{name:"活跃用户数",dimensionId:191},payAmount:{name:"付费金额",dimensionId:192},payUsers:{name:"付费人数",dimensionId:193},incomeAmount:{name:"入账金额",dimensionId:194},incomeCount:{name:"入账笔数",dimensionId:195},outcomeAmount:{name:"出账金额",dimensionId:196},outcomeCount:{name:"出账笔数",dimensionId:197},retention:{name:"次日留存率",dimensionId:198}},Q=o(()=>{const a=(J.value[G.value]||[]).find(a=>a.name&&a.name.includes("汇总"));return a&&a.statistics?a.statistics.map((a,e)=>({key:a.id||e,cutDay:a.cutDay,statisticsValue:a.statisticsValue,proportion:a.proportion})):[]}),W=a=>["userTotal","newUsers","activeUsers","payUsers"].includes(a)?{dateHeader:"日期",valueHeader:"人数"}:["payAmount","incomeAmount","outcomeAmount"].includes(a)?{dateHeader:"日期",valueHeader:"金额"}:["incomeCount","outcomeCount"].includes(a)?{dateHeader:"日期",valueHeader:"笔数"}:["retention"].includes(a)?{dateHeader:"日期",valueHeader:"留存率"}:{dateHeader:"日期",valueHeader:"数值"},X=o(()=>{const a=W(G.value);return[{title:a.dateHeader,dataIndex:"cutDay",key:"cutDay",width:100},{title:a.valueHeader,dataIndex:"statisticsValue",key:"statisticsValue",width:80,align:"right",customRender:({text:a})=>null==a?"0":a.toLocaleString()}]}),Z=a=>{p(a,(a,e)=>{N.startTime=a||"",N.endTime=e||"",a&&e&&ta()})},aa=a=>{G.value=a,console.log("标签页切换到:",a),J.value[a]||ea(a)},ea=async a=>{const e=q[a];if(e){b.value=!0;try{const t={id:e.dimensionId,startTime:N.startTime,endTime:N.endTime,type:N.type};console.log(`正在获取${e.name}数据，简化参数:`,t);const l=await y(t);if(console.log(`${e.name}数据响应:`,l),console.log(`${e.name}响应数据类型:`,typeof l.data),console.log(`${e.name}响应数据结构:`,JSON.stringify(l.data,null,2)),l.data&&l.data.sections){console.log(`${e.name}使用sections数据`);const t=l.data.sections.map((a,e)=>({...a,key:a.id||e,total:a.total||0}));J.value[a]=t}else l.data&&Array.isArray(l.data)?(console.log(`${e.name}使用数组数据`),J.value[a]=l.data):(console.warn(`${e.name}数据格式不符合预期:`,l.data),console.warn(`${e.name}设置为空数组`),J.value[a]=[{key:1,name:"暂无数据",total:0,memo:"当前时间范围内无数据"}])}catch(t){console.error(`获取${e.name}数据失败:`,t),_.error(`获取${e.name}数据失败`),J.value[a]=[{key:1,name:"2024-01-01",total:1e3,memo:"示例数据"},{key:2,name:"2024-01-02",total:1200,memo:"示例数据"},{key:3,name:"2024-01-03",total:980,memo:"示例数据"}]}finally{b.value=!1}}},ta=()=>{J.value={},ea(G.value)},la=async()=>{const e=Q.value;if(e&&0!==e.length){O.value=!0;try{const t=q[G.value],l=`${t.name}_${g().format("YYYY-MM-DD_HH-mm-ss")}.xlsx`,o=W(G.value),n=e.map(a=>({[o.dateHeader]:a.cutDay||"-",[o.valueHeader]:a.statisticsValue||0})),s=await a(()=>import("./xlsx-DfDjAMCE.js"),[],import.meta.url),i=s.utils.book_new(),d=s.utils.json_to_sheet(n);d["!cols"]=[{wch:20},{wch:15}],s.utils.book_append_sheet(i,d,t.name),s.writeFile(i,l),_.success(`成功导出 ${e.length} 条${t.name}数据`)}catch(t){console.error("导出数据失败:",t),_.error("导出数据失败")}finally{O.value=!1}}else _.warning("当前标签页没有数据可导出")},oa=async a=>{try{O.value=!0;const e=q[a];if(!e)return void _.error("未找到对应的数据配置");const t=new URLSearchParams({id:e.dimensionId.toString()});N.startTime&&N.endTime&&(t.append("startTime",N.startTime),t.append("endTime",N.endTime)),N.type&&t.append("timeInterval",N.type.toString());const l=`https://api.zj7hui.com/pasture/admin/spectaculars/report?${t.toString()}`;console.log(`${e.name}导出URL:`,l);const o=document.createElement("a");o.href=l,o.target="_blank",document.body.appendChild(o),o.click(),document.body.removeChild(o),_.success(`正在导出${e.name}数据，请稍候...`)}catch(e){console.error("导出数据失败:",e),_.error("导出数据失败")}finally{O.value=!1}};return n(async()=>{const a=g(),e=a.subtract(6,"day");P.value=[e,a],Z(P.value),console.log("StatisticsBoard初始化完成，直接加载数据"),ea(G.value)}),(a,e)=>{const t=r("a-button"),l=r("a-range-picker"),o=r("a-form-item"),n=r("a-form"),y=r("a-card"),p=r("a-table"),g=r("a-tab-pane"),_=r("a-tabs");return c(),s("div",h,[i("div",w,[e[14]||(e[14]=i("h2",null,"数据看板",-1)),i("div",C,[d(t,{onClick:ta,loading:b.value,type:"primary"},{icon:u(()=>[d(v(k))]),default:u(()=>[e[12]||(e[12]=m(" 刷新数据 ",-1))]),_:1,__:[12]},8,["loading"]),d(t,{onClick:la,loading:O.value,type:"default"},{icon:u(()=>[d(v(f))]),default:u(()=>[e[13]||(e[13]=m(" 导出 ",-1))]),_:1,__:[13]},8,["loading"])])]),d(y,{class:"filter-card",style:{"margin-bottom":"20px"}},{default:u(()=>[d(n,{layout:"inline"},{default:u(()=>[d(o,{label:"时间范围"},{default:u(()=>[d(l,{value:P.value,"onUpdate:value":e[0]||(e[0]=a=>P.value=a),format:"YYYY-MM-DD",onChange:Z,style:{width:"240px"}},null,8,["value"])]),_:1})]),_:1})]),_:1}),d(y,null,{default:u(()=>[d(_,{activeKey:G.value,"onUpdate:activeKey":e[11]||(e[11]=a=>G.value=a),onChange:aa},{default:u(()=>[d(g,{key:"userTotal",tab:"用户总数"},{default:u(()=>[i("div",x,[i("div",z,[d(t,{type:"primary",size:"small",onClick:e[1]||(e[1]=a=>oa("userTotal")),loading:O.value,style:{"margin-bottom":"16px"}},{icon:u(()=>[d(v(f))]),default:u(()=>[e[15]||(e[15]=m(" 导出数据 ",-1))]),_:1,__:[15]},8,["loading"])]),d(p,{columns:X.value,"data-source":Q.value,loading:b.value,pagination:{pageSize:10},"row-key":"key"},null,8,["columns","data-source","loading"])])]),_:1}),d(g,{key:"newUsers",tab:"新增用户数"},{default:u(()=>[i("div",T,[i("div",S,[d(t,{type:"primary",size:"small",onClick:e[2]||(e[2]=a=>oa("newUsers")),loading:O.value,style:{"margin-bottom":"16px"}},{icon:u(()=>[d(v(f))]),default:u(()=>[e[16]||(e[16]=m(" 导出数据 ",-1))]),_:1,__:[16]},8,["loading"])]),d(p,{columns:X.value,"data-source":Q.value,loading:b.value,pagination:{pageSize:10},"row-key":"key"},null,8,["columns","data-source","loading"])])]),_:1}),d(g,{key:"activeUsers",tab:"活跃用户数"},{default:u(()=>[i("div",I,[i("div",$,[d(t,{type:"primary",size:"small",onClick:e[3]||(e[3]=a=>oa("activeUsers")),loading:O.value,style:{"margin-bottom":"16px"}},{icon:u(()=>[d(v(f))]),default:u(()=>[e[17]||(e[17]=m(" 导出数据 ",-1))]),_:1,__:[17]},8,["loading"])]),d(p,{columns:X.value,"data-source":Q.value,loading:b.value,pagination:{pageSize:10},"row-key":"key"},null,8,["columns","data-source","loading"])])]),_:1}),d(g,{key:"payAmount",tab:"付费金额"},{default:u(()=>[i("div",H,[i("div",U,[d(t,{type:"primary",size:"small",onClick:e[4]||(e[4]=a=>oa("payAmount")),loading:O.value,style:{"margin-bottom":"16px"}},{icon:u(()=>[d(v(f))]),default:u(()=>[e[18]||(e[18]=m(" 导出数据 ",-1))]),_:1,__:[18]},8,["loading"])]),d(p,{columns:X.value,"data-source":Q.value,loading:b.value,pagination:{pageSize:10},"row-key":"key"},null,8,["columns","data-source","loading"])])]),_:1}),d(g,{key:"payUsers",tab:"付费人数"},{default:u(()=>[i("div",A,[i("div",D,[d(t,{type:"primary",size:"small",onClick:e[5]||(e[5]=a=>oa("payUsers")),loading:O.value,style:{"margin-bottom":"16px"}},{icon:u(()=>[d(v(f))]),default:u(()=>[e[19]||(e[19]=m(" 导出数据 ",-1))]),_:1,__:[19]},8,["loading"])]),d(p,{columns:X.value,"data-source":Q.value,loading:b.value,pagination:{pageSize:10},"row-key":"key"},null,8,["columns","data-source","loading"])])]),_:1}),d(g,{key:"incomeAmount",tab:"入账金额"},{default:u(()=>[i("div",j,[i("div",Y,[d(t,{type:"primary",size:"small",onClick:e[6]||(e[6]=a=>oa("incomeAmount")),loading:O.value,style:{"margin-bottom":"16px"}},{icon:u(()=>[d(v(f))]),default:u(()=>[e[20]||(e[20]=m(" 导出数据 ",-1))]),_:1,__:[20]},8,["loading"])]),d(p,{columns:X.value,"data-source":Q.value,loading:b.value,pagination:{pageSize:10},"row-key":"key"},null,8,["columns","data-source","loading"])])]),_:1}),d(g,{key:"incomeCount",tab:"入账笔数"},{default:u(()=>[i("div",V,[i("div",R,[d(t,{type:"primary",size:"small",onClick:e[7]||(e[7]=a=>oa("incomeCount")),loading:O.value,style:{"margin-bottom":"16px"}},{icon:u(()=>[d(v(f))]),default:u(()=>[e[21]||(e[21]=m(" 导出数据 ",-1))]),_:1,__:[21]},8,["loading"])]),d(p,{columns:X.value,"data-source":Q.value,loading:b.value,pagination:{pageSize:10},"row-key":"key"},null,8,["columns","data-source","loading"])])]),_:1}),d(g,{key:"outcomeAmount",tab:"出账金额"},{default:u(()=>[i("div",L,[i("div",M,[d(t,{type:"primary",size:"small",onClick:e[8]||(e[8]=a=>oa("outcomeAmount")),loading:O.value,style:{"margin-bottom":"16px"}},{icon:u(()=>[d(v(f))]),default:u(()=>[e[22]||(e[22]=m(" 导出数据 ",-1))]),_:1,__:[22]},8,["loading"])]),d(p,{columns:X.value,"data-source":Q.value,loading:b.value,pagination:{pageSize:10},"row-key":"key"},null,8,["columns","data-source","loading"])])]),_:1}),d(g,{key:"outcomeCount",tab:"出账笔数"},{default:u(()=>[i("div",E,[i("div",K,[d(t,{type:"primary",size:"small",onClick:e[9]||(e[9]=a=>oa("outcomeCount")),loading:O.value,style:{"margin-bottom":"16px"}},{icon:u(()=>[d(v(f))]),default:u(()=>[e[23]||(e[23]=m(" 导出数据 ",-1))]),_:1,__:[23]},8,["loading"])]),d(p,{columns:X.value,"data-source":Q.value,loading:b.value,pagination:{pageSize:10},"row-key":"key"},null,8,["columns","data-source","loading"])])]),_:1}),d(g,{key:"retention",tab:"次日留存率"},{default:u(()=>[i("div",B,[i("div",F,[d(t,{type:"primary",size:"small",onClick:e[10]||(e[10]=a=>oa("retention")),loading:O.value,style:{"margin-bottom":"16px"}},{icon:u(()=>[d(v(f))]),default:u(()=>[e[24]||(e[24]=m(" 导出数据 ",-1))]),_:1,__:[24]},8,["loading"])]),d(p,{columns:X.value,"data-source":Q.value,loading:b.value,pagination:{pageSize:10},"row-key":"key"},null,8,["columns","data-source","loading"])])]),_:1})]),_:1},8,["activeKey"])]),_:1})])}}}),[["__scopeId","data-v-da4a8a38"]]);export{O as default};
