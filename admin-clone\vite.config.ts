import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
  plugins: [
    vue({
      template: {
        compilerOptions: {
          // 启用模板编译
          isCustomElement: (tag) => false
        }
      }
    }),
    vueDevTools(), // 开发工具
  ],
  esbuild: {
    target: 'es2022'
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      // 关键：配置Vue使用完整版本（包含模板编译器）
      'vue': 'vue/dist/vue.esm-bundler.js'
    },
  },
  base: './', // 使用相对路径，适配各种部署环境
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          antd: ['ant-design-vue', '@ant-design/icons-vue']
        }
      },
      onwarn(warning, warn) {
        // 忽略TypeScript相关警告
        if (warning.code === 'UNRESOLVED_IMPORT') return
        warn(warning)
      }
    }
  },
  server: {
    // 根据环境变量决定是否启用代理
    proxy: env.VITE_API_MODE === 'proxy' ? {
      '/pasture': {
        target: env.VITE_PROXY_TARGET || 'https://api.zj7hui.com',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/pasture/, '/pasture')
      }
    } : undefined
  }
}
})
