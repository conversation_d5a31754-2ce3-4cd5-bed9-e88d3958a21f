# 管理后台系统部署说明

## 📦 打包完成

项目已成功打包到 `dist` 目录，可以直接部署到服务器。

## 🔧 修复的部署问题

### v2.0 更新 (解决线上部署问题)
- ✅ **使用相对路径**: `base: './'` 适配各种部署环境
- ✅ **Hash路由模式**: 无需服务器路由配置，避免404错误
- ✅ **资源路径修复**: 所有资源使用 `./` 相对路径
- ✅ **添加测试页面**: `test.html` 用于验证部署状态

## 🔧 配置切换功能

### 开发环境API模式切换

项目支持两种API访问模式，可以通过以下命令快速切换：

```bash
# 切换到代理模式（开发时使用本地代理）
npm run api:proxy

# 切换到直接访问模式（直接访问服务器API）
npm run api:direct

# 查看当前API配置状态
npm run api:status
```

### 配置说明

- **代理模式 (proxy)**：开发环境使用 `/pasture` 代理到 `https://api.zj7hui.com`
- **直接访问模式 (direct)**：直接访问 `https://api.zj7hui.com/pasture`

## 🚀 部署步骤

### 1. 上传文件
将 `dist` 目录下的所有文件上传到服务器的网站根目录

### 2. 测试部署
访问 `your-domain.com/test.html` 验证部署状态

### 3. 服务器配置 (可选)
由于使用Hash路由模式，无需特殊服务器配置。但如果需要，可以配置：

#### Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/your/dist;
    index index.html;

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### Apache 配置示例
在 `dist` 目录创建 `.htaccess` 文件：
```apache
RewriteEngine On
RewriteBase /
RewriteRule ^index\.html$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]
```

## 📋 打包信息

- **打包时间**: 20.16秒
- **总文件大小**: ~1.6MB (压缩后 ~500KB)
- **主要组件**:
  - Vue 3 + TypeScript
  - Ant Design Vue
  - Vue Router
  - Pinia
  - Axios

## 🔗 API配置

生产环境已配置为直接访问服务器API：
- **API地址**: `https://api.zj7hui.com/pasture`
- **无需代理**: 生产环境直接访问，无跨域问题

## 🛠 开发环境

如需继续开发，可以：

```bash
# 启动开发服务器
npm run dev

# 切换API模式（根据需要）
npm run api:proxy   # 使用代理
npm run api:direct  # 直接访问

# 重新打包
npm run build-fast
```

## 📝 注意事项

1. **API访问**: 生产环境已配置为直接访问服务器API，无需额外配置
2. **路由支持**: 确保服务器支持SPA路由重定向
3. **HTTPS**: 建议生产环境使用HTTPS
4. **缓存**: 建议为静态资源配置适当的缓存策略

## 🎯 功能特性

- ✅ 用户管理
- ✅ 订单管理（含退款功能）
- ✅ 退款管理
- ✅ 实时统计
- ✅ 数据看板
- ✅ 响应式设计
- ✅ 权限控制
