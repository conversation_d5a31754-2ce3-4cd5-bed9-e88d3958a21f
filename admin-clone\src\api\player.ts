import api from './index'

// 玩家信息接口
export interface Player {
  id?: number // 玩家ID
  playerId?: number
  userId?: string
  account?: string // 账号
  appCode?: string // 应用编码/渠道标识
  outAccount?: string // 渠道账号
  nickname?: string
  sex?: number // 0女 1男
  level?: number
  levelName?: string
  exp?: number
  expValue?: number
  nextExpValue?: number
  coin?: number
  ore?: number
  vipOre?: number
  vim?: number
  vimLimit?: number
  wish?: number
  score?: number
  rankScore?: number
  isVip?: number
  vipStatus?: number
  vipType?: number
  vipEndTime?: string
  status?: number
  protectStatus?: number
  model?: string
  createTime?: string
  updateTime?: string
}

// 玩家查询参数 (根据admin接口文档)
export interface PlayerQueryParams {
  page?: number
  size?: number
  name?: string // 昵称 (接口文档中是name)
  playerId?: number // 玩家ID (搜索参数)
  account?: string // 账号
  appCode?: string // 应用渠道
}

// 玩家币信息
export interface PlayerCoin {
  coin?: number
  ore?: number
  vipOre?: number
  vim?: number
  vimLimit?: number
  wish?: number
}

// 玩家个人中心信息
export interface PlayerCenterInfo {
  player?: Player
  score?: number
}

// 玩家年级信息
export interface PlayerGrade {
  grade?: number
  gradeName?: string
}

// 操作选项
export interface OperationOption {
  code?: string
  coinType?: number
  coinValue?: number
  description?: string
  isVip?: number
  name?: string
  num?: number
  optId?: number
  packId?: number
  playerPackId?: number
  type?: number
}

// 操作列表响应
export interface OperationListResponse {
  checked?: boolean
  optList?: OperationOption[]
  redHot?: boolean
  remind?: string
  sort?: number
  tab?: string
}

// 获取玩家币信息
export const getPlayerCoin = (playerId: number) => {
  return api.get('/player/coin', { params: { playerId } })
}

// 获取玩家个人中心信息
export const getPlayerCenterInfo = (playerId: number) => {
  return api.get('/player/getPlayerCenterInfo', { params: { playerId } })
}

// 获取玩家年级信息
export const getPlayerGrade = (playerId: number) => {
  return api.get('/player/getPlayerGrade', { params: { playerId } })
}

// 获取操作列表
export const getOperationList = (playerId: number, version: string) => {
  return api.get('/player/player/operation/list', {
    params: { playerId, version }
  })
}

// 获取玩家详情
export const getPlayerDetail = (playerId: number) => {
  return api.get('/player/playerDetail', { params: { playerId } })
}

// 喂养操作
export const feedPlayer = (playerId: number, optId: number) => {
  return api.get('/player/playerInfo/feed', {
    params: { playerId, optId }
  })
}

// 获取红点信息
export const getPlayerReds = (playerId: number) => {
  return api.get('/player/queryReds', { params: { playerId } })
}

// 获取玩家列表 (这个接口可能需要根据实际后端调整)
export const getPlayerList = (params: PlayerQueryParams) => {
  return api.post('/admin/player/list', params)
}
