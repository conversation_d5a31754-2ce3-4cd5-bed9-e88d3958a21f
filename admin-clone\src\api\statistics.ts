import api from './index'

// 实时维度接口
export interface RealTimeDimension {
  id?: number
  memo?: string // 备注
  name?: string // 名称
  range?: number // 幅度
  rangeType?: number // 幅度类型 1上涨 2下跌
  total?: number // 当日累计
  unitType?: number // 单位类型：1秒 2个 3元
  weekRange?: number // 上周幅度
  weekRangeType?: number // 上周幅度类型 1上涨 2下跌
}

// 维度详情接口
export interface DimensionDetail {
  createName?: string
  createTime?: string
  id?: number
  memo?: string
  name?: string
  sections?: DimensionSection[]
  selected?: boolean
  spectacularsDimensionId?: number
  unitType?: number
  updateName?: string
  updateTime?: string
  viewType?: string
}

// 维度分段接口
export interface DimensionSection {
  cutDay?: string
  id?: number
  proportion?: number
  sectionName?: string
  statisticsValue?: number
  name?: string
  memo?: string
  total?: number
  key?: number | string
  statistics?: Array<{
    id?: number | null
    cutDay?: string
    statisticsValue?: number
    proportion?: number
  }>
}

// 统计查询参数
export interface StatisticsQuery {
  appCode?: string
  day?: string
  type?: number
}

// 手动统计参数
export interface ManualStatisticsParams {
  dimensionId?: number
  sectionId?: number
  time: string
}

// 获取维度列表
export const getDimensionList = (appCode: string) => {
  return api.get('/admin/realTimeReports/dimensionList', {
    params: { appCode }
  })
}

// 获取维度详情 - 简化参数版本
export const getDimensionDetail = (params: {
  id: number
  startTime: string
  endTime: string
  type: number
}) => {
  return api.post('/admin/spectaculars/dimensionDetail', params)
}

// 查询统计数据
export const queryStatistics = (query: StatisticsQuery) => {
  return api.post('/reports/query', query)
}

// 手动统计
export const manualStatistics = (params: ManualStatisticsParams) => {
  return api.get('/admin/manual/statistics', { params })
}

// 获取看板维度列表
export const getSpectacularsList = (params: { appCode?: string, type?: number }) => {
  return api.get('/admin/spectaculars/list', { params })
}

// 获取看板详情
export const getSpectacularsDetail = (params: { appCode: string, id: number }) => {
  return api.get('/admin/spectaculars/detail', { params })
}

// 看板维度详情参数
export interface SpectacularsDimensionDetailParams {
  appCode: string
  id: number
  startTime?: string
  endTime?: string
  type?: number // 统计类型 1按日统计 2按周统计 3按月统计 4按年统计
  timeInterval?: number
  granularity?: number // 颗粒度 1全部应用 2应用级 3渠道级
}

// 获取看板维度详情
export const getSpectacularsDimensionDetail = (params: SpectacularsDimensionDetailParams) => {
  return api.post('/admin/spectaculars/dimensionDetail', params)
}

// 获取昨日数据
export const getYesterdayData = () => {
  return api.post('/admin/spectaculars/yesterdayData')
}

// 导出维度报表参数接口
export interface ExportReportParams {
  id: number
  startTime?: string
  endTime?: string
  timeInterval?: string
}

// 导出维度报表
export const exportSpectacularsReport = (params: ExportReportParams) => {
  return api.get('/admin/spectaculars/report', {
    params,
    responseType: 'blob' // 用于下载文件
  })
}

// 应用渠道接口
export interface AppChannel {
  appCode: string
  appName: string
  status?: number
}

// 获取应用渠道列表
export const getAppChannelList = () => {
  return api.get('/admin/app/channels')
}
