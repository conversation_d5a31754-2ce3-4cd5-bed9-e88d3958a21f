导出

**接口地址** `/pasture/order/refund/export`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| appCode         |      应用编码   |     query        |       true      | string   |      |
            | endTime         |      结束时间   |     query        |       true      | string   |      |
            | payType         |      支付方式 1微信公众号 2小天才服务号 3线下支付 4小天才表端支付   |     query        |       true      | integer   |      |
            | startTime         |      开始时间   |     query        |       true      | string   |      |
            | type         |      订单类型 1所有订单 2出账订单   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

暂无




**响应示例**


```json

```



--------------分割线
通过订单号获取退款列表

**接口地址** `/pasture/order/refund/list`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| flowNo         |      flowNo   |     query        |       true      | string   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«com.qimingxing.journey.model.OrderRefund»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   com.qimingxing.journey.model.OrderRefund    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qimingxing.journey.model.OrderRefund**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| amount         |     amount退款金额      |  number   |      |
            | appCode         |     appCodeapp_code      |  string   |      |
            | createName         |     createName创建人      |  string   |      |
            | createTime         |     createTime创建时间      |  date-time   |      |
            | flowNo         |     flowNo订单号      |  string   |      |
            | id         |     id      |  int32   |      |
            | payType         |     payType支付方式 1微信公众号 2小天才服务号 3线下支付 4小天才表端支付  5小天才H5支付 6支付宝支付      |  int32   |      |
            | refundDesc         |     refundDesc退款说明(用户看)      |  string   |      |
            | refundSn         |     refundSn退款订单号      |  string   |      |
            | refundStatus         |     refundStatus退款状态 0失败  1发起成功  2实际到账      |  int32   |      |
            | returnDays         |     returnDays会员扣除天数      |  int32   |      |
            | successTime         |     successTime到款时间      |  date-time   |      |
            | type         |     type类型 1全部退款 2部分退款      |  int32   |      |
            | updateTime         |     updateTime      |  date-time   |      |
            | vipEndTime         |     vipEndTime退款后VIP到期时间      |  date-time   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "amount": 0,
            "appCode": "",
            "createName": "",
            "createTime": "",
            "flowNo": "",
            "id": 0,
            "payType": 0,
            "refundDesc": "",
            "refundSn": "",
            "refundStatus": 0,
            "returnDays": 0,
            "successTime": "",
            "type": 0,
            "updateTime": "",
            "vipEndTime": ""
        }
    ],
    "message": "",
    "msg": ""
}
```



--------------分割线
退款列表

**接口地址** `/pasture/order/refund/pageList`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| parameters         |      parameters   |     body        |       true      | 退款列表入参   | 退款列表入参     |
            



**schema属性说明**
  
**退款列表入参**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| appCode  | 应用编码 |   body    |   true   |string  |       |
| createName  | 退款员工名称 |   body    |   false   |string  |       |
| endTime  | 结束时间 |   body    |   false   |string  |       |
| flowNo  | 订单号 |   body    |   false   |string  |       |
| page  | 页码 |   body    |   false   |int32  |       |
| payType  | 支付方式 1微信公众号 2小天才服务号 3线下支付 4小天才表端支付 |   body    |   true   |int32  |       |
| refundSn  | 退款订单号 |   body    |   false   |string  |       |
| size  | 每页条数 |   body    |   false   |int32  |       |
| startTime  | 开始时间 |   body    |   false   |string  |       |
| status  | 退款状态 0退款失败  1退款中  2退款成功 |   body    |   false   |int32  |       |
| type  | 类型 1全款 2部分 |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«退款订单»»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   退款订单    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**退款订单**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| amount         |     amount退款金额      |  number   |      |
            | appCode         |     应用编号      |  string   |      |
            | createName         |     createName创建人      |  string   |      |
            | createTime         |     createTime创建时间      |  date-time   |      |
            | flowNo         |     flowNo订单号      |  string   |      |
            | id         |     id      |  int32   |      |
            | payType         |     支付方式 1微信公众号 2小天才服务号 3线下支付 4小天才表端支付      |  string   |      |
            | refundDesc         |     退款描述(显示给用户的话)      |  string   |      |
            | refundSn         |     refundSn退款单号      |  string   |      |
            | refundStatus         |     退款状态 0退款失败  1退款中  2退款成功      |  int32   |      |
            | returnDays         |     returnDays会员扣除天数      |  int32   |      |
            | successTime         |     successTime退款到账时间      |  date-time   |      |
            | type         |     type类型 1全部退款 2部分退款      |  int32   |      |
            | vipEndTime         |     VIP到期时间      |  date-time   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "amount": 0,
            "appCode": "",
            "createName": "",
            "createTime": "",
            "flowNo": "",
            "id": 0,
            "payType": "",
            "refundDesc": "",
            "refundSn": "",
            "refundStatus": 0,
            "returnDays": 0,
            "successTime": "",
            "type": 0,
            "vipEndTime": ""
        }
    ],
    "message": "",
    "msg": ""
}
```


