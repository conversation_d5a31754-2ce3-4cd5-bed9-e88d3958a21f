chess获取详情

**接口地址** `/pasture/chess/api/detail`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| chessId         |      chessId   |     query        |       true      | integer   |      |
            | playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«下棋活动对象»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    下棋活动对象   |   下棋活动对象    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**下棋活动对象**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| chessGrids         |     有奖品的格子      |  array   | 下棋活动格子     |
            | chessId         |     活动id      |  int64   |      |
            | level         |     当前步数      |  int32   |      |
            | name         |     活动名称      |  string   |      |
            | pets         |     宠物集合      |  array   | 下棋宠物     |
            | total         |     总格子数      |  int32   |      |
            

**下棋活动格子**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| chessId         |     活动id      |  int64   |      |
            | level         |     当前格子数      |  int32   |      |
            | name         |     格子名称      |  string   |      |
            | prizes         |     奖品      |  array   | 掉落物品明细     |
            | vipPrizes         |     vip奖品      |  array   | 掉落物品明细     |
            

**掉落物品明细**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| code         |     图标      |  string   |      |
            | name         |     名称      |  string   |      |
            | sourceId         |     sourceId道具id      |  int64   |      |
            | sourceType         |     sourceType类型 1背包方案 2背包道具 3主角经验 4能量币 5体力 6矿石 7宠物 8装备 9排位积分 10 金币      |  int32   |      |
            | type         |     类型 1普通掉落 2首次掉落      |  int32   |      |
            | upgrade         |     人物升级信息      |  升级后玩家信息   | 升级后玩家信息     |
            | value         |     num数量      |  int32   |      |
            

**升级后玩家信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coinType         |     收获代币类型： 1-金币，2-能量      |  int32   |      |
            | coinVal         |     收获代币值      |  int32   |      |
            | drops         |     物品掉落      |  array   | 掉落物品明细     |
            | level         |     人物等级      |  int32   |      |
            | player         |     玩家      |  玩家详情   | 玩家详情     |
            | score         |     评分      |  int32   |      |
            

**玩家详情**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | breed         |     动物养殖      |  动物养殖   | 动物养殖     |
            | coin         |     金币      |  int32   |      |
            | energy         |     能量      |  int32   |      |
            | equipageScore         |     装备评分      |  int32   |      |
            | equipages         |     装备      |  array   | 玩家装备     |
            | events         |     互动事件      |  array   | 互动事件     |
            | expValue         |     expValue当前经验值      |  int64   |      |
            | followWx         |     是否关注微信公众号 1-已关注 0-未关注      |  int32   |      |
            | icon         |     玩家头像图标地址      |  string   |      |
            | interacts         |     互动      |  array   | 互动事件     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | level         |     人物等级      |  int32   |      |
            | levelName         |     官职      |  string   |      |
            | model         |     角色模型      |  string   |      |
            | nextExpValue         |     nextExpValue下一个等级所需经验值      |  int64   |      |
            | nickname         |     玩家昵称      |  string   |      |
            | ore         |     粮草      |  int32   |      |
            | playerId         |     玩家id      |  int64   |      |
            | protectStatus         |     保护状态 1有保护 2无保护      |  int32   |      |
            | rankScore         |     排位积分      |  int32   |      |
            | score         |     总评分      |  int32   |      |
            | sex         |     玩家性别 1-男 2-女      |  int32   |      |
            | status         |     玩家状态 0-不可用 1-正常      |  int32   |      |
            | userId         |     用户id      |  string   |      |
            | vim         |     活力      |  int32   |      |
            | vimLimit         |     活力上限      |  int32   |      |
            | vipOre         |     vip粮草      |  int32   |      |
            | vipStatus         |     vip状态      |  int32   |      |
            | wish         |     许愿值      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**动物养殖**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| animalId         |     animalId动物id      |  int64   |      |
            | breedTime         |     breedTime养殖时间      |  date-time   |      |
            | createTime         |     createTime      |  date-time   |      |
            | downTime         |     倒计时      |  int64   |      |
            | modelSpine         |     动效      |  string   |      |
            | petId         |     petId宠物id      |  int64   |      |
            | pickStatus         |     是否可以采摘 1可以 2自己不能偷 3好友不能偷      |  int32   |      |
            | playerBreedId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态1 成长中 2待收获 3已收获      |  int32   |      |
            | suspendTime         |     暂停时间 不为空就是被暂停      |  date-time   |      |
            

**玩家装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| id         |     id      |  int64   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | type         |     类型 1神兵 2宝马 3皮肤 4兵书 5虎符      |  int32   |      |
            

**互动事件**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coin         |     金币      |  int32   |      |
            | createTime         |     createTime      |  date-time   |      |
            | detail         |     详情      |  string   |      |
            | eventType         |     操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽  8便便 9蚊子 10老鼠      |  int32   |      |
            | eventVal         |     eventVal事件值      |  int32   |      |
            | finishVal         |     eventVal事件完成值      |  int32   |      |
            | icon         |     图片      |  string   |      |
            | playerEventId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            

**下棋宠物**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| evolutionType         |     evolutionType等阶类型（冗余）      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | level         |     level当前等级      |  int32   |      |
            | name         |     name      |  string   |      |
            | petId         |     petId      |  int64   |      |
            | playerPetId         |     玩家宠物id      |  int64   |      |
            | score         |     已掷分      |  int32   |      |
            | status         |     状态 1正常 2未解锁 3已掷      |  int32   |      |
            | type         |     类型 1普通 2稀有 3非凡 4完美      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "chessGrids": [
            {
                "chessId": 0,
                "level": 0,
                "name": "",
                "prizes": [
                    {
                        "code": "",
                        "name": "",
                        "sourceId": 0,
                        "sourceType": 0,
                        "type": 0,
                        "upgrade": {
                            "coinType": 0,
                            "coinVal": 0,
                            "drops": [
                                {}
                            ],
                            "level": 0,
                            "player": {
                                "attrs": [
                                    {
                                        "attrType": 0,
                                        "attrValue": 0,
                                        "type": 0
                                    }
                                ],
                                "breed": {
                                    "animalId": 0,
                                    "breedTime": "",
                                    "createTime": "",
                                    "downTime": 0,
                                    "modelSpine": "",
                                    "petId": 0,
                                    "pickStatus": 0,
                                    "playerBreedId": 0,
                                    "playerId": 0,
                                    "seq": 0,
                                    "status": 0,
                                    "suspendTime": ""
                                },
                                "coin": 0,
                                "energy": 0,
                                "equipageScore": 0,
                                "equipages": [
                                    {
                                        "id": 0,
                                        "model": "",
                                        "modelSpine": "",
                                        "name": "",
                                        "type": 0
                                    }
                                ],
                                "events": [
                                    {
                                        "coin": 0,
                                        "createTime": "",
                                        "detail": "",
                                        "eventType": 0,
                                        "eventVal": 0,
                                        "finishVal": 0,
                                        "icon": "",
                                        "playerEventId": 0,
                                        "playerId": 0
                                    }
                                ],
                                "expValue": 0,
                                "followWx": 0,
                                "icon": "",
                                "interacts": [
                                    {
                                        "coin": 0,
                                        "createTime": "",
                                        "detail": "",
                                        "eventType": 0,
                                        "eventVal": 0,
                                        "finishVal": 0,
                                        "icon": "",
                                        "playerEventId": 0,
                                        "playerId": 0
                                    }
                                ],
                                "isVip": 0,
                                "level": 0,
                                "levelName": "",
                                "model": "",
                                "nextExpValue": 0,
                                "nickname": "",
                                "ore": 0,
                                "playerId": 0,
                                "protectStatus": 0,
                                "rankScore": 0,
                                "score": 0,
                                "sex": 0,
                                "status": 0,
                                "userId": "",
                                "vim": 0,
                                "vimLimit": 0,
                                "vipOre": 0,
                                "vipStatus": 0,
                                "wish": 0
                            },
                            "score": 0
                        },
                        "value": 0
                    }
                ],
                "vipPrizes": [
                    {}
                ]
            }
        ],
        "chessId": 0,
        "level": 0,
        "name": "",
        "pets": [
            {
                "evolutionType": 0,
                "icon": "",
                "level": 0,
                "name": "",
                "petId": 0,
                "playerPetId": 0,
                "score": 0,
                "status": 0,
                "type": 0
            }
        ],
        "total": 0
    },
    "message": "",
    "msg": ""
}
```



--------分割线
走一步

**接口地址** `/pasture/chess/api/walk`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| chessId         |      chessId   |     query        |       true      | integer   |      |
            | playerId         |      playerId   |     query        |       true      | integer   |      |
            | playerPetId         |      playerPetId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«下棋走一步»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    下棋走一步   |   下棋走一步    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**下棋走一步**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| prizes         |     已领奖品      |  array   | 掉落物品明细     |
            | step         |     前进步数      |  int32   |      |
            | vipPrizes         |     未领VIP奖品      |  array   | 掉落物品明细     |
            

**掉落物品明细**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| code         |     图标      |  string   |      |
            | name         |     名称      |  string   |      |
            | sourceId         |     sourceId道具id      |  int64   |      |
            | sourceType         |     sourceType类型 1背包方案 2背包道具 3主角经验 4能量币 5体力 6矿石 7宠物 8装备 9排位积分 10 金币      |  int32   |      |
            | type         |     类型 1普通掉落 2首次掉落      |  int32   |      |
            | upgrade         |     人物升级信息      |  升级后玩家信息   | 升级后玩家信息     |
            | value         |     num数量      |  int32   |      |
            

**升级后玩家信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coinType         |     收获代币类型： 1-金币，2-能量      |  int32   |      |
            | coinVal         |     收获代币值      |  int32   |      |
            | drops         |     物品掉落      |  array   | 掉落物品明细     |
            | level         |     人物等级      |  int32   |      |
            | player         |     玩家      |  玩家详情   | 玩家详情     |
            | score         |     评分      |  int32   |      |
            

**玩家详情**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | breed         |     动物养殖      |  动物养殖   | 动物养殖     |
            | coin         |     金币      |  int32   |      |
            | energy         |     能量      |  int32   |      |
            | equipageScore         |     装备评分      |  int32   |      |
            | equipages         |     装备      |  array   | 玩家装备     |
            | events         |     互动事件      |  array   | 互动事件     |
            | expValue         |     expValue当前经验值      |  int64   |      |
            | followWx         |     是否关注微信公众号 1-已关注 0-未关注      |  int32   |      |
            | icon         |     玩家头像图标地址      |  string   |      |
            | interacts         |     互动      |  array   | 互动事件     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | level         |     人物等级      |  int32   |      |
            | levelName         |     官职      |  string   |      |
            | model         |     角色模型      |  string   |      |
            | nextExpValue         |     nextExpValue下一个等级所需经验值      |  int64   |      |
            | nickname         |     玩家昵称      |  string   |      |
            | ore         |     粮草      |  int32   |      |
            | playerId         |     玩家id      |  int64   |      |
            | protectStatus         |     保护状态 1有保护 2无保护      |  int32   |      |
            | rankScore         |     排位积分      |  int32   |      |
            | score         |     总评分      |  int32   |      |
            | sex         |     玩家性别 1-男 2-女      |  int32   |      |
            | status         |     玩家状态 0-不可用 1-正常      |  int32   |      |
            | userId         |     用户id      |  string   |      |
            | vim         |     活力      |  int32   |      |
            | vimLimit         |     活力上限      |  int32   |      |
            | vipOre         |     vip粮草      |  int32   |      |
            | vipStatus         |     vip状态      |  int32   |      |
            | wish         |     许愿值      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**动物养殖**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| animalId         |     animalId动物id      |  int64   |      |
            | breedTime         |     breedTime养殖时间      |  date-time   |      |
            | createTime         |     createTime      |  date-time   |      |
            | downTime         |     倒计时      |  int64   |      |
            | modelSpine         |     动效      |  string   |      |
            | petId         |     petId宠物id      |  int64   |      |
            | pickStatus         |     是否可以采摘 1可以 2自己不能偷 3好友不能偷      |  int32   |      |
            | playerBreedId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态1 成长中 2待收获 3已收获      |  int32   |      |
            | suspendTime         |     暂停时间 不为空就是被暂停      |  date-time   |      |
            

**玩家装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| id         |     id      |  int64   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | type         |     类型 1神兵 2宝马 3皮肤 4兵书 5虎符      |  int32   |      |
            

**互动事件**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coin         |     金币      |  int32   |      |
            | createTime         |     createTime      |  date-time   |      |
            | detail         |     详情      |  string   |      |
            | eventType         |     操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽  8便便 9蚊子 10老鼠      |  int32   |      |
            | eventVal         |     eventVal事件值      |  int32   |      |
            | finishVal         |     eventVal事件完成值      |  int32   |      |
            | icon         |     图片      |  string   |      |
            | playerEventId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "prizes": [
            {
                "code": "",
                "name": "",
                "sourceId": 0,
                "sourceType": 0,
                "type": 0,
                "upgrade": {
                    "coinType": 0,
                    "coinVal": 0,
                    "drops": [
                        {}
                    ],
                    "level": 0,
                    "player": {
                        "attrs": [
                            {
                                "attrType": 0,
                                "attrValue": 0,
                                "type": 0
                            }
                        ],
                        "breed": {
                            "animalId": 0,
                            "breedTime": "",
                            "createTime": "",
                            "downTime": 0,
                            "modelSpine": "",
                            "petId": 0,
                            "pickStatus": 0,
                            "playerBreedId": 0,
                            "playerId": 0,
                            "seq": 0,
                            "status": 0,
                            "suspendTime": ""
                        },
                        "coin": 0,
                        "energy": 0,
                        "equipageScore": 0,
                        "equipages": [
                            {
                                "id": 0,
                                "model": "",
                                "modelSpine": "",
                                "name": "",
                                "type": 0
                            }
                        ],
                        "events": [
                            {
                                "coin": 0,
                                "createTime": "",
                                "detail": "",
                                "eventType": 0,
                                "eventVal": 0,
                                "finishVal": 0,
                                "icon": "",
                                "playerEventId": 0,
                                "playerId": 0
                            }
                        ],
                        "expValue": 0,
                        "followWx": 0,
                        "icon": "",
                        "interacts": [
                            {
                                "coin": 0,
                                "createTime": "",
                                "detail": "",
                                "eventType": 0,
                                "eventVal": 0,
                                "finishVal": 0,
                                "icon": "",
                                "playerEventId": 0,
                                "playerId": 0
                            }
                        ],
                        "isVip": 0,
                        "level": 0,
                        "levelName": "",
                        "model": "",
                        "nextExpValue": 0,
                        "nickname": "",
                        "ore": 0,
                        "playerId": 0,
                        "protectStatus": 0,
                        "rankScore": 0,
                        "score": 0,
                        "sex": 0,
                        "status": 0,
                        "userId": "",
                        "vim": 0,
                        "vimLimit": 0,
                        "vipOre": 0,
                        "vipStatus": 0,
                        "wish": 0
                    },
                    "score": 0
                },
                "value": 0
            }
        ],
        "step": 0,
        "vipPrizes": [
            {}
        ]
    },
    "message": "",
    "msg": ""
}
```


