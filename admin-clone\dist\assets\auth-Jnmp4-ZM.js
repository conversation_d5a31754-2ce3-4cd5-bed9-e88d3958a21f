import{a7 as e,f as o}from"./vendor-Dhi-hotu.js";import{l as a,a as s}from"./auth-CDe9Kw3v.js";import{d as t}from"./antd-DSyIu0Jx.js";const n=e("auth",()=>{const e=o(localStorage.getItem("token")||""),n=o(null),r=o([]),c=o(!!e.value);return{token:e,userInfo:n,menuList:r,isLoggedIn:c,login:async o=>{try{console.log("尝试使用API登录:",o),console.log("当前环境:","生产环境");const s=await a(o);if(console.log("登录响应:",s),!s||"200"!==s.code&&200!==s.code&&"200"!==s.data?.code&&200!==s.data?.code){const e=s?.message||s?.msg||s?.data?.message||s?.data?.msg||"登录失败";return console.log("登录失败，服务器返回:",s),t.error(`登录失败: ${e}`),!1}{const a=s.data;return e.value=a?.token||"api-token-"+Date.now(),n.value=a||{name:o.account,account:o.account},c.value=!0,localStorage.setItem("token",e.value),t.success("登录成功"),!0}}catch(s){if(console.error("API登录失败:",s),console.error("错误详情:",{message:s.message,code:s.code,response:s.response?.data,status:s.response?.status}),s.response?.status>=500){const e=s.response.data,o=e?.message||e?.msg||"服务器内部错误";return t.error(`服务器错误: ${o}`),!1}if("ERR_NETWORK"===s.code||s.message?.includes("Network Error")||s.message?.includes("未能解析此远程名称")||s.message?.includes("getaddrinfo ENOTFOUND")||"ENOTFOUND"===s.code||0===s.response?.status)return console.log("检测到网络错误，启用演示模式"),t.warning("无法连接到API服务器，启用演示模式"),"admin"===o.account&&"123456"===o.pwd?(e.value="demo-token-"+Date.now(),n.value={name:"Admin (演示模式)",account:"admin"},c.value=!0,localStorage.setItem("token",e.value),t.success("登录成功 (演示模式)"),!0):(t.error("演示模式下，请使用账号: admin, 密码: 123456"),!1);{const e=s.response?.data?.message||s.response?.data?.msg||s.message||"登录失败";return t.error(`登录失败: ${e}`),!1}}},getMenu:async()=>{try{const e=await s();e.data&&(r.value=e.data)}catch(e){console.error("获取菜单失败:",e)}},logout:()=>{e.value="",n.value=null,r.value=[],c.value=!1,localStorage.removeItem("token"),window.location.href="#/login"}}});export{n as u};
