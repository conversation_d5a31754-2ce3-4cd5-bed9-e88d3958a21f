<template>
  <div>
    <a-select
      v-model:value="selectedValue"
      :placeholder="placeholder"
      :style="{ width: width }"
      :allowClear="allowClear"
      :loading="appStore.loading"
      @change="handleChange"
      show-search
      :filter-option="false"
    >
      <a-select-option value="">所有</a-select-option>
      <a-select-option
        v-for="option in appStore.appOptions"
        :key="option.value"
        :value="option.value"
      >
        {{ option.label }}
      </a-select-option>
    </a-select>

    <!-- 调试信息 -->
    <div v-if="showDebug" style="margin-top: 8px; font-size: 12px; color: #666;">
      <div>当前选中值: {{ selectedValue }}</div>
      <div>应用选项数量: {{ appStore.appOptions.length }}</div>
      <div>应用列表: {{ JSON.stringify(appStore.appOptions) }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useAppStore } from '../stores/app'

// Props
interface Props {
  modelValue?: string
  placeholder?: string
  width?: string
  allowClear?: boolean
  showDebug?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择应用渠道',
  width: '200px',
  allowClear: true,
  showDebug: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string | undefined]
  'change': [value: string | undefined]
}>()

// Store
const appStore = useAppStore()

// 内部状态
const selectedValue = ref<string | undefined>(props.modelValue)

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedValue.value = newValue
}, { immediate: true })

// 监听内部值变化
watch(selectedValue, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理选择变化
const handleChange = (value: string | undefined) => {
  emit('change', value)
}

// 组件挂载时初始化应用列表
onMounted(async () => {
  await appStore.initAppList()
})
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
