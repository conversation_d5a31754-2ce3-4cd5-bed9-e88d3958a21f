<template>
  <div class="statistics-board">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>数据看板</h2>
      <div class="header-actions">
        <a-button @click="refreshData" :loading="loading" type="primary">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新数据
        </a-button>
        <a-button @click="exportData" :loading="exportLoading" type="default">
          <template #icon>
            <DownloadOutlined />
          </template>
          导出
        </a-button>
      </div>
    </div>

    <!-- 时间选择器 -->
    <a-card class="filter-card" style="margin-bottom: 20px;">
      <a-form layout="inline">
        <a-form-item label="应用渠道">
          <a-select
            v-model:value="searchForm.appCode"
            style="width: 160px;"
            @change="handleAppCodeChange"
            :loading="appChannelsLoading"
          >
            <a-select-option value="">所有</a-select-option>
            <a-select-option
              v-for="channel in appChannels"
              :key="channel.appCode"
              :value="channel.appCode"
            >
              {{ channel.appName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="时间范围">
          <a-range-picker
            v-model:value="dateRange"
            format="YYYY-MM-DD"
            @change="handleDateChange"
            style="width: 240px;"
          />
        </a-form-item>
        <a-form-item label="统计类型">
          <a-select v-model:value="searchForm.type" style="width: 120px;" @change="handleTypeChange">
            <a-select-option :value="1">按日统计</a-select-option>
            <a-select-option :value="2">按周统计</a-select-option>
            <a-select-option :value="3">按月统计</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 数据标签页 -->
    <a-card>
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane key="userTotal" tab="用户总数">
          <div class="data-content">
            <div class="table-header">
              <a-button
                type="primary"
                size="small"
                @click="exportTabData('userTotal')"
                :loading="exportLoading"
                style="margin-bottom: 16px;"
              >
                <template #icon>
                  <DownloadOutlined />
                </template>
                导出数据
              </a-button>
            </div>
            <a-table
              :columns="tableColumns"
              :data-source="currentTabData"
              :loading="loading"
              :pagination="{ pageSize: 10 }"
              row-key="key"
            />
          </div>
        </a-tab-pane>

        <a-tab-pane key="newUsers" tab="新增用户数">
          <div class="data-content">
            <div class="table-header">
              <a-button
                type="primary"
                size="small"
                @click="exportTabData('newUsers')"
                :loading="exportLoading"
                style="margin-bottom: 16px;"
              >
                <template #icon>
                  <DownloadOutlined />
                </template>
                导出数据
              </a-button>
            </div>
            <a-table
              :columns="tableColumns"
              :data-source="currentTabData"
              :loading="loading"
              :pagination="{ pageSize: 10 }"
              row-key="key"
            />
          </div>
        </a-tab-pane>

        <a-tab-pane key="activeUsers" tab="活跃用户数">
          <div class="data-content">
            <div class="table-header">
              <a-button
                type="primary"
                size="small"
                @click="exportTabData('activeUsers')"
                :loading="exportLoading"
                style="margin-bottom: 16px;"
              >
                <template #icon>
                  <DownloadOutlined />
                </template>
                导出数据
              </a-button>
            </div>
            <a-table
              :columns="tableColumns"
              :data-source="currentTabData"
              :loading="loading"
              :pagination="{ pageSize: 10 }"
              row-key="key"
            />
          </div>
        </a-tab-pane>

        <a-tab-pane key="payAmount" tab="付费金额">
          <div class="data-content">
            <div class="table-header">
              <a-button
                type="primary"
                size="small"
                @click="exportTabData('payAmount')"
                :loading="exportLoading"
                style="margin-bottom: 16px;"
              >
                <template #icon>
                  <DownloadOutlined />
                </template>
                导出数据
              </a-button>
            </div>
            <a-table
              :columns="tableColumns"
              :data-source="currentTabData"
              :loading="loading"
              :pagination="{ pageSize: 10 }"
              row-key="key"
            />
          </div>
        </a-tab-pane>

        <a-tab-pane key="payUsers" tab="付费人数">
          <div class="data-content">
            <div class="table-header">
              <a-button
                type="primary"
                size="small"
                @click="exportTabData('payUsers')"
                :loading="exportLoading"
                style="margin-bottom: 16px;"
              >
                <template #icon>
                  <DownloadOutlined />
                </template>
                导出数据
              </a-button>
            </div>
            <a-table
              :columns="tableColumns"
              :data-source="currentTabData"
              :loading="loading"
              :pagination="{ pageSize: 10 }"
              row-key="key"
            />
          </div>
        </a-tab-pane>

        <a-tab-pane key="incomeAmount" tab="入账金额">
          <div class="data-content">
            <div class="table-header">
              <a-button
                type="primary"
                size="small"
                @click="exportTabData('incomeAmount')"
                :loading="exportLoading"
                style="margin-bottom: 16px;"
              >
                <template #icon>
                  <DownloadOutlined />
                </template>
                导出数据
              </a-button>
            </div>
            <a-table
              :columns="tableColumns"
              :data-source="currentTabData"
              :loading="loading"
              :pagination="{ pageSize: 10 }"
              row-key="key"
            />
          </div>
        </a-tab-pane>

        <a-tab-pane key="incomeCount" tab="入账笔数">
          <div class="data-content">
            <div class="table-header">
              <a-button
                type="primary"
                size="small"
                @click="exportTabData('incomeCount')"
                :loading="exportLoading"
                style="margin-bottom: 16px;"
              >
                <template #icon>
                  <DownloadOutlined />
                </template>
                导出数据
              </a-button>
            </div>
            <a-table
              :columns="tableColumns"
              :data-source="currentTabData"
              :loading="loading"
              :pagination="{ pageSize: 10 }"
              row-key="key"
            />
          </div>
        </a-tab-pane>

        <a-tab-pane key="outcomeAmount" tab="出账金额">
          <div class="data-content">
            <div class="table-header">
              <a-button
                type="primary"
                size="small"
                @click="exportTabData('outcomeAmount')"
                :loading="exportLoading"
                style="margin-bottom: 16px;"
              >
                <template #icon>
                  <DownloadOutlined />
                </template>
                导出数据
              </a-button>
            </div>
            <a-table
              :columns="tableColumns"
              :data-source="currentTabData"
              :loading="loading"
              :pagination="{ pageSize: 10 }"
              row-key="key"
            />
          </div>
        </a-tab-pane>

        <a-tab-pane key="outcomeCount" tab="出账笔数">
          <div class="data-content">
            <div class="table-header">
              <a-button
                type="primary"
                size="small"
                @click="exportTabData('outcomeCount')"
                :loading="exportLoading"
                style="margin-bottom: 16px;"
              >
                <template #icon>
                  <DownloadOutlined />
                </template>
                导出数据
              </a-button>
            </div>
            <a-table
              :columns="tableColumns"
              :data-source="currentTabData"
              :loading="loading"
              :pagination="{ pageSize: 10 }"
              row-key="key"
            />
          </div>
        </a-tab-pane>

        <a-tab-pane key="retention" tab="次日留存率">
          <div class="data-content">
            <div class="table-header">
              <a-button
                type="primary"
                size="small"
                @click="exportTabData('retention')"
                :loading="exportLoading"
                style="margin-bottom: 16px;"
              >
                <template #icon>
                  <DownloadOutlined />
                </template>
                导出数据
              </a-button>
            </div>
            <a-table
              :columns="tableColumns"
              :data-source="currentTabData"
              :loading="loading"
              :pagination="{ pageSize: 10 }"
              row-key="key"
            />
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined, DownloadOutlined } from '@ant-design/icons-vue'

// DataTable组件已经用a-table替代，不再需要导入
import {
  getDimensionDetail,
  getAppChannelList,
  type DimensionSection,
  type AppChannel
} from '../api/statistics'
import { handleDateRangeChange } from '../utils/dateFormat'
import dayjs, { type Dayjs } from 'dayjs'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const appChannelsLoading = ref(false)
const dateRange = ref<[Dayjs, Dayjs] | null>(null)
const activeTab = ref('userTotal')

// 维度数据
const tabDataMap = ref<Record<string, DimensionSection[]>>({})

// 应用渠道数据
const appChannels = ref<AppChannel[]>([])

// 搜索表单
const searchForm = reactive({
  appCode: '', // 应用渠道代码
  startTime: '',
  endTime: '',
  type: 1 // 按日统计
})

// 标签页配置 - 根据接口文档使用正确的ID
const tabConfig = {
  userTotal: { name: '用户总数', dimensionId: 189 },
  newUsers: { name: '新增用户数', dimensionId: 190 },
  activeUsers: { name: '活跃用户数', dimensionId: 191 },
  payAmount: { name: '付费金额', dimensionId: 192 },
  payUsers: { name: '付费人数', dimensionId: 193 },
  incomeAmount: { name: '入账金额', dimensionId: 194 },
  incomeCount: { name: '入账笔数', dimensionId: 195 },
  outcomeAmount: { name: '出账金额', dimensionId: 196 },
  outcomeCount: { name: '出账笔数', dimensionId: 197 },
  retention: { name: '次日留存率', dimensionId: 198 }
}

// 当前标签页数据 - 只显示汇总数据的statistics
const currentTabData = computed(() => {
  const rawData = tabDataMap.value[activeTab.value] || []

  // 查找汇总数据（name包含"汇总"的section）
  const summarySection = rawData.find((item: any) =>
    item.name && item.name.includes('汇总')
  )

  // 如果找到汇总数据，返回其statistics数组
  if (summarySection && summarySection.statistics) {
    return summarySection.statistics.map((stat: any, index: number) => ({
      key: stat.id || index,
      cutDay: stat.cutDay,
      statisticsValue: stat.statisticsValue,
      proportion: stat.proportion
    }))
  }

  return []
})

// 根据标签页类型获取表头配置
const getTableHeaders = (tabKey: string) => {
  const userCountTabs = ['userTotal', 'newUsers', 'activeUsers', 'payUsers']
  const amountTabs = ['payAmount', 'incomeAmount', 'outcomeAmount']
  const countTabs = ['incomeCount', 'outcomeCount']
  const rateTabs = ['retention']

  if (userCountTabs.includes(tabKey)) {
    return { dateHeader: '日期', valueHeader: '人数' }
  } else if (amountTabs.includes(tabKey)) {
    return { dateHeader: '日期', valueHeader: '金额' }
  } else if (countTabs.includes(tabKey)) {
    return { dateHeader: '日期', valueHeader: '笔数' }
  } else if (rateTabs.includes(tabKey)) {
    return { dateHeader: '日期', valueHeader: '留存率' }
  } else {
    return { dateHeader: '日期', valueHeader: '数值' }
  }
}

// 动态表格列配置 - 根据当前标签页调整表头
const tableColumns = computed(() => {
  const headers = getTableHeaders(activeTab.value)

  return [
    {
      title: headers.dateHeader,
      dataIndex: 'cutDay',
      key: 'cutDay',
      width: 100
    },
    {
      title: headers.valueHeader,
      dataIndex: 'statisticsValue',
      key: 'statisticsValue',
      width: 80,
      align: 'right' as const,
      customRender: ({ text }: { text: number }) => {
        if (text === undefined || text === null) return '0'
        return text.toLocaleString()
      }
    }
  ]
})



// 处理日期变化
const handleDateChange = (dates: [Dayjs, Dayjs] | null) => {
  handleDateRangeChange(dates, (startTime, endTime) => {
    searchForm.startTime = startTime || ''
    searchForm.endTime = endTime || ''
    if (startTime && endTime) {
      refreshData()
    }
  })
}

// 处理标签页切换
const handleTabChange = (key: string) => {
  activeTab.value = key
  console.log('标签页切换到:', key)
  // 如果该标签页没有数据，就加载数据
  if (!tabDataMap.value[key]) {
    loadTabData(key)
  }
}

// 处理统计类型变化
const handleTypeChange = () => {
  console.log('统计类型变化:', searchForm.type)
  refreshData()
}

// 处理应用渠道变化
const handleAppCodeChange = () => {
  console.log('应用渠道变化:', searchForm.appCode)
  refreshData()
}

// 加载应用渠道列表
const loadAppChannels = async () => {
  appChannelsLoading.value = true
  try {
    const response = await getAppChannelList()
    console.log('应用渠道列表响应:', response)

    if (response.data && Array.isArray(response.data)) {
      appChannels.value = response.data
    } else {
      // 如果API还没有实现，使用真实的应用渠道数据
      appChannels.value = [
        { appCode: 'pasture', appName: '神奇农场' },
        { appCode: 'pasture_zy', appName: '神奇农场-掌育' },
        { appCode: 'pasture_xm', appName: '神奇农场-小米' }
      ]
    }
    console.log('应用渠道列表:', appChannels.value)
  } catch (error) {
    console.error('获取应用渠道列表失败:', error)
    // 使用真实的应用渠道数据
    appChannels.value = [
      { appCode: 'pasture', appName: '神奇农场' },
      { appCode: 'pasture_zy', appName: '神奇农场-掌育' },
      { appCode: 'pasture_xm', appName: '神奇农场-小米' }
    ]
    console.log('使用默认应用渠道列表:', appChannels.value)
  } finally {
    appChannelsLoading.value = false
  }
}

// 加载标签页数据
const loadTabData = async (tabKey: string) => {
  const config = tabConfig[tabKey as keyof typeof tabConfig]
  if (!config) return

  loading.value = true
  try {
    // 简化请求参数，只保留四个必要参数
    const params = {
      id: config.dimensionId,
      startTime: searchForm.startTime,
      endTime: searchForm.endTime,
      type: searchForm.type
    }

    console.log(`正在获取${config.name}数据，简化参数:`, params)

    const response = await getDimensionDetail(params)
    console.log(`${config.name}数据响应:`, response)
    console.log(`${config.name}响应数据类型:`, typeof response.data)
    console.log(`${config.name}响应数据结构:`, JSON.stringify(response.data, null, 2))

    if (response.data && response.data.sections) {
      console.log(`${config.name}使用sections数据`)
      // 处理sections数据，如果total为0，显示为0而不是null
      const processedSections = response.data.sections.map((section: any, index: number) => ({
        ...section,
        key: section.id || index,
        total: section.total || 0
      }))
      tabDataMap.value[tabKey] = processedSections
    } else if (response.data && Array.isArray(response.data)) {
      console.log(`${config.name}使用数组数据`)
      tabDataMap.value[tabKey] = response.data
    } else {
      console.warn(`${config.name}数据格式不符合预期:`, response.data)
      console.warn(`${config.name}设置为空数组`)
      // 如果没有数据，创建默认的空数据结构
      tabDataMap.value[tabKey] = [
        { key: 1, name: '暂无数据', total: 0, memo: '当前时间范围内无数据' }
      ]
    }
  } catch (error) {
    console.error(`获取${config.name}数据失败:`, error)
    message.error(`获取${config.name}数据失败`)
    // 提供示例数据以避免空白页面
    tabDataMap.value[tabKey] = [
      { key: 1, name: '2024-01-01', total: 1000, memo: '示例数据' },
      { key: 2, name: '2024-01-02', total: 1200, memo: '示例数据' },
      { key: 3, name: '2024-01-03', total: 980, memo: '示例数据' }
    ]
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  // 清空缓存数据
  tabDataMap.value = {}
  // 重新加载当前标签页数据
  loadTabData(activeTab.value)
}



// 导出数据
const exportData = async () => {
  const currentData = currentTabData.value
  if (!currentData || currentData.length === 0) {
    message.warning('当前标签页没有数据可导出')
    return
  }

  exportLoading.value = true
  try {
    // 获取当前标签页的配置
    const config = tabConfig[activeTab.value as keyof typeof tabConfig]
    const fileName = `${config.name}_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`

    // 获取表头配置
    const headers = getTableHeaders(activeTab.value)

    // 准备导出数据 - 导出汇总数据的statistics
    const exportData = currentData.map((item: any) => ({
      [headers.dateHeader]: item.cutDay || '-',
      [headers.valueHeader]: item.statisticsValue || 0
    }))

    // 动态导入xlsx库
    const XLSX = await import('xlsx')

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.json_to_sheet(exportData)

    // 设置列宽
    ws['!cols'] = [
      { wch: 20 }, // 日期列
      { wch: 15 }  // 数值列
    ]

    // 添加工作表
    XLSX.utils.book_append_sheet(wb, ws, config.name)

    // 导出文件
    XLSX.writeFile(wb, fileName)

    message.success(`成功导出 ${currentData.length} 条${config.name}数据`)
  } catch (error) {
    console.error('导出数据失败:', error)
    message.error('导出数据失败')
  } finally {
    exportLoading.value = false
  }
}

// 导出指定标签页数据（超链接方式，无需token认证）
const exportTabData = async (tabKey: string) => {
  try {
    exportLoading.value = true

    // 获取标签页配置
    const config = tabConfig[tabKey as keyof typeof tabConfig]
    if (!config) {
      message.error('未找到对应的数据配置')
      return
    }

    // 构建查询参数 - 根据接口文档的必填参数
    const params = new URLSearchParams({
      id: config.dimensionId.toString() // 维度ID，必填
    })

    // 如果有时间范围，添加到参数中
    if (searchForm.startTime && searchForm.endTime) {
      params.append('startTime', searchForm.startTime)
      params.append('endTime', searchForm.endTime)
    }

    // 添加时间间隔参数（根据接口文档）
    if (searchForm.type) {
      params.append('timeInterval', searchForm.type.toString())
    }

    // 构建导出URL
    const exportUrl = `https://api.zj7hui.com/pasture/admin/spectaculars/report?${params.toString()}`

    console.log(`${config.name}导出URL:`, exportUrl)

    // 创建a标签并直接跳转到服务器下载地址
    const link = document.createElement('a')
    link.href = exportUrl
    link.target = '_blank' // 在新窗口打开，避免影响当前页面

    // 添加到DOM，触发跳转，然后移除
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    message.success(`正在导出${config.name}数据，请稍候...`)
  } catch (error) {
    console.error('导出数据失败:', error)
    message.error('导出数据失败')
  } finally {
    exportLoading.value = false
  }
}

// 组件挂载时初始化
onMounted(async () => {
  // 加载应用渠道列表
  await loadAppChannels()

  // 设置默认时间范围（最近7天）
  const endDate = dayjs()
  const startDate = endDate.subtract(6, 'day')
  dateRange.value = [startDate, endDate]

  // 设置默认时间
  handleDateChange(dateRange.value)

  console.log('StatisticsBoard初始化完成，直接加载数据')

  // 加载默认标签页数据
  loadTabData(activeTab.value)
})
</script>

<style scoped>
.statistics-board {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-card .ant-form-item {
  margin-bottom: 0;
}

/* 自定义表格样式 - 减少padding使表格更紧凑 */
:deep(.ant-table-thead > tr > th),
:deep(.ant-table-tbody > tr > td) {
  padding: 8px 12px !important;
}

/* 设置表格宽度，让右侧留出空白 */
:deep(.ant-table) {
  max-width: 300px;
}

/* 确保表格列宽生效 */
:deep(.ant-table-thead > tr > th:first-child) {
  width: 100px !important;
  max-width: 100px !important;
}

:deep(.ant-table-thead > tr > th:last-child) {
  width: 80px !important;
  max-width: 80px !important;
}

:deep(.ant-table-tbody > tr > td:first-child) {
  width: 100px !important;
  max-width: 100px !important;
}

:deep(.ant-table-tbody > tr > td:last-child) {
  width: 80px !important;
  max-width: 80px !important;
}
</style>
