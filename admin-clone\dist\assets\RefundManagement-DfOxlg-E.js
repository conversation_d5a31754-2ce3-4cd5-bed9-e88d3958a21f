import{d as e,f as a,r as t,o as l,b as o,a1 as d,a0 as n,k as r,_ as u,S as i,V as p,G as s,u as m,U as c,a2 as f,a3 as v,F as y}from"./vendor-Dhi-hotu.js";import{d as g,e as _}from"./antd-DSyIu0Jx.js";import{a as h,A as w}from"./AppSelector-CdXIhBuS.js";import{u as k}from"./app-C5BK_7TP.js";import{h as C}from"./dateFormat-DmnmjEpg.js";import{_ as T}from"./_plugin-vue_export-helper-xcdYWU4n.js";import"./auth-CDe9Kw3v.js";const S={class:"refund-management"},b={class:"page-header"},x={class:"header-actions"},Y=T(e({__name:"RefundManagement",setup(e){const T=a(!1),Y=a(!1),N=a([]),U=a(null),j=a(!1),z=a(null),M=t({page:1,size:10}),I=t({appCode:"",endTime:"",startTime:"",type:1}),D=t({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`}),V=[{title:"退款订单号",dataIndex:"refundSn",key:"refundSn",width:180},{title:"原订单号",dataIndex:"flowNo",key:"flowNo",width:180},{title:"退款金额",dataIndex:"amount",key:"amount",width:100},{title:"退款类型",dataIndex:"type",key:"type",width:100},{title:"支付方式",dataIndex:"payType",key:"payType",width:120},{title:"退款状态",dataIndex:"refundStatus",key:"refundStatus",width:120},{title:"操作人",dataIndex:"createName",key:"createName",width:100},{title:"申请时间",dataIndex:"createTime",key:"createTime",width:160}],A=e=>({1:"微信公众号",2:"小天才服务号",3:"线下支付",4:"小天才表端支付",5:"小天才H5支付",6:"华为支付"}[e]||"未知"),R=e=>({0:"失败",1:"发起成功",2:"实际到账"}[e]||"未知"),F=async()=>{T.value=!0;try{const e={page:D.current,size:D.pageSize,payType:1};M.appCode&&M.appCode.trim()&&(e.appCode=M.appCode),M.refundSn?.trim()&&(e.refundSn=M.refundSn.trim()),M.flowNo?.trim()&&(e.flowNo=M.flowNo.trim()),M.startTime&&(e.startTime=M.startTime),M.endTime&&(e.endTime=M.endTime);const a=await h(e);a.data&&(Array.isArray(a.data)?(N.value=a.data,D.total=a.data.length):a.data.list?(N.value=a.data.list||[],D.total=a.data.total||0):(N.value=[],D.total=0))}catch(e){console.error("获取退款列表失败:",e),g.error("获取退款列表失败"),N.value=[],D.total=0}finally{T.value=!1}},L=()=>{Object.assign(M,{appCode:void 0,flowNo:void 0,refundSn:void 0,startTime:void 0,endTime:void 0,page:1,size:10}),U.value=null,D.current=1,F()},O=e=>{C(e,(e,a)=>{M.startTime=e,M.endTime=a})},$=e=>{D.current=e.current||1,D.pageSize=e.pageSize||10,F()},q=()=>{j.value=!0,I.appCode="",I.type=1,z.value=null},E=async()=>{try{if(!z.value||2!==z.value.length)return void g.error("请选择退款时间范围");Y.value=!0;const e=z.value[0].format("YYYY-MM-DD")+" 00:00:00",a=z.value[1].format("YYYY-MM-DD")+" 23:59:59",t=`https://api.zj7hui.com/pasture/order/refund/export?${new URLSearchParams({appCode:I.appCode||"",startTime:e,endTime:a,type:I.type.toString()}).toString()}`;console.log("退款导出URL:",t);const l=document.createElement("a");l.href=t,l.target="_blank",document.body.appendChild(l),l.click(),document.body.removeChild(l),g.success("正在导出退款数据，请稍候..."),j.value=!1}catch(e){console.error("导出退款数据失败:",e),g.error("导出退款数据失败")}finally{Y.value=!1}};return l(async()=>{const e=k();await e.initAppList(),M.appCode="",console.log("RefundManagement设置默认为所有应用"),F()}),o(()=>{N.value=[],D.total=0,T.value=!1,Object.assign(M,{appCode:void 0,flowNo:void 0,refundSn:void 0,startTime:void 0,endTime:void 0,page:1,size:10}),U.value=null,console.log("退款管理页面已清理")}),(e,a)=>{const t=i("a-button"),l=i("a-form-item"),o=i("a-input"),g=i("a-range-picker"),h=i("a-form"),k=i("a-card"),C=i("a-tag"),G=i("a-table"),H=i("a-radio"),J=i("a-radio-group"),P=i("a-modal");return p(),d("div",S,[n("div",b,[a[10]||(a[10]=n("h2",null,"退款订单管理",-1)),n("div",x,[r(t,{type:"primary",onClick:q,loading:Y.value},{icon:u(()=>[r(m(_))]),default:u(()=>[a[9]||(a[9]=s(" 导出退款数据 ",-1))]),_:1,__:[9]},8,["loading"])])]),r(k,{class:"search-card",style:{"margin-bottom":"16px"}},{default:u(()=>[r(h,{layout:"inline",model:M},{default:u(()=>[r(l,{label:"应用渠道"},{default:u(()=>[r(w,{modelValue:M.appCode,"onUpdate:modelValue":a[0]||(a[0]=e=>M.appCode=e),width:"150px"},null,8,["modelValue"])]),_:1}),r(l,{label:"退款编号"},{default:u(()=>[r(o,{value:M.refundSn,"onUpdate:value":a[1]||(a[1]=e=>M.refundSn=e),placeholder:"请输入退款编号"},null,8,["value"])]),_:1}),r(l,{label:"订单编号"},{default:u(()=>[r(o,{value:M.flowNo,"onUpdate:value":a[2]||(a[2]=e=>M.flowNo=e),placeholder:"请输入订单编号"},null,8,["value"])]),_:1}),r(l,{label:"退款时间"},{default:u(()=>[r(g,{value:U.value,"onUpdate:value":a[3]||(a[3]=e=>U.value=e),format:"YYYY-MM-DD",placeholder:"['开始时间', '结束时间']",style:{width:"240px"},onChange:O},null,8,["value"])]),_:1}),r(l,null,{default:u(()=>[r(t,{type:"primary",onClick:F,loading:T.value},{default:u(()=>a[11]||(a[11]=[s(" 搜索 ",-1)])),_:1,__:[11]},8,["loading"]),r(t,{onClick:L,style:{"margin-left":"8px"}},{default:u(()=>a[12]||(a[12]=[s(" 重置 ",-1)])),_:1,__:[12]})]),_:1})]),_:1},8,["model"])]),_:1}),r(k,null,{default:u(()=>[r(G,{columns:V,"data-source":N.value,loading:T.value,pagination:D,onChange:$,"row-key":"id"},{bodyCell:u(({column:e,record:a})=>{return["payType"===e.key?(p(),c(C,{key:0,color:(l=a.payType,{1:"green",2:"blue",3:"orange",4:"purple",5:"cyan",6:"red"}[l]||"default")},{default:u(()=>[s(v(A(a.payType)),1)]),_:2},1032,["color"])):f("",!0),"refundStatus"===e.key?(p(),c(C,{key:1,color:(t=a.refundStatus,{0:"red",1:"orange",2:"green"}[t]||"default")},{default:u(()=>[s(v(R(a.refundStatus)),1)]),_:2},1032,["color"])):f("",!0),"type"===e.key?(p(),c(C,{key:2,color:1===a.type?"blue":"orange"},{default:u(()=>[s(v(1===a.type?"全部退款":"部分退款"),1)]),_:2},1032,["color"])):f("",!0),"amount"===e.key?(p(),d(y,{key:3},[s(" ¥"+v(a.amount?.toFixed(2)||"0.00"),1)],64)):f("",!0)];var t,l}),_:1},8,["data-source","loading","pagination"])]),_:1}),r(P,{open:j.value,"onUpdate:open":a[7]||(a[7]=e=>j.value=e),title:"数据导出",onOk:E,onCancel:a[8]||(a[8]=e=>j.value=!1),"confirm-loading":Y.value,width:"500px"},{default:u(()=>[r(h,{model:I,layout:"vertical"},{default:u(()=>[r(l,{label:"应用渠道"},{default:u(()=>[r(w,{modelValue:I.appCode,"onUpdate:modelValue":a[4]||(a[4]=e=>I.appCode=e),width:"100%"},null,8,["modelValue"])]),_:1}),r(l,{label:"退款时间",required:""},{default:u(()=>[r(g,{value:z.value,"onUpdate:value":a[5]||(a[5]=e=>z.value=e),format:"YYYY-MM-DD",style:{width:"100%"},placeholder:"['开始日期', '结束日期']"},null,8,["value"])]),_:1}),r(l,{label:"订单类型"},{default:u(()=>[r(J,{value:I.type,"onUpdate:value":a[6]||(a[6]=e=>I.type=e)},{default:u(()=>[r(H,{value:1},{default:u(()=>a[13]||(a[13]=[s("所有订单",-1)])),_:1,__:[13]}),r(H,{value:2},{default:u(()=>a[14]||(a[14]=[s("出账订单",-1)])),_:1,__:[14]})]),_:1},8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open","confirm-loading"])])}}}),[["__scopeId","data-v-4bf0dc4f"]]);export{Y as default};
