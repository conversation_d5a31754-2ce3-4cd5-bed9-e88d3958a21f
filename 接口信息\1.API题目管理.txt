获取一题 num 获取题目数
**接口地址** `/pasture/question/getOneQuestion`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| num         |      num   |     query        |       true      | integer   |      |
            | playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«题目组»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    题目组   |   题目组    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**题目组**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| grade         |     grade年级      |  int32   |      |
            | id         |     id      |  int64   |      |
            | name         |     name名称      |  string   |      |
            | people         |     people适用人群：0学前、1年级、2年级、3年级、4年级、5年级、6年级，多选使用|分隔      |  string   |      |
            | questionIds         |     questionIds题目id多个题使用“,”隔离      |  string   |      |
            | questionType         |     questionType题目类型 1启蒙 2口算 3易错 4复习（后台计算组装） 5课程包（代码写死目前走启蒙类型） 6通用      |  int32   |      |
            | questions         |     questionIds题目id多个题使用“,”隔离      |  array   | 题     |
            | region         |     region地域分类：多选省份，也可直接选择全国，多选使用,分隔      |  string   |      |
            | subjects         |     subjects科目： 0数学、1语文、2英语、3自然科学      |  int32   |      |
            | viewType         |     viewType展示类型1双选模板 2：四选模板      |  int32   |      |
            

**题**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| answer         |     answer正确答案 1A 2B 3C 4D      |  int32   |      |
            | answerType         |     answerType答案类型：1选择       |  int32   |      |
            | code         |     code题目编号      |  string   |      |
            | correctNum         |     correctNum正确次数      |  int32   |      |
            | correctTime         |     correctTime正确答题所用时间（秒）      |  number   |      |
            | createTime         |     createTime      |  date-time   |      |
            | difficulty         |     difficulty难度，由系统根据学生做题反馈自动生成      |  int32   |      |
            | explained         |     explained老师讲解      |  string   |      |
            | exposureNum         |     exposureNum曝光次数      |  int32   |      |
            | grade         |     grade年级0学龄前      |  int32   |      |
            | id         |     id      |  int64   |      |
            | labelIds         |     labelIds标签：多个使用“,”区分      |  string   |      |
            | level         |     level启蒙题等级       |  int32   |      |
            | modifyAdvice         |     modifyAdvice修改意见, JSON格式, 可叠加      |  string   |      |
            | name         |     name名称      |  string   |      |
            | options         |     选项      |  array   | 选项     |
            | questionStatus         |     questionStatus题目状态,待修改0 审核通过1 待审核2      |  int32   |      |
            | questionType         |     questionType题目类型 1启蒙 2口算 3易错 4复习 5课程包 6通用      |  int32   |      |
            | seq         |     seq排序值      |  int32   |      |
            | timeType         |     timeType计时类型：0倒计时 1计时      |  int32   |      |
            | timeValue         |     timeValue倒计时时间      |  int32   |      |
            | topicPic         |     topicPic图片题干      |  string   |      |
            | topicText         |     topicText文字题干      |  string   |      |
            | topicType         |     topicType题干类型：0语音 1文字 2文字+语音 3图片 4图片+语音      |  int32   |      |
            | topicVoice         |     topicVoice语音题干      |  string   |      |
            | updateTime         |     updateTime      |  date-time   |      |
            | updateUserId         |     updateUserId创建人ID      |  int64   |      |
            | updateUsername         |     updateUsername创建人名字      |  string   |      |
            | voiceTime         |     voiceTime语言时长（秒）      |  int32   |      |
            

**选项**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| answerType         |     answerType答案类型 1选项1 2选项2 3选项3 4选项4      |  int32   |      |
            | content         |     content内容      |  string   |      |
            | createTime         |     createTime      |  date-time   |      |
            | id         |     id      |  int64   |      |
            | questionId         |     questionId题id      |  int64   |      |
            | type         |     type类型 1文本 2APP端资源 3网络图片      |  int32   |      |
            | updateTime         |     updateTime      |  date-time   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "grade": 0,
        "id": 0,
        "name": "",
        "people": "",
        "questionIds": "",
        "questionType": 0,
        "questions": [
            {
                "answer": 0,
                "answerType": 0,
                "code": "",
                "correctNum": 0,
                "correctTime": 0,
                "createTime": "",
                "difficulty": 0,
                "explained": "",
                "exposureNum": 0,
                "grade": 0,
                "id": 0,
                "labelIds": "",
                "level": 0,
                "modifyAdvice": "",
                "name": "",
                "options": [
                    {
                        "answerType": 0,
                        "content": "",
                        "createTime": "",
                        "id": 0,
                        "questionId": 0,
                        "type": 0,
                        "updateTime": ""
                    }
                ],
                "questionStatus": 0,
                "questionType": 0,
                "seq": 0,
                "timeType": 0,
                "timeValue": 0,
                "topicPic": "",
                "topicText": "",
                "topicType": 0,
                "topicVoice": "",
                "updateTime": "",
                "updateUserId": 0,
                "updateUsername": "",
                "voiceTime": 0
            }
        ],
        "region": "",
        "subjects": 0,
        "viewType": 0
    },
    "message": "",
    "msg": ""
}
```
--------分割线
保存题目

**接口地址** `/pasture/question/saveQuestion`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| list         |      list   |     body        |       true      | array   | 课程包题目答案     |
            



**schema属性说明**
  
**课程包题目答案**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| groupId  | 题目组id |   body    |   false   |int64  |       |
| playerId  | 玩家id |   body    |   false   |int64  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```




