templateAward新增

**接口地址** `/pasture/template/award/add`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| templateAward         |      templateAward   |     body        |       true      | com.qihui.pasture.model.TemplateAward   | com.qihui.pasture.model.TemplateAward     |
            



**schema属性说明**
  
**com.qihui.pasture.model.TemplateAward**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| icon  | iconapp使用的icon |   body    |   false   |string  |       |
| id  | id |   body    |   false   |int64  |       |
| menuId  | menuId菜单id |   body    |   false   |int64  |       |
| name  | name奖品名称 |   body    |   false   |string  |       |
| num  | num奖励个数 |   body    |   false   |int32  |       |
| templateId  | templateId |   body    |   false   |int64  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------------分割线
templateAward删除

**接口地址** `/pasture/template/award/delete`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------------分割线
templateAward获取详情

**接口地址** `/pasture/template/award/detail`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«com.qihui.pasture.model.TemplateAward»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    com.qihui.pasture.model.TemplateAward   |   com.qihui.pasture.model.TemplateAward    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.TemplateAward**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| icon         |     iconapp使用的icon      |  string   |      |
            | id         |     id      |  int64   |      |
            | menuId         |     menuId菜单id      |  int64   |      |
            | name         |     name奖品名称      |  string   |      |
            | num         |     num奖励个数      |  int32   |      |
            | templateId         |     templateId      |  int64   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "icon": "",
        "id": 0,
        "menuId": 0,
        "name": "",
        "num": 0,
        "templateId": 0
    },
    "message": "",
    "msg": ""
}
```



--------------分割线
templateAward获取列表

**接口地址** `/pasture/template/award/list`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| page         |      page   |     query        |       false      | integer   |      |
            | size         |      size   |     query        |       false      | integer   |      |
            | templateAward         |      templateAward   |     body        |       true      | com.qihui.pasture.model.TemplateAward   | com.qihui.pasture.model.TemplateAward     |
            



**schema属性说明**
  
**com.qihui.pasture.model.TemplateAward**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| icon  | iconapp使用的icon |   body    |   false   |string  |       |
| id  | id |   body    |   false   |int64  |       |
| menuId  | menuId菜单id |   body    |   false   |int64  |       |
| name  | name奖品名称 |   body    |   false   |string  |       |
| num  | num奖励个数 |   body    |   false   |int32  |       |
| templateId  | templateId |   body    |   false   |int64  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«com.qihui.pasture.model.TemplateAward»»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   com.qihui.pasture.model.TemplateAward    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.TemplateAward**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| icon         |     iconapp使用的icon      |  string   |      |
            | id         |     id      |  int64   |      |
            | menuId         |     menuId菜单id      |  int64   |      |
            | name         |     name奖品名称      |  string   |      |
            | num         |     num奖励个数      |  int32   |      |
            | templateId         |     templateId      |  int64   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "icon": "",
            "id": 0,
            "menuId": 0,
            "name": "",
            "num": 0,
            "templateId": 0
        }
    ],
    "message": "",
    "msg": ""
}
```



--------------分割线
templateAward更新

**接口地址** `/pasture/template/award/update`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| templateAward         |      templateAward   |     body        |       true      | com.qihui.pasture.model.TemplateAward   | com.qihui.pasture.model.TemplateAward     |
            



**schema属性说明**
  
**com.qihui.pasture.model.TemplateAward**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| icon  | iconapp使用的icon |   body    |   false   |string  |       |
| id  | id |   body    |   false   |int64  |       |
| menuId  | menuId菜单id |   body    |   false   |int64  |       |
| name  | name奖品名称 |   body    |   false   |string  |       |
| num  | num奖励个数 |   body    |   false   |int32  |       |
| templateId  | templateId |   body    |   false   |int64  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```


