<template>
  <a-layout class="layout">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      theme="dark"
      width="150"
      :collapsed-width="80"
      class="custom-sider"
    >
      <div class="logo">
        <span v-if="!collapsed">管理后台</span>
        <span v-else>后台</span>
      </div>

      <a-menu
        v-model:selectedKeys="selectedKeys"
        theme="dark"
        mode="inline"
        @click="handleMenuClick"
        class="custom-menu"
      >
        <!-- 扁平化菜单 - 直接显示需要的页面 -->
        <a-menu-item key="/dashboard">
          <template #icon>
            <DashboardOutlined />
          </template>
          实时统计
        </a-menu-item>

        <a-menu-item key="/users">
          <template #icon>
            <UserOutlined />
          </template>
          用户列表
        </a-menu-item>

        <a-menu-item key="/orders">
          <template #icon>
            <ShoppingOutlined />
          </template>
          订单列表
        </a-menu-item>

        <a-menu-item key="/refunds">
          <template #icon>
            <UndoOutlined />
          </template>
          退款订单
        </a-menu-item>

        <a-menu-item key="/statistics">
          <template #icon>
            <BarChartOutlined />
          </template>
          数据看板
        </a-menu-item>
      </a-menu>
    </a-layout-sider>
    
    <!-- 主内容区 -->
    <a-layout>
      <!-- 顶部导航 -->
      <a-layout-header class="layout-header">
        <div class="header-left">
          <MenuUnfoldOutlined
            v-if="collapsed"
            class="trigger"
            @click="() => (collapsed = !collapsed)"
          />
          <MenuFoldOutlined
            v-else
            class="trigger"
            @click="() => (collapsed = !collapsed)"
          />
        </div>
        
        <div class="header-right">
          <a-dropdown>
            <a class="ant-dropdown-link" @click.prevent>
              <a-avatar class="userhead" :size="32" :style="{ backgroundColor: '#667eea' }">
                <template #icon>
                  <UserOutlined />
                </template>
              </a-avatar>
              <span class="username">admin1</span>
              <DownOutlined />
            </a>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleLogout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>
      
      <!-- 内容区域 -->
      <a-layout-content class="layout-content">
        <div class="page">
          <router-view />
        </div>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  DashboardOutlined,
  UserOutlined,
  ShoppingOutlined,
  BarChartOutlined,
  DownOutlined,
  LogoutOutlined,
  UndoOutlined
} from '@ant-design/icons-vue'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const collapsed = ref(false)
const selectedKeys = ref<string[]>([route.path])

// 监听路由变化，更新选中的菜单
watch(
  () => route.path,
  (newPath) => {
    selectedKeys.value = [newPath]
  }
)

// 菜单点击处理
const handleMenuClick = async ({ key }: { key: string }) => {
  try {
    console.log(`导航到: ${key}`)
    await router.push(key)
    console.log(`导航成功: ${key}`)
  } catch (error) {
    console.error('路由导航失败:', error)
    // 如果导航失败，尝试强制刷新
    window.location.href = `#${key}`
  }
}

// 退出登录
const handleLogout = () => {
  authStore.logout()
}

// 组件挂载时获取菜单（保留用于其他可能的用途）
onMounted(async () => {
  if (authStore.menuList.length === 0) {
    await authStore.getMenu()
  }
})
</script>

<style scoped>
.layout {
  min-height: 100vh;
  width: 100vw;
  overflow-x: hidden;
}

/* 侧边栏样式 */
.custom-sider {
  background: #1e3a5f !important;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  z-index: 100;
}

.logo {
  height: 50px;
  margin: 16px;
  color: #fff;
  font-size: 16px;
  font-weight: 700;
  background: #1e3a5f;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.logo-icon {
  font-size: 20px;
}

.logo-text {
  white-space: nowrap;
  overflow: hidden;
}

/* 菜单样式 */
.custom-menu.ant-menu-dark {
  background: #1e3a5f !important;
  border-right: none;
}

.custom-menu.ant-menu-dark .ant-menu-item {
  margin: 0;
  border-radius: 0;
  transition: all 0.3s ease;
  color: #fff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.custom-menu.ant-menu-dark .ant-menu-item:hover {
  background: #2d4a6b;
  transform: none;
}

.custom-menu.ant-menu-dark .ant-menu-item-selected {
  background: #4a90e2;
  border-radius: 0;
  color: #fff;
}

/* 顶部导航样式 */
.layout-header {
  height: 70px;
  line-height: 70px;
  background: #fff;
  padding: 0 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid #f0f0f0;
  z-index: 99;
}

.header-left {
  display: flex;
  align-items: center;
}

.trigger {
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 6px;
  color: #666;
}

.trigger:hover {
  color: #667eea;
  background: #f5f5f5;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 16px;
}



.ant-dropdown-link {
  cursor: pointer;
  height: 70px;
  display: flex;
  align-items: center;
  gap: 12px;
  color: rgba(0, 0, 0, 0.85);
  padding: 0 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.ant-dropdown-link:hover {
  background: #f5f5f5;
  color: #667eea;
}

.userhead {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.username {
  font-weight: 500;
  font-size: 14px;
}

/* 内容区域样式 */
.layout-content {
  margin: 0;
  min-height: calc(100vh - 70px);
  background: #f5f7fa;
}

.page {
  min-height: calc(100vh - 70px);
  background: transparent;
  border-radius: 0;
  padding: 24px;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-header {
    padding: 0 16px;
  }

  .header-right {
    gap: 12px;
  }

  .username {
    display: none;
  }

  .page {
    padding: 16px;
  }
}

/* 滚动条样式 */
.ant-layout-sider ::-webkit-scrollbar {
  width: 6px;
}

.ant-layout-sider ::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.ant-layout-sider ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.ant-layout-sider ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style>
