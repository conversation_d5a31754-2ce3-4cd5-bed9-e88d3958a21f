import dayjs, { type Dayjs } from 'dayjs'

/**
 * 将日期格式化为 yyyy-MM-dd HH:mm:ss 格式
 * @param date 日期对象或字符串
 * @param includeTime 是否包含时间部分，默认true
 * @returns 格式化后的字符串
 */
export const formatToApiDate = (date: Dayjs | Date | string, includeTime: boolean = true): string => {
  const dayjsDate = dayjs(date)

  if (includeTime) {
    return dayjsDate.format('YYYY-MM-DD HH:mm:ss')
  } else {
    return dayjsDate.format('YYYY-MM-DD')
  }
}

/**
 * 将日期范围格式化为API需要的格式
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param startWithZeroTime 开始时间是否设为00:00:00，默认true
 * @param endWithMaxTime 结束时间是否设为23:59:59，默认true
 * @returns 格式化后的开始和结束时间
 */
export const formatDateRange = (
  startDate: Dayjs | Date | string,
  endDate: Dayjs | Date | string,
  startWithZeroTime: boolean = true,
  endWithMaxTime: boolean = true
): { startTime: string; endTime: string } => {
  let start = dayjs(startDate)
  let end = dayjs(endDate)
  
  if (startWithZeroTime) {
    start = start.startOf('day') // 设为当天00:00:00
  }
  
  if (endWithMaxTime) {
    end = end.endOf('day') // 设为当天23:59:59
  }
  
  return {
    startTime: formatToApiDate(start),
    endTime: formatToApiDate(end)
  }
}

/**
 * 处理日期范围选择器的变化
 * @param dates 日期范围数组
 * @param callback 回调函数，接收格式化后的开始和结束时间
 */
export const handleDateRangeChange = (
  dates: [Dayjs, Dayjs] | null,
  callback: (startTime?: string, endTime?: string) => void
) => {
  if (dates && dates.length === 2) {
    const { startTime, endTime } = formatDateRange(dates[0], dates[1])
    callback(startTime, endTime)
  } else {
    callback(undefined, undefined)
  }
}

/**
 * 将API返回的时间字符串转换为显示格式
 * @param apiDateString API返回的时间字符串 (YYYY-MM-DD HH:mm:ss 或其他格式)
 * @returns 格式化后的显示字符串 (YYYY-MM-DD HH:mm:ss)
 */
export const parseApiDate = (apiDateString: string): string => {
  if (!apiDateString) return ''

  // 如果已经是标准格式，直接返回
  if (apiDateString.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
    return apiDateString
  }

  // 处理 yyyyMMdd HHmmss 格式（兼容旧格式）
  if (apiDateString.includes(' ')) {
    const [datePart, timePart] = apiDateString.split(' ')
    if (datePart.length === 8 && timePart.length === 6) {
      const year = datePart.substring(0, 4)
      const month = datePart.substring(4, 6)
      const day = datePart.substring(6, 8)
      const hour = timePart.substring(0, 2)
      const minute = timePart.substring(2, 4)
      const second = timePart.substring(4, 6)

      return `${year}-${month}-${day} ${hour}:${minute}:${second}`
    }
  }

  // 处理 yyyyMMdd 格式（兼容旧格式）
  if (apiDateString.length === 8 && /^\d{8}$/.test(apiDateString)) {
    const year = apiDateString.substring(0, 4)
    const month = apiDateString.substring(4, 6)
    const day = apiDateString.substring(6, 8)

    return `${year}-${month}-${day}`
  }

  // 如果格式不匹配，尝试用dayjs解析
  return dayjs(apiDateString).format('YYYY-MM-DD HH:mm:ss')
}
