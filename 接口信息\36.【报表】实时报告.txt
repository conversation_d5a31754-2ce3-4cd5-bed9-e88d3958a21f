查询统计数据 入参：RealTimeDimensionDto.id

**接口地址** `/pasture/admin/realTimeReports/dimensionDetail`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| appCode         |      appCode   |     query        |       true      | string   |      |
            | dimensionId         |      dimensionId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«纬度»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    纬度   |   纬度    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**纬度**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| createName         |     创建人      |  string   |      |
            | createTime         |     创建时间      |  date-time   |      |
            | id         |           |  int64   |      |
            | memo         |     备注      |  string   |      |
            | name         |     名称      |  string   |      |
            | sections         |     纬度区间      |  array   | 纬度区间     |
            | selected         |     是否选中      |  boolean   |      |
            | spectacularsDimensionId         |     纬度关系id      |  int32   |      |
            | unitType         |     单位类型：1秒 2个      |  int32   |      |
            | updateName         |     更新人      |  string   |      |
            | updateTime         |     更新时间      |  date-time   |      |
            | viewType         |     显示类型 line:折线图 bar:圆饼图 pie:柱状图      |  string   |      |
            

**纬度区间**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| cutDay         |     日切时间      |  string   |      |
            | id         |           |  int64   |      |
            | proportion         |     当日占比      |  number   |      |
            | sectionName         |     名称      |  string   |      |
            | statisticsValue         |     统计值      |  number   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "createName": "",
        "createTime": "",
        "id": 0,
        "memo": "",
        "name": "",
        "sections": [
            {
                "cutDay": "",
                "id": 0,
                "proportion": 0,
                "sectionName": "",
                "statisticsValue": 0
            }
        ],
        "selected": true,
        "spectacularsDimensionId": 0,
        "unitType": 0,
        "updateName": "",
        "updateTime": "",
        "viewType": ""
    },
    "message": "",
    "msg": ""
}
```



---------------分割线
获取纬度列表

**接口地址** `/pasture/admin/realTimeReports/dimensionList`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| appCode         |      appCode   |     query        |       true      | string   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«实时纬度»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   实时纬度    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**实时纬度**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| id         |           |  int64   |      |
            | memo         |     备注      |  string   |      |
            | name         |     名称      |  string   |      |
            | range         |     幅度      |  number   |      |
            | rangeType         |     幅度类型 1上涨 2下跌      |  int32   |      |
            | total         |     当日累计      |  number   |      |
            | unitType         |     单位类型：1秒 2个 3元      |  int32   |      |
            | weekRange         |     上周幅度      |  number   |      |
            | weekRangeType         |     上周幅度类型 1上涨 2下跌      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "id": 0,
            "memo": "",
            "name": "",
            "range": 0,
            "rangeType": 0,
            "total": 0,
            "unitType": 0,
            "weekRange": 0,
            "weekRangeType": 0
        }
    ],
    "message": "",
    "msg": ""
}
```


