# 神奇农场管理后台项目概述

## 项目简介
这是一个基于Vue 3的现代化管理后台系统，用于管理"神奇农场"游戏的相关业务数据。系统提供用户管理、订单管理、退款管理和数据统计等核心功能。

## 技术栈
- **前端框架**: Vue 3.4+ (Composition API)
- **构建工具**: Vite 7.0+
- **语言**: TypeScript 5.0+
- **UI框架**: Ant Design Vue 4.0+
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **样式**: CSS3 + Ant Design Vue主题
- **图表**: 支持数据可视化展示

## 项目结构
```
admin-clone/
├── src/
│   ├── api/           # API接口定义
│   │   ├── auth.ts    # 认证相关接口
│   │   ├── statistics.ts # 统计数据接口
│   │   ├── user.ts    # 用户管理接口
│   │   └── order.ts   # 订单管理接口
│   ├── components/    # 公共组件
│   ├── router/        # 路由配置
│   ├── stores/        # Pinia状态管理
│   ├── utils/         # 工具函数
│   ├── views/         # 页面组件
│   │   ├── Dashboard.vue        # 实时统计页面
│   │   ├── StatisticsBoard.vue  # 看板数据页面
│   │   ├── UserManagement.vue   # 用户管理页面
│   │   ├── OrderManagement.vue  # 订单管理页面
│   │   └── RefundManagement.vue # 退款管理页面
│   └── main.ts        # 应用入口
├── public/            # 静态资源
├── dist/              # 构建输出目录
└── 接口信息/          # API接口文档
```

## 主要功能模块

### 1. 实时统计 (Dashboard)
- **功能**: 显示三个应用渠道的实时数据
- **应用渠道**: 
  - 神奇农场 (pasture)
  - 神奇农场-掌育 (pasture_zy)
  - 神奇农场-小米 (pasture_xm)
- **显示指标**: 
  - 今日订单数
  - 今日付款金额（元）
  - 今日新增用户数
- **API接口**: `/pasture/admin/realTimeReports/dimensionList`
- **特性**: 
  - 自动刷新（5分钟间隔）
  - 趋势对比（较昨日同期、较上周同期）
  - 响应式布局

### 2. 看板数据 (StatisticsBoard)
- **功能**: 详细的数据分析和统计
- **数据维度**: 
  - 用户总数 (ID: 189)
  - 新增用户数 (ID: 190)
  - 活跃用户数 (ID: 191)
  - 付费金额 (ID: 192)
  - 付费人数 (ID: 193)
  - 入账金额 (ID: 194)
  - 入账笔数 (ID: 195)
  - 出账金额 (ID: 196)
  - 出账笔数 (ID: 197)
  - 次日留存率 (ID: 198)
- **API接口**: `/pasture/admin/spectaculars/dimensionDetail`
- **筛选功能**: 时间范围选择
- **导出功能**: 支持数据导出

### 3. 用户管理
- 用户列表查看
- 用户信息编辑
- 用户状态管理
- 用户搜索和筛选

### 4. 订单管理
- 订单列表查看
- 订单详情查看
- 订单状态管理
- 订单搜索和筛选

### 5. 退款管理
- 退款申请列表
- 退款审核处理
- 退款状态跟踪

## API配置
- **基础URL**: `https://api.zj7hui.com/pasture`
- **认证方式**: Token认证 (Accesstoken header)
- **数据格式**: JSON
- **主要接口**:
  - 实时报告: `/admin/realTimeReports/dimensionList`
  - 看板数据: `/admin/spectaculars/dimensionDetail`
  - 用户管理: `/admin/user/*`
  - 订单管理: `/admin/order/*`

## 开发环境
- Node.js 16+
- npm 或 yarn
- 现代浏览器支持

## 快速开始
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

## 部署说明
项目构建后生成静态文件，可部署到任何支持静态文件托管的服务器。构建产物位于 `dist/` 目录。

## 最近更新 (2025-08-02)
1. **实时统计页面优化**:
   - 改用 `/admin/realTimeReports/dimensionList` 接口
   - 简化显示指标为3个核心指标
   - 优化数值格式化（整数不显示小数点）
   - 分应用渠道并行显示数据

2. **看板数据页面简化**:
   - 简化API参数，只保留4个核心参数
   - 移除应用渠道和统计类型选择器
   - 只保留时间范围筛选功能

3. **数据格式化优化**:
   - 订单数、用户数显示为整数（如：1个、1008人）
   - 金额类型保留2位小数（如：¥19.00）
   - 趋势数据来自真实API调用
