<template>
  <div class="dashboard">
    <div class="page-header">
      <h2>实时统计</h2>
      <div class="header-actions">
        <a-button type="primary" @click="loadAllAppData" :loading="loading">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新数据
        </a-button>
        <span v-if="lastUpdateTime" class="last-update" style="margin-left: 16px; color: #666;">
          最后更新：{{ lastUpdateTime }}
        </span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" tip="正在加载统计数据...">
        <div class="loading-placeholder"></div>
      </a-spin>
    </div>

    <!-- 数据展示 -->
    <div v-else class="apps-container">
      <!-- 神奇农场 -->
      <div class="app-section">
        <h3 class="app-title">神奇农场</h3>
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="8" :lg="8" v-for="dimension in appDataMap.pasture" :key="`pasture-${dimension.id}`">
            <a-card class="stat-card">
              <div class="stat-content">
                <div class="stat-title">{{ dimension.name }}</div>
                <div class="stat-value">
                  {{ formatValue(dimension.total, dimension.unitType, dimension.name) }}
                </div>
                <div class="stat-detail">
                  <div>较昨日同期：
                    <span :class="getTrendClass(dimension.rangeType)">
                      {{ formatPercentage(dimension.range) }}
                      {{ getTrendIcon(dimension.rangeType) }}
                    </span>
                  </div>
                  <div>较上周同期：
                    <span :class="getTrendClass(dimension.weekRangeType)">
                      {{ formatPercentage(dimension.weekRange) }}
                      {{ getTrendIcon(dimension.weekRangeType) }}
                    </span>
                  </div>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 神奇农场-掌育 -->
      <div class="app-section">
        <h3 class="app-title">神奇农场-掌育</h3>
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="8" :lg="8" v-for="dimension in appDataMap.pasture_zy" :key="`pasture_zy-${dimension.id}`">
            <a-card class="stat-card">
              <div class="stat-content">
                <div class="stat-title">{{ dimension.name }}</div>
                <div class="stat-value">
                  {{ formatValue(dimension.total, dimension.unitType, dimension.name) }}
                </div>
                <div class="stat-detail">
                  <div>较昨日同期：
                    <span :class="getTrendClass(dimension.rangeType)">
                      {{ formatPercentage(dimension.range) }}
                      {{ getTrendIcon(dimension.rangeType) }}
                    </span>
                  </div>
                  <div>较上周同期：
                    <span :class="getTrendClass(dimension.weekRangeType)">
                      {{ formatPercentage(dimension.weekRange) }}
                      {{ getTrendIcon(dimension.weekRangeType) }}
                    </span>
                  </div>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 神奇农场-小米 -->
      <div class="app-section">
        <h3 class="app-title">神奇农场-小米</h3>
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="8" :lg="8" v-for="dimension in appDataMap.pasture_xm" :key="`pasture_xm-${dimension.id}`">
            <a-card class="stat-card">
              <div class="stat-content">
                <div class="stat-title">{{ dimension.name }}</div>
                <div class="stat-value">
                  {{ formatValue(dimension.total, dimension.unitType, dimension.name) }}
                </div>
                <div class="stat-detail">
                  <div>较昨日同期：
                    <span :class="getTrendClass(dimension.rangeType)">
                      {{ formatPercentage(dimension.range) }}
                      {{ getTrendIcon(dimension.rangeType) }}
                    </span>
                  </div>
                  <div>较上周同期：
                    <span :class="getTrendClass(dimension.weekRangeType)">
                      {{ formatPercentage(dimension.weekRange) }}
                      {{ getTrendIcon(dimension.weekRangeType) }}
                    </span>
                  </div>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 空数据状态 -->
    <a-empty v-if="!loading && !hasAnyData" description="暂无统计数据" />
  </div>
</template>


<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import { getSpectacularsDimensionDetail, type RealTimeDimension } from '../api/statistics'

const loading = ref(false)
const lastUpdateTime = ref<string>('')
const autoRefreshTimer = ref<number | null>(null)

// 应用数据映射
const appDataMap = ref<{
  pasture: RealTimeDimension[]
  pasture_zy: RealTimeDimension[]
  pasture_xm: RealTimeDimension[]
}>({
  pasture: [],
  pasture_zy: [],
  pasture_xm: []
})

// 应用配置
const appConfigs = [
  { code: 'pasture', name: '神奇农场' },
  { code: 'pasture_zy', name: '神奇农场-掌育' },
  { code: 'pasture_xm', name: '神奇农场-小米' }
]

// 维度ID配置（根据看板数据接口讲解.txt）
const dimensionIds = [
  { id: 189, name: '用户总数' },
  { id: 190, name: '新增用户数' },
  { id: 191, name: '活跃用户数' },
  { id: 192, name: '付费金额' },
  { id: 193, name: '付费人数' },
  { id: 194, name: '入账金额' },
  { id: 195, name: '入账笔数' },
  { id: 196, name: '出账金额' },
  { id: 197, name: '出账笔数' },
  { id: 198, name: '次日留存率' }
]

// 检查是否有任何数据
const hasAnyData = computed(() => {
  return appDataMap.value.pasture.length > 0 ||
         appDataMap.value.pasture_zy.length > 0 ||
         appDataMap.value.pasture_xm.length > 0
})

// 加载单个应用的维度数据
const loadAppDimensionData = async (appCode: string) => {
  const results: RealTimeDimension[] = []

  for (const dimension of dimensionIds) {
    try {
      console.log(`加载 ${appCode} 的维度数据，ID: ${dimension.id}, 名称: ${dimension.name}`)

      const params = {
        appCode: appCode,
        id: dimension.id,
        endTime: '',
        granularity: 0,
        startTime: '',
        timeInterval: 0,
        type: 1 // 按日统计
      }

      const response = await getSpectacularsDimensionDetail(params)

      if (response.data && response.data.sections && response.data.sections.length > 0) {
        // 从sections中获取最新的统计数据
        const latestSection = response.data.sections[0]

        const dimensionData: RealTimeDimension = {
          id: dimension.id,
          name: dimension.name,
          total: latestSection.statisticsValue || 0,
          unitType: response.data.unitType || 2, // 默认为个
          range: latestSection.proportion || 0, // 使用proportion作为日环比
          rangeType: (latestSection.proportion || 0) >= 0 ? 1 : 0, // 1上涨 0下跌
          weekRange: 0, // 暂时设为0，如果API有周环比数据可以使用
          weekRangeType: 1,
          memo: response.data.memo || ''
        }

        results.push(dimensionData)
        console.log(`${appCode} - ${dimension.name} 数据加载成功:`, dimensionData)
      } else {
        console.warn(`${appCode} - ${dimension.name} 无数据`)
        // 即使无数据也添加一个默认项
        results.push({
          id: dimension.id,
          name: dimension.name,
          total: 0,
          unitType: 2,
          range: 0,
          rangeType: 1,
          weekRange: 0,
          weekRangeType: 1
        })
      }
    } catch (error) {
      console.error(`加载 ${appCode} - ${dimension.name} 数据失败:`, error)
      // 添加默认数据
      results.push({
        id: dimension.id,
        name: dimension.name,
        total: 0,
        unitType: 2,
        range: 0,
        rangeType: 1,
        weekRange: 0,
        weekRangeType: 1
      })
    }
  }

  return results
}

// 加载所有应用数据
const loadAllAppData = async () => {
  loading.value = true
  try {
    console.log('开始加载所有应用的维度数据')

    // 并行加载所有应用的数据
    const [pastureData, pastureZyData, pastureXmData] = await Promise.all([
      loadAppDimensionData('pasture'),
      loadAppDimensionData('pasture_zy'),
      loadAppDimensionData('pasture_xm')
    ])

    appDataMap.value = {
      pasture: pastureData,
      pasture_zy: pastureZyData,
      pasture_xm: pastureXmData
    }

    lastUpdateTime.value = new Date().toLocaleTimeString()
    console.log('所有应用数据加载完成:', appDataMap.value)

  } catch (error) {
    console.error('加载应用数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}



// 格式化数值显示
const formatValue = (value?: number, unitType?: number, dimensionName?: string) => {
  if (value === undefined || value === null) return '0'

  const precision = getUnitPrecision(unitType)
  const suffix = getUnitSuffix(unitType, dimensionName)

  // 只有真正的金额相关数据才显示¥符号
  if (dimensionName && (dimensionName.includes('金额') || dimensionName.includes('收入') || dimensionName.includes('营收') || dimensionName.includes('付费'))) {
    return `¥${value.toFixed(precision)}`
  }

  return `${value.toFixed(precision)}${suffix}`
}

// 格式化百分比
const formatPercentage = (value?: number) => {
  if (value === undefined || value === null) return '0%'
  return `${Math.abs(value).toFixed(2)}%`
}

// 获取单位精度
const getUnitPrecision = (unitType?: number) => {
  switch (unitType) {
    case 3: return 2 // 元
    default: return 0
  }
}

// 获取单位后缀
const getUnitSuffix = (unitType?: number, dimensionName?: string) => {
  // 根据维度名称智能判断单位
  if (dimensionName) {
    if (dimensionName.includes('用户') || dimensionName.includes('人数') || dimensionName.includes('新增用户')) {
      return '人'
    }
    if (dimensionName.includes('订单')) {
      return '个'
    }
  }

  // 回退到unitType判断
  switch (unitType) {
    case 1: return '秒'
    case 2: return '个'
    case 3: return ''  // 金额不需要后缀，在formatValue中处理
    default: return ''
  }
}

// 获取趋势样式类
const getTrendClass = (rangeType?: number) => {
  return rangeType === 1 ? 'trend-up' : 'trend-down'
}

// 获取趋势图标
const getTrendIcon = (rangeType?: number) => {
  return rangeType === 1 ? '↑' : '↓'
}

// 自动刷新机制
const startAutoRefresh = () => {
  stopAutoRefresh() // 先停止之前的定时器
  autoRefreshTimer.value = setInterval(() => {
    loadAllAppData()
  }, 5 * 60 * 1000) // 5分钟刷新一次
}

const stopAutoRefresh = () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
    autoRefreshTimer.value = null
  }
}

// 组件挂载时初始化
onMounted(async () => {
  console.log('Dashboard初始化，加载所有应用数据')

  // 加载所有应用的维度数据
  await loadAllAppData()

  // 启动自动刷新
  startAutoRefresh()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
}

.apps-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.app-section {
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
}

.app-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.stat-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-content {
  text-align: center;
  padding: 16px 0;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 12px;
}

.stat-detail {
  font-size: 12px;
  color: #999;
}

.stat-detail div {
  margin-bottom: 4px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.last-update {
  color: #999;
  font-size: 12px;
}

.loading-container {
  padding: 60px 0;
  text-align: center;
}

.loading-placeholder {
  height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .app-section {
    padding: 16px;
  }

  .stat-value {
    font-size: 24px;
  }
}
</style>
