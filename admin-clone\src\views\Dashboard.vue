<template>
  <div class="dashboard">
    <div class="page-header">
      <h2>实时统计</h2>
    </div>

    <!-- 筛选区域 -->
    <a-card class="filter-card" style="margin-bottom: 20px;">
      <a-form layout="inline">
        <a-form-item label="应用渠道">
          <a-select
            v-model:value="selectedAppCode"
            style="width: 160px;"
            @change="handleAppCodeChange"
            :loading="appChannelsLoading"
          >
            <a-select-option value="">所有</a-select-option>
            <a-select-option
              v-for="channel in appChannels"
              :key="channel.appCode"
              :value="channel.appCode"
            >
              {{ channel.appName }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="section-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <h3>实时统计数据</h3>
        <div>
          <a-button type="primary" @click="loadDimensions" :loading="loading">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新数据
          </a-button>
          <span v-if="lastUpdateTime" class="last-update" style="margin-left: 16px; color: #666;">
            最后更新：{{ lastUpdateTime }}
          </span>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <a-spin size="large" tip="正在加载统计数据...">
          <div class="loading-placeholder"></div>
        </a-spin>
      </div>

      <!-- 数据展示 -->
      <a-row :gutter="[24, 24]" v-else-if="dimensions.length > 0">
        <a-col :xs="24" :sm="8" :lg="8" v-for="dimension in dimensions" :key="dimension.id">
          <a-card class="stat-card">
            <div class="stat-content">
              <div class="stat-title">{{ dimension.name }}</div>
              <div class="stat-value">
                {{ formatValue(dimension.total, dimension.unitType, dimension.name) }}
              </div>
              <div class="stat-detail">
                <div>较昨日同期：
                  <span :class="getTrendClass(dimension.rangeType)">
                    {{ formatPercentage(dimension.range) }}
                    {{ getTrendIcon(dimension.rangeType) }}
                  </span>
                </div>
                <div>较上周同期：
                  <span :class="getTrendClass(dimension.weekRangeType)">
                    {{ formatPercentage(dimension.weekRange) }}
                    {{ getTrendIcon(dimension.weekRangeType) }}
                  </span>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 空数据状态 -->
      <a-empty v-else description="暂无统计数据" />
    </div>

  </div>
</template>


<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import { getDimensionList, getAppChannelList, type RealTimeDimension, type AppChannel } from '../api/statistics'


const loading = ref(false)
const dimensions = ref<RealTimeDimension[]>([])
const lastUpdateTime = ref<string>('')
const autoRefreshTimer = ref<number | null>(null)

// 应用渠道相关
const appChannelsLoading = ref(false)
const appChannels = ref<AppChannel[]>([])
const selectedAppCode = ref<string>('')



// 加载应用渠道列表
const loadAppChannels = async () => {
  appChannelsLoading.value = true
  try {
    const response = await getAppChannelList()
    console.log('应用渠道列表响应:', response)

    if (response.data && Array.isArray(response.data)) {
      appChannels.value = response.data
    } else {
      // 如果API还没有实现，使用真实的应用渠道数据
      appChannels.value = [
        { appCode: 'pasture', appName: '神奇农场' },
        { appCode: 'pasture_zy', appName: '神奇农场-掌育' },
        { appCode: 'pasture_xm', appName: '神奇农场-小米' }
      ]
    }
    console.log('应用渠道列表:', appChannels.value)
  } catch (error) {
    console.error('获取应用渠道列表失败:', error)
    // 使用真实的应用渠道数据
    appChannels.value = [
      { appCode: 'pasture', appName: '神奇农场' },
      { appCode: 'pasture_zy', appName: '神奇农场-掌育' },
      { appCode: 'pasture_xm', appName: '神奇农场-小米' }
    ]
    console.log('使用默认应用渠道列表:', appChannels.value)
  } finally {
    appChannelsLoading.value = false
  }
}

// 处理应用渠道变化
const handleAppCodeChange = () => {
  console.log('应用渠道变化:', selectedAppCode.value)
  loadDimensions()
}

// 加载维度数据
const loadDimensions = async () => {
  loading.value = true
  try {
    // 使用选中的应用渠道代码，如果没有选择则传空字符串（显示所有）
    const appCode = selectedAppCode.value || ''
    console.log('加载维度数据，appCode:', appCode)
    const response = await getDimensionList(appCode)
    if (response.data) {
      dimensions.value = response.data
      lastUpdateTime.value = new Date().toLocaleTimeString()
      console.log('数据加载成功，维度数量:', dimensions.value.length)
    } else {
      console.warn('API返回的数据为空')
      dimensions.value = []
    }
  } catch (error) {
    console.error('加载维度数据失败:', error)
    message.error('加载数据失败')
    dimensions.value = []
  } finally {
    loading.value = false
  }
}



// 格式化数值显示
const formatValue = (value?: number, unitType?: number, dimensionName?: string) => {
  if (value === undefined || value === null) return '0'

  const precision = getUnitPrecision(unitType)
  const suffix = getUnitSuffix(unitType, dimensionName)

  // 只有真正的金额相关数据才显示¥符号
  if (dimensionName && (dimensionName.includes('金额') || dimensionName.includes('收入') || dimensionName.includes('营收') || dimensionName.includes('付费'))) {
    return `¥${value.toFixed(precision)}`
  }

  return `${value.toFixed(precision)}${suffix}`
}

// 格式化百分比
const formatPercentage = (value?: number) => {
  if (value === undefined || value === null) return '0%'
  return `${Math.abs(value).toFixed(2)}%`
}

// 获取单位精度
const getUnitPrecision = (unitType?: number) => {
  switch (unitType) {
    case 3: return 2 // 元
    default: return 0
  }
}

// 获取单位后缀
const getUnitSuffix = (unitType?: number, dimensionName?: string) => {
  // 根据维度名称智能判断单位
  if (dimensionName) {
    if (dimensionName.includes('用户') || dimensionName.includes('人数') || dimensionName.includes('新增用户')) {
      return '人'
    }
    if (dimensionName.includes('订单')) {
      return '个'
    }
  }

  // 回退到unitType判断
  switch (unitType) {
    case 1: return '秒'
    case 2: return '个'
    case 3: return ''  // 金额不需要后缀，在formatValue中处理
    default: return ''
  }
}

// 获取趋势样式类
const getTrendClass = (rangeType?: number) => {
  return rangeType === 1 ? 'trend-up' : 'trend-down'
}

// 获取趋势图标
const getTrendIcon = (rangeType?: number) => {
  return rangeType === 1 ? '↑' : '↓'
}

// 自动刷新机制
const startAutoRefresh = () => {
  stopAutoRefresh() // 先停止之前的定时器
  autoRefreshTimer.value = setInterval(() => {
    loadDimensions()
  }, 5 * 60 * 1000) // 5分钟刷新一次
}

const stopAutoRefresh = () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
    autoRefreshTimer.value = null
  }
}

// 组件挂载时初始化
onMounted(async () => {
  console.log('Dashboard初始化，直接加载数据')

  // 先加载应用渠道列表
  await loadAppChannels()

  // 然后加载维度数据
  loadDimensions()

  startAutoRefresh()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.stats-section {
  margin-bottom: 32px;
}

.stats-section h3 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.stat-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  text-align: center;
  padding: 16px 0;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 12px;
}

.stat-detail {
  font-size: 12px;
  color: #999;
}

.stat-detail div {
  margin-bottom: 4px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.last-update {
  color: #999;
  font-size: 12px;
}

.loading-container {
  padding: 60px 0;
}

.loading-placeholder {
  height: 200px;
}

.no-app-selected {
  padding: 60px 0;
  text-align: center;
}
</style>
