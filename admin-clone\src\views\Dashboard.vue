<template>
  <div class="dashboard">
    <div class="page-header">
      <h2>实时统计</h2>
      <div class="header-actions">
        <a-button type="primary" @click="loadAllAppData" :loading="loading">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新数据
        </a-button>
        <span v-if="lastUpdateTime" class="last-update" style="margin-left: 16px; color: #666;">
          最后更新：{{ lastUpdateTime }}
        </span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" tip="正在加载统计数据...">
        <div class="loading-placeholder"></div>
      </a-spin>
    </div>

    <!-- 数据展示 -->
    <div v-else class="apps-container">
      <!-- 神奇农场 -->
      <div class="app-section">
        <h3 class="app-title">神奇农场</h3>
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="8" :lg="8" v-for="dimension in appDataMap.pasture" :key="`pasture-${dimension.id}`">
            <a-card class="stat-card">
              <div class="stat-content">
                <div class="stat-title">{{ dimension.name }}</div>
                <div class="stat-value">
                  {{ formatValue(dimension.total, dimension.unitType, dimension.name) }}
                </div>
                <div class="stat-detail">
                  <div>较昨日同期：
                    <span :class="getTrendClass(dimension.rangeType)">
                      {{ formatPercentage(dimension.range) }}
                      {{ getTrendIcon(dimension.rangeType) }}
                    </span>
                  </div>
                  <div>较上周同期：
                    <span :class="getTrendClass(dimension.weekRangeType)">
                      {{ formatPercentage(dimension.weekRange) }}
                      {{ getTrendIcon(dimension.weekRangeType) }}
                    </span>
                  </div>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 神奇农场-掌育 -->
      <div class="app-section">
        <h3 class="app-title">神奇农场-掌育</h3>
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="8" :lg="8" v-for="dimension in appDataMap.pasture_zy" :key="`pasture_zy-${dimension.id}`">
            <a-card class="stat-card">
              <div class="stat-content">
                <div class="stat-title">{{ dimension.name }}</div>
                <div class="stat-value">
                  {{ formatValue(dimension.total, dimension.unitType, dimension.name) }}
                </div>
                <div class="stat-detail">
                  <div>较昨日同期：
                    <span :class="getTrendClass(dimension.rangeType)">
                      {{ formatPercentage(dimension.range) }}
                      {{ getTrendIcon(dimension.rangeType) }}
                    </span>
                  </div>
                  <div>较上周同期：
                    <span :class="getTrendClass(dimension.weekRangeType)">
                      {{ formatPercentage(dimension.weekRange) }}
                      {{ getTrendIcon(dimension.weekRangeType) }}
                    </span>
                  </div>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 神奇农场-小米 -->
      <div class="app-section">
        <h3 class="app-title">神奇农场-小米</h3>
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="8" :lg="8" v-for="dimension in appDataMap.pasture_xm" :key="`pasture_xm-${dimension.id}`">
            <a-card class="stat-card">
              <div class="stat-content">
                <div class="stat-title">{{ dimension.name }}</div>
                <div class="stat-value">
                  {{ formatValue(dimension.total, dimension.unitType, dimension.name) }}
                </div>
                <div class="stat-detail">
                  <div>较昨日同期：
                    <span :class="getTrendClass(dimension.rangeType)">
                      {{ formatPercentage(dimension.range) }}
                      {{ getTrendIcon(dimension.rangeType) }}
                    </span>
                  </div>
                  <div>较上周同期：
                    <span :class="getTrendClass(dimension.weekRangeType)">
                      {{ formatPercentage(dimension.weekRange) }}
                      {{ getTrendIcon(dimension.weekRangeType) }}
                    </span>
                  </div>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 空数据状态 -->
    <a-empty v-if="!loading && !hasAnyData" description="暂无统计数据" />
  </div>
</template>


<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import { getDimensionList, type RealTimeDimension } from '../api/statistics'

const loading = ref(false)
const lastUpdateTime = ref<string>('')
const autoRefreshTimer = ref<number | null>(null)

// 应用数据映射
const appDataMap = ref<{
  pasture: RealTimeDimension[]
  pasture_zy: RealTimeDimension[]
  pasture_xm: RealTimeDimension[]
}>({
  pasture: [],
  pasture_zy: [],
  pasture_xm: []
})

// 应用配置
const appConfigs = [
  { code: 'pasture', name: '神奇农场' },
  { code: 'pasture_zy', name: '神奇农场-掌育' },
  { code: 'pasture_xm', name: '神奇农场-小米' }
]

// 需要显示的维度名称（只显示这三个）
const targetDimensions = ['今日订单数', '今日付款金额', '今日新增用户数']

// 检查是否有任何数据
const hasAnyData = computed(() => {
  return appDataMap.value.pasture.length > 0 ||
         appDataMap.value.pasture_zy.length > 0 ||
         appDataMap.value.pasture_xm.length > 0
})

// 过滤维度数据，只保留需要的三个指标
const filterTargetDimensions = (dimensions: RealTimeDimension[]) => {
  return dimensions.filter(dimension =>
    targetDimensions.includes(dimension.name || '')
  )
}

// 加载单个应用的维度数据
const loadAppDimensionData = async (appCode: string) => {
  try {
    console.log(`加载 ${appCode} 的实时维度数据`)

    const response = await getDimensionList(appCode)

    if (response.data && Array.isArray(response.data)) {
      // 过滤出需要的维度数据
      const filteredData = filterTargetDimensions(response.data)
      console.log(`${appCode} 数据加载成功，过滤后维度数量:`, filteredData.length)
      return filteredData
    } else {
      console.warn(`${appCode} API返回的数据为空`)
      return []
    }
  } catch (error) {
    console.error(`加载 ${appCode} 维度数据失败:`, error)
    return []
  }
}

// 加载所有应用数据
const loadAllAppData = async () => {
  loading.value = true
  try {
    console.log('开始加载所有应用的实时维度数据')

    // 并行加载所有应用的数据
    const [pastureData, pastureZyData, pastureXmData] = await Promise.all([
      loadAppDimensionData('pasture'),
      loadAppDimensionData('pasture_zy'),
      loadAppDimensionData('pasture_xm')
    ])

    appDataMap.value = {
      pasture: pastureData,
      pasture_zy: pastureZyData,
      pasture_xm: pastureXmData
    }

    lastUpdateTime.value = new Date().toLocaleTimeString()
    console.log('所有应用数据加载完成:', appDataMap.value)

  } catch (error) {
    console.error('加载应用数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}



// 格式化数值显示
const formatValue = (value?: number, unitType?: number, dimensionName?: string) => {
  if (value === undefined || value === null) return '0'

  const precision = getUnitPrecision(unitType)
  const suffix = getUnitSuffix(unitType, dimensionName)

  // 只有真正的金额相关数据才显示¥符号
  if (dimensionName && (dimensionName.includes('金额') || dimensionName.includes('收入') || dimensionName.includes('营收') || dimensionName.includes('付费'))) {
    return `¥${value.toFixed(precision)}`
  }

  return `${value.toFixed(precision)}${suffix}`
}

// 格式化百分比
const formatPercentage = (value?: number) => {
  if (value === undefined || value === null) return '0%'
  return `${Math.abs(value).toFixed(2)}%`
}

// 获取单位精度
const getUnitPrecision = (unitType?: number) => {
  switch (unitType) {
    case 3: return 2 // 元
    default: return 0
  }
}

// 获取单位后缀
const getUnitSuffix = (unitType?: number, dimensionName?: string) => {
  // 根据维度名称智能判断单位
  if (dimensionName) {
    if (dimensionName.includes('用户') || dimensionName.includes('人数') || dimensionName.includes('新增用户')) {
      return '人'
    }
    if (dimensionName.includes('订单')) {
      return '个'
    }
  }

  // 回退到unitType判断
  switch (unitType) {
    case 1: return '秒'
    case 2: return '个'
    case 3: return ''  // 金额不需要后缀，在formatValue中处理
    default: return ''
  }
}

// 获取趋势样式类
const getTrendClass = (rangeType?: number) => {
  return rangeType === 1 ? 'trend-up' : 'trend-down'
}

// 获取趋势图标
const getTrendIcon = (rangeType?: number) => {
  return rangeType === 1 ? '↑' : '↓'
}

// 自动刷新机制
const startAutoRefresh = () => {
  stopAutoRefresh() // 先停止之前的定时器
  autoRefreshTimer.value = setInterval(() => {
    loadAllAppData()
  }, 5 * 60 * 1000) // 5分钟刷新一次
}

const stopAutoRefresh = () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
    autoRefreshTimer.value = null
  }
}

// 组件挂载时初始化
onMounted(async () => {
  console.log('Dashboard初始化，加载所有应用数据')

  // 加载所有应用的维度数据
  await loadAllAppData()

  // 启动自动刷新
  startAutoRefresh()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
}

.apps-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.app-section {
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
}

.app-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.stat-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-content {
  text-align: center;
  padding: 16px 0;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 12px;
}

.stat-detail {
  font-size: 12px;
  color: #999;
}

.stat-detail div {
  margin-bottom: 4px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.last-update {
  color: #999;
  font-size: 12px;
}

.loading-container {
  padding: 60px 0;
  text-align: center;
}

.loading-placeholder {
  height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .app-section {
    padding: 16px;
  }

  .stat-value {
    font-size: 24px;
  }
}
</style>
