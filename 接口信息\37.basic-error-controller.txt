errorHtml

**接口地址** `/pasture/error`


**请求方式** `GET`


**consumes** ``


**produces** `["text/html"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |ModelAndView                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| empty     |      |    boolean   |       |
            | model     |      |    object   |       |
            | modelMap     |      |    object   |       |
            | reference     |      |    boolean   |       |
            | status     |      |    string   |       |
            | view     |      |    View   |   View    |
            | viewName     |      |    string   |       |
            



**schema属性说明**
  
**View**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| contentType         |           |  string   |      |
            




**响应示例**


```json
{
    "empty": true,
    "model": {},
    "modelMap": {},
    "reference": true,
    "status": "",
    "view": {
        "contentType": ""
    },
    "viewName": ""
}
```



-----------------分割线
errorHtml

**接口地址** `/pasture/error`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["text/html"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |ModelAndView                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| empty     |      |    boolean   |       |
            | model     |      |    object   |       |
            | modelMap     |      |    object   |       |
            | reference     |      |    boolean   |       |
            | status     |      |    string   |       |
            | view     |      |    View   |   View    |
            | viewName     |      |    string   |       |
            



**schema属性说明**
  
**View**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| contentType         |           |  string   |      |
            




**响应示例**


```json
{
    "empty": true,
    "model": {},
    "modelMap": {},
    "reference": true,
    "status": "",
    "view": {
        "contentType": ""
    },
    "viewName": ""
}
```



-----------------分割线
errorHtml

**接口地址** `/pasture/error`


**请求方式** `PUT`


**consumes** `["application/json"]`


**produces** `["text/html"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |ModelAndView                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| empty     |      |    boolean   |       |
            | model     |      |    object   |       |
            | modelMap     |      |    object   |       |
            | reference     |      |    boolean   |       |
            | status     |      |    string   |       |
            | view     |      |    View   |   View    |
            | viewName     |      |    string   |       |
            



**schema属性说明**
  
**View**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| contentType         |           |  string   |      |
            




**响应示例**


```json
{
    "empty": true,
    "model": {},
    "modelMap": {},
    "reference": true,
    "status": "",
    "view": {
        "contentType": ""
    },
    "viewName": ""
}
```



-----------------分割线
errorHtml

**接口地址** `/pasture/error`


**请求方式** `DELETE`


**consumes** ``


**produces** `["text/html"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |ModelAndView                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| empty     |      |    boolean   |       |
            | model     |      |    object   |       |
            | modelMap     |      |    object   |       |
            | reference     |      |    boolean   |       |
            | status     |      |    string   |       |
            | view     |      |    View   |   View    |
            | viewName     |      |    string   |       |
            



**schema属性说明**
  
**View**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| contentType         |           |  string   |      |
            




**响应示例**


```json
{
    "empty": true,
    "model": {},
    "modelMap": {},
    "reference": true,
    "status": "",
    "view": {
        "contentType": ""
    },
    "viewName": ""
}
```



-----------------分割线
errorHtml

**接口地址** `/pasture/error`


**请求方式** `PATCH`


**consumes** `["application/json"]`


**produces** `["text/html"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |ModelAndView                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| empty     |      |    boolean   |       |
            | model     |      |    object   |       |
            | modelMap     |      |    object   |       |
            | reference     |      |    boolean   |       |
            | status     |      |    string   |       |
            | view     |      |    View   |   View    |
            | viewName     |      |    string   |       |
            



**schema属性说明**
  
**View**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| contentType         |           |  string   |      |
            




**响应示例**


```json
{
    "empty": true,
    "model": {},
    "modelMap": {},
    "reference": true,
    "status": "",
    "view": {
        "contentType": ""
    },
    "viewName": ""
}
```



-----------------分割线
errorHtml

**接口地址** `/pasture/error`


**请求方式** `OPTIONS`


**consumes** `["application/json"]`


**produces** `["text/html"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |ModelAndView                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| empty     |      |    boolean   |       |
            | model     |      |    object   |       |
            | modelMap     |      |    object   |       |
            | reference     |      |    boolean   |       |
            | status     |      |    string   |       |
            | view     |      |    View   |   View    |
            | viewName     |      |    string   |       |
            



**schema属性说明**
  
**View**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| contentType         |           |  string   |      |
            




**响应示例**


```json
{
    "empty": true,
    "model": {},
    "modelMap": {},
    "reference": true,
    "status": "",
    "view": {
        "contentType": ""
    },
    "viewName": ""
}
```


---------------------分割线
errorHtml

**接口地址** `/pasture/error`


**请求方式** `HEAD`


**consumes** `["application/json"]`


**produces** `["text/html"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |ModelAndView                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| empty     |      |    boolean   |       |
            | model     |      |    object   |       |
            | modelMap     |      |    object   |       |
            | reference     |      |    boolean   |       |
            | status     |      |    string   |       |
            | view     |      |    View   |   View    |
            | viewName     |      |    string   |       |
            



**schema属性说明**
  
**View**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| contentType         |           |  string   |      |
            




**响应示例**


```json
{
    "empty": true,
    "model": {},
    "modelMap": {},
    "reference": true,
    "status": "",
    "view": {
        "contentType": ""
    },
    "viewName": ""
}
```



