app列表

**接口地址** `/pasture/admin/login/appList`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```


---------分割线
后台管理员登录
**接口地址** `/pasture/admin/login/login`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| loginParameters         |      loginParameters   |     body        |       true      | 账号登录参数   | 账号登录参数     |
            



**schema属性说明**
  
**账号登录参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| account  | 登录账号 |   body    |   true   |string  |       |
| pwd  | 登录密码 |   body    |   true   |string  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```


--------分割线
获取用户菜单

**接口地址** `/pasture/admin/login/queryMenu`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«PermissionBo»»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   PermissionBo    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**PermissionBo**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| children         |           |  array   | PermissionBo     |
            | composingKey         |           |  string   |      |
            | createTime         |           |  date-time   |      |
            | createUserId         |           |  string   |      |
            | hasRelevance         |           |  string   |      |
            | id         |           |  int32   |      |
            | isAction         |     是否允许点击      |  string   |      |
            | label         |           |  string   |      |
            | logo         |           |  string   |      |
            | name         |           |  string   |      |
            | parentId         |           |  string   |      |
            | seq         |           |  int32   |      |
            | type         |           |  string   |      |
            | updateTime         |           |  date-time   |      |
            | updateUserId         |           |  string   |      |
            | url         |           |  string   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "children": [
                {
                    "children": [
                        {}
                    ],
                    "composingKey": "",
                    "createTime": "",
                    "createUserId": "",
                    "hasRelevance": "",
                    "id": 0,
                    "isAction": "",
                    "label": "",
                    "logo": "",
                    "name": "",
                    "parentId": "",
                    "seq": 0,
                    "type": "",
                    "updateTime": "",
                    "updateUserId": "",
                    "url": ""
                }
            ],
            "composingKey": "",
            "createTime": "",
            "createUserId": "",
            "hasRelevance": "",
            "id": 0,
            "isAction": "",
            "label": "",
            "logo": "",
            "name": "",
            "parentId": "",
            "seq": 0,
            "type": "",
            "updateTime": "",
            "updateUserId": "",
            "url": ""
        }
    ],
    "message": "",
    "msg": ""
}
```


