playerEvent新增

**接口地址** `/pasture/player/event/add`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerEvent         |      playerEvent   |     body        |       true      | com.qihui.pasture.model.PlayerEvent   | com.qihui.pasture.model.PlayerEvent     |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerEvent**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| createTime  | createTime |   body    |   false   |date-time  |       |
| eventId  | eventId事件id |   body    |   false   |int64  |       |
| eventType  | eventType操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽 |   body    |   false   |int32  |       |
| eventVal  | eventVal事件值 |   body    |   false   |int32  |       |
| id  | id |   body    |   false   |int64  |       |
| loseTime  | 失效时间 |   body    |   false   |date-time  |       |
| playerId  | playerId |   body    |   false   |int64  |       |
| status  | status玩家状态  1-正常 2已解决  |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



---------------分割线
playerEvent删除

**接口地址** `/pasture/player/event/delete`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



---------------分割线
playerEvent获取详情

**接口地址** `/pasture/player/event/detail`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«com.qihui.pasture.model.PlayerEvent»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    com.qihui.pasture.model.PlayerEvent   |   com.qihui.pasture.model.PlayerEvent    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerEvent**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| createTime         |     createTime      |  date-time   |      |
            | eventId         |     eventId事件id      |  int64   |      |
            | eventType         |     eventType操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽      |  int32   |      |
            | eventVal         |     eventVal事件值      |  int32   |      |
            | id         |     id      |  int64   |      |
            | loseTime         |     失效时间      |  date-time   |      |
            | playerId         |     playerId      |  int64   |      |
            | status         |     status玩家状态  1-正常 2已解决       |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "createTime": "",
        "eventId": 0,
        "eventType": 0,
        "eventVal": 0,
        "id": 0,
        "loseTime": "",
        "playerId": 0,
        "status": 0
    },
    "message": "",
    "msg": ""
}
```



---------------分割线
playerEvent获取列表

**接口地址** `/pasture/player/event/list`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| page         |      page   |     query        |       false      | integer   |      |
            | playerEvent         |      playerEvent   |     body        |       true      | com.qihui.pasture.model.PlayerEvent   | com.qihui.pasture.model.PlayerEvent     |
            | size         |      size   |     query        |       false      | integer   |      |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerEvent**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| createTime  | createTime |   body    |   false   |date-time  |       |
| eventId  | eventId事件id |   body    |   false   |int64  |       |
| eventType  | eventType操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽 |   body    |   false   |int32  |       |
| eventVal  | eventVal事件值 |   body    |   false   |int32  |       |
| id  | id |   body    |   false   |int64  |       |
| loseTime  | 失效时间 |   body    |   false   |date-time  |       |
| playerId  | playerId |   body    |   false   |int64  |       |
| status  | status玩家状态  1-正常 2已解决  |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«com.qihui.pasture.model.PlayerEvent»»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   com.qihui.pasture.model.PlayerEvent    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerEvent**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| createTime         |     createTime      |  date-time   |      |
            | eventId         |     eventId事件id      |  int64   |      |
            | eventType         |     eventType操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽      |  int32   |      |
            | eventVal         |     eventVal事件值      |  int32   |      |
            | id         |     id      |  int64   |      |
            | loseTime         |     失效时间      |  date-time   |      |
            | playerId         |     playerId      |  int64   |      |
            | status         |     status玩家状态  1-正常 2已解决       |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "createTime": "",
            "eventId": 0,
            "eventType": 0,
            "eventVal": 0,
            "id": 0,
            "loseTime": "",
            "playerId": 0,
            "status": 0
        }
    ],
    "message": "",
    "msg": ""
}
```



---------------分割线
playerEvent更新

**接口地址** `/pasture/player/event/update`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerEvent         |      playerEvent   |     body        |       true      | com.qihui.pasture.model.PlayerEvent   | com.qihui.pasture.model.PlayerEvent     |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerEvent**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| createTime  | createTime |   body    |   false   |date-time  |       |
| eventId  | eventId事件id |   body    |   false   |int64  |       |
| eventType  | eventType操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽 |   body    |   false   |int32  |       |
| eventVal  | eventVal事件值 |   body    |   false   |int32  |       |
| id  | id |   body    |   false   |int64  |       |
| loseTime  | 失效时间 |   body    |   false   |date-time  |       |
| playerId  | playerId |   body    |   false   |int64  |       |
| status  | status玩家状态  1-正常 2已解决  |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```


