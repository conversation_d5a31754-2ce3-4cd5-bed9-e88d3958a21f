# 导出API对接完成文档

## 📋 总览

已完成三个导出功能的API对接，全部改为超链接方式，无需token认证。

## 🔗 API对接详情

### 1. 订单导出接口

**接口路径**: `/pasture/admin/order/export`  
**请求方法**: GET  
**页面位置**: 订单管理页面 (`src/views/OrderManagement.vue`)

**参数说明**:
- `appCode`: 应用编码 (必填)
- `startTime`: 开始时间 (必填) - 格式: YYYY-MM-DD HH:mm:ss
- `endTime`: 结束时间 (必填) - 格式: YYYY-MM-DD HH:mm:ss
- `type`: 订单类型 (必填) - 1:所有订单, 2:入账订单

**URL示例**:
```
https://api.zj7hui.com/pasture/admin/order/export?appCode=pasture&startTime=2025-01-01 00:00:00&endTime=2025-01-31 23:59:59&type=2
```

### 2. 退款导出接口

**接口路径**: `/pasture/order/refund/export`  
**请求方法**: GET  
**页面位置**: 退款管理页面 (`src/views/RefundManagement.vue`)

**参数说明**:
- `appCode`: 应用编码 (必填)
- `startTime`: 开始时间 (必填) - 格式: YYYY-MM-DD HH:mm:ss
- `endTime`: 结束时间 (必填) - 格式: YYYY-MM-DD HH:mm:ss
- `type`: 订单类型 (必填) - 1:所有订单, 2:出账订单

**URL示例**:
```
https://api.zj7hui.com/pasture/order/refund/export?appCode=pasture&startTime=2025-01-01 00:00:00&endTime=2025-01-31 23:59:59&type=1
```

### 3. 看板数据导出接口

**接口路径**: `/pasture/admin/spectaculars/report`  
**请求方法**: GET  
**页面位置**: 统计看板页面 (`src/views/StatisticsBoard.vue`)

**参数说明**:
- `id`: 维度ID (必填) - 见下方维度ID对照表
- `startTime`: 开始时间 (可选) - 格式: YYYY-MM-DD HH:mm:ss
- `endTime`: 结束时间 (可选) - 格式: YYYY-MM-DD HH:mm:ss
- `timeInterval`: 时间间隔 (可选) - 统计粒度

**维度ID对照表**:
- 189: 用户总数
- 190: 新增用户数
- 191: 活跃用户数
- 192: 付费金额
- 193: 付费人数
- 194: 入账金额
- 195: 入账笔数
- 196: 出账金额
- 197: 出账笔数
- 198: 次日留存率

**URL示例**:
```
https://api.zj7hui.com/pasture/admin/spectaculars/report?id=189&startTime=2025-01-01 00:00:00&endTime=2025-01-31 23:59:59&timeInterval=1
```

## 🔧 技术实现

### 导出方式
- **方法**: 超链接直接跳转
- **认证**: 无需token认证
- **窗口**: 新窗口打开 (`target="_blank"`)

### 代码实现
```javascript
// 构建查询参数
const params = new URLSearchParams({
  // 根据接口添加必填参数
})

// 构建导出URL
const exportUrl = `https://api.zj7hui.com${endpoint}?${params.toString()}`

// 创建a标签并跳转
const link = document.createElement('a')
link.href = exportUrl
link.target = '_blank'
document.body.appendChild(link)
link.click()
document.body.removeChild(link)
```

## 📝 用户界面

### 订单导出弹窗
- 应用渠道选择
- 订单时间范围选择 (必填)
- 订单类型选择 (必填)

### 退款导出弹窗
- 应用渠道选择
- 退款时间范围选择 (必填)
- 订单类型选择 (必填)

### 看板数据导出
- 每个维度独立的导出按钮
- 自动使用当前选择的时间范围
- 支持10个不同维度的数据导出

## ✅ 完成状态

- [x] 订单导出API对接完成
- [x] 退款导出API对接完成
- [x] 看板数据导出API对接完成
- [x] 移除token认证依赖
- [x] 改为超链接跳转方式
- [x] 添加必要的参数验证
- [x] 优化用户界面和体验

## 🧪 测试建议

1. **订单导出测试**:
   - 选择不同的时间范围
   - 测试不同的订单类型

2. **退款导出测试**:
   - 选择不同的时间范围
   - 测试不同的订单类型

3. **看板数据导出测试**:
   - 测试所有10个维度的导出
   - 测试有时间范围和无时间范围的情况
   - 验证URL构建是否正确

## 🚨 已知问题和解决方案

### POI库版本问题

**错误信息**：
```json
{
  "code": "400",
  "message": "Handler dispatch failed; nested exception is java.lang.NoSuchMethodError: org.apache.poi.ss.usermodel.WorkbookFactory.create(Z)Lorg/apache/poi/ss/usermodel/Workbook;"
}
```

**问题原因**：Apache POI库版本不兼容

**解决方案**：
1. 升级POI库到最新版本 (5.2.4)
2. 或修改代码使用兼容的WorkbookFactory方法

详细解决方案请参考 `EXPORT_SOLUTION.md` 文档。

## 📞 后续支持

当后端修复POI库问题后，这些导出功能应该可以直接使用。如果需要重新添加token认证，只需要在URL参数中添加`Accesstoken`参数即可。

---

**更新时间**: 2025-01-26
**状态**: API对接完成，等待后端修复POI库问题
