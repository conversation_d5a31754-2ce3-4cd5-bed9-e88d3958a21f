import { createApp } from 'vue'
import { createRouter, createWebHashHistory } from 'vue-router'
import { createPinia } from 'pinia'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'

import App from './App.vue'

console.log('🚀 开始加载管理后台应用...')

// 创建路由 - 使用hash模式避免服务器配置问题
const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      component: () => import('./views/Layout.vue'),
      redirect: '/dashboard',
      children: [
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('./views/Dashboard.vue'),
          meta: { title: '实时统计' }
        },
        {
          path: 'users',
          name: 'UserManagement',
          component: () => import('./views/UserManagement.vue'),
          meta: { title: '用户管理' }
        },
        {
          path: 'orders',
          name: 'OrderManagement',
          component: () => import('./views/OrderManagement.vue'),
          meta: { title: '订单管理' }
        },
        {
          path: 'refunds',
          name: 'RefundManagement',
          component: () => import('./views/RefundManagement.vue'),
          meta: { title: '退款管理' }
        },
        {
          path: 'statistics',
          name: 'StatisticsBoard',
          component: () => import('./views/StatisticsBoard.vue'),
          meta: { title: '数据看板' }
        }
      ]
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('./views/Login.vue')
    }
  ]
})

// 创建Pinia状态管理
const pinia = createPinia()

// 创建Vue应用
const app = createApp(App)

// 使用插件
app.use(router)
app.use(pinia)
app.use(Antd)

console.log('🎯 挂载管理后台应用...')
app.mount('#app')
console.log('🎉 管理后台应用启动成功！')
