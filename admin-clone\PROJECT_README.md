# 管理后台系统 - 项目说明文档

## 📋 项目概述

这是一个基于 Vue 3 + TypeScript + Ant Design Vue 构建的现代化管理后台系统，用于管理用户、订单、退款等业务数据，并提供实时统计功能。

### 🎯 主要功能
- **用户管理**: 玩家信息查看、搜索、导出
- **订单管理**: 订单查询、退款处理、数据导出
- **退款管理**: 退款订单查看、状态跟踪、数据导出
- **实时统计**: 多维度数据统计、趋势分析
- **数据看板**: 综合业务数据展示

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Vue 3.4+ (Composition API)
- **语言**: TypeScript 5.0+
- **构建工具**: Vite 7.0+
- **UI组件库**: Ant Design Vue 4.0+
- **状态管理**: Pinia
- **路由**: Vue Router 4.0+
- **HTTP客户端**: Axios
- **日期处理**: Day.js
- **图标**: Ant Design Icons Vue

### 项目结构
```
admin-clone/
├── src/
│   ├── api/           # API接口定义
│   ├── components/    # 公共组件
│   ├── stores/        # Pinia状态管理
│   ├── utils/         # 工具函数
│   ├── views/         # 页面组件
│   ├── App.vue        # 根组件
│   └── main.ts        # 入口文件
├── public/            # 静态资源
├── dist/              # 构建输出
├── vite.config.ts     # Vite配置
├── package.json       # 依赖配置
└── tsconfig.json      # TypeScript配置
```

## 🔧 核心功能模块

### 1. 应用渠道管理 (AppStore)
- **位置**: `src/stores/app.ts`
- **功能**: 统一管理应用渠道列表
- **API**: `/pasture/admin/login/appList`
- **特点**: 
  - 自动从API获取渠道列表
  - 提供备用默认渠道
  - 全局状态管理

### 2. 用户管理模块
- **页面**: `src/views/UserManagement.vue`
- **功能**: 
  - 玩家信息查询（ID、账号、渠道等）
  - 玩家详情查看
  - 玩家币信息查看
  - 用户数据导出
- **API**: 
  - 列表: `/pasture/admin/user/list`
  - 详情: `/pasture/admin/user/detail`
  - 玩家币: `/pasture/admin/user/coin`

### 3. 订单管理模块
- **页面**: `src/views/OrderManagement.vue`
- **功能**:
  - 订单查询（渠道、编号、状态、时间等）
  - 订单退款处理
  - 订单数据导出
- **API**:
  - 列表: `/pasture/admin/order/list`
  - 退款: `/pasture/admin/order/refund`

### 4. 退款管理模块
- **页面**: `src/views/RefundManagement.vue`
- **功能**:
  - 退款订单查询
  - 退款状态跟踪
  - 退款数据导出
- **API**:
  - 列表: `/pasture/admin/refund/list`

### 5. 统计模块
- **实时统计**: `src/views/Dashboard.vue`
- **数据看板**: `src/views/StatisticsBoard.vue`
- **功能**:
  - 多维度实时数据统计
  - 趋势分析（日环比、周环比）
  - 自动刷新机制
- **API**:
  - 维度数据: `/pasture/admin/statistics/dimension`

## 🛠️ 工具函数

### 时间格式化 (`src/utils/dateFormat.ts`)
- **formatToApiDate**: 格式化为API需要的时间格式 (yyyyMMdd HHmmss)
- **formatDateRange**: 处理日期范围
- **handleDateRangeChange**: 日期选择器变化处理
- **parseApiDate**: 解析API返回的时间格式

### 导出功能 (`src/utils/export.ts`)
- **exportByUrl**: 通过URL拼接方式导出文件
- **exportUserList**: 用户列表导出
- **exportOrderList**: 订单列表导出
- **exportRefundList**: 退款列表导出
- **getExportParams**: 获取导出参数

## 🔌 API接口规范

### 基础配置
- **Base URL**: `https://api.zj7hui.com`
- **请求方式**: 主要使用 POST
- **认证方式**: Token认证（存储在localStorage）

### 通用响应格式
```typescript
interface ApiResponse<T> {
  code: string
  data: T
  message: string
  msg: string
}
```

### 分页参数
```typescript
interface PaginationParams {
  page: number    // 页码，从1开始
  size: number    // 每页数量
}
```

### 时间参数格式
- **格式**: `yyyyMMdd HHmmss`
- **示例**: `20241225 143000`
- **范围查询**: `startTime` 和 `endTime`

## 🚀 部署说明

### 开发环境
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问地址
http://localhost:5173
```

### 生产环境
```bash
# 构建项目
npm run build-fast

# 构建输出
dist/ 目录包含所有静态文件

# 部署方式
1. 上传 dist/ 目录所有文件到服务器
2. 配置静态文件服务器
3. 支持 Hash 路由，无需特殊服务器配置
```

### 部署配置
- **路由模式**: Hash模式 (`createWebHashHistory`)
- **资源路径**: 相对路径 (`base: './'`)
- **兼容性**: 支持各种部署环境

## 🔍 开发指南

### 添加新页面
1. 在 `src/views/` 创建页面组件
2. 在 `src/main.ts` 添加路由配置
3. 在 `src/views/Layout.vue` 添加菜单项

### 添加新API
1. 在 `src/api/` 对应模块文件中定义接口
2. 定义TypeScript类型
3. 在组件中调用API

### 状态管理
- 使用 Pinia 进行状态管理
- 全局状态存储在 `src/stores/` 目录
- 组件内使用 `useXxxStore()` 访问状态

### 样式规范
- 使用 Scoped CSS
- 遵循 Ant Design 设计规范
- 响应式设计支持

## 📝 注意事项

### 安全性
- 所有API请求需要认证
- 敏感数据（如账号）可能经过加密
- 前端需要处理Base64解码

### 性能优化
- 实时统计页面有自动刷新机制（5分钟）
- 大数据列表支持分页
- 组件懒加载

### 错误处理
- 统一的错误提示机制
- API错误自动处理
- 网络异常友好提示

## 🔄 更新日志

### v2.0 (当前版本)
- ✅ 对接真实应用渠道API
- ✅ 修复时间格式为 yyyyMMdd HHmmss
- ✅ 添加数据导出功能
- ✅ 优化部署配置（Hash路由 + 相对路径）
- ✅ 完善项目文档

### v1.0
- ✅ 基础功能实现
- ✅ 用户、订单、退款管理
- ✅ 实时统计功能
- ✅ 响应式设计

## 🤝 开发团队

本项目由AI助手协助开发，遵循现代前端开发最佳实践。

---

## 📚 相关文档

- [API接口文档](./API_REFERENCE.md)
- [组件使用指南](./COMPONENT_GUIDE.md)
- [部署详细说明](./DEPLOYMENT.md)

**📞 技术支持**: 如有问题请查看代码注释或联系开发团队
