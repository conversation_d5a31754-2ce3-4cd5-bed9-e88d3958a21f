import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login as loginApi, getUserMenu, type LoginParams, type PermissionBo } from '../api/auth'
import { message } from 'ant-design-vue'

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<any>(null)
  const menuList = ref<PermissionBo[]>([])
  const isLoggedIn = ref<boolean>(!!token.value)

  // 登录
  const login = async (params: LoginParams) => {
    try {
      console.log('尝试使用API登录:', params)
      console.log('当前环境:', import.meta.env.DEV ? '开发环境(使用代理)' : '生产环境')

      // 调用真实API
      const response = await loginApi(params)
      console.log('登录响应:', response)

      if (response && ((response as any).code === '200' || (response as any).code === 200 || response.data?.code === '200' || response.data?.code === 200)) {
        // 登录成功
        const data = response.data
        token.value = data?.token || 'api-token-' + Date.now()
        userInfo.value = data || { name: params.account, account: params.account }
        isLoggedIn.value = true
        localStorage.setItem('token', token.value)
        message.success('登录成功')
        return true
      } else {
        // 登录失败 - 显示服务器返回的具体错误信息
        const errorMsg = (response as any)?.message || (response as any)?.msg || response?.data?.message || response?.data?.msg || '登录失败'
        console.log('登录失败，服务器返回:', response)
        message.error(`登录失败: ${errorMsg}`)
        return false
      }
    } catch (error: any) {
      console.error('API登录失败:', error)
      console.error('错误详情:', {
        message: error.message,
        code: error.code,
        response: error.response?.data,
        status: error.response?.status
      })

      // 检查是否是服务器错误（500等）
      if (error.response?.status >= 500) {
        const serverError = error.response.data
        const errorMsg = serverError?.message || serverError?.msg || '服务器内部错误'
        message.error(`服务器错误: ${errorMsg}`)
        return false
      }

      // 检查各种网络错误情况
      const isNetworkError =
        error.code === 'ERR_NETWORK' ||
        error.message?.includes('Network Error') ||
        error.message?.includes('未能解析此远程名称') ||
        error.message?.includes('getaddrinfo ENOTFOUND') ||
        error.code === 'ENOTFOUND' ||
        error.response?.status === 0

      if (isNetworkError) {
        console.log('检测到网络错误，启用演示模式')
        message.warning('无法连接到API服务器，启用演示模式')

        // 演示模式登录
        if (params.account === 'admin' && params.pwd === '123456') {
          token.value = 'demo-token-' + Date.now()
          userInfo.value = { name: 'Admin (演示模式)', account: 'admin' }
          isLoggedIn.value = true
          localStorage.setItem('token', token.value)
          message.success('登录成功 (演示模式)')
          return true
        } else {
          message.error('演示模式下，请使用账号: admin, 密码: 123456')
          return false
        }
      } else {
        // 其他错误（如401认证失败等）
        const errorMsg = error.response?.data?.message || error.response?.data?.msg || error.message || '登录失败'
        message.error(`登录失败: ${errorMsg}`)
        return false
      }
    }
  }

  // 获取菜单
  const getMenu = async () => {
    try {
      const response = await getUserMenu()
      if (response.data) {
        menuList.value = response.data
      }
    } catch (error) {
      console.error('获取菜单失败:', error)
    }
  }

  // 登出
  const logout = () => {
    token.value = ''
    userInfo.value = null
    menuList.value = []
    isLoggedIn.value = false
    localStorage.removeItem('token')
    window.location.href = '#/login'
  }

  return {
    token,
    userInfo,
    menuList,
    isLoggedIn,
    login,
    getMenu,
    logout
  }
})
