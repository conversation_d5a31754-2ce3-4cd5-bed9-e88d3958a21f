ccc

**接口地址** `/pasture/login/ccc`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------分割线
授权码登录

**接口地址** `/pasture/login/codeLogin`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| loginParameters         |      loginParameters   |     body        |       true      | 授权码登录参数   | 授权码登录参数     |
            



**schema属性说明**
  
**授权码登录参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| authCode  | 授权码 |   body    |   true   |string  |       |
| authPlayerId  | 授权用户id |   body    |   true   |int64  |       |
| playerId  | 用户id |   body    |   true   |int64  |       |
| version  | 版本号 |   body    |   true   |string  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«玩家»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    玩家   |   玩家    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**玩家**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| accumulatePc         |     accumulatePc累计产出金币      |  int32   |      |
            | animal         |     animal      |  com.qihui.pasture.model.ConfAnimal   | com.qihui.pasture.model.ConfAnimal     |
            | appCode         |     appCode账户体系生成的ID      |  string   |      |
            | birthday         |     birthday生日      |  date-time   |      |
            | coin         |     coin金币      |  int32   |      |
            | consumePc         |     consumePc累计消耗金币      |  int32   |      |
            | createTime         |     createTime创建时间      |  date-time   |      |
            | energy         |     energy能量币      |  int32   |      |
            | equipages         |     装备      |  array   | 装备     |
            | expValue         |     expValue当前经验值      |  int64   |      |
            | icon         |     icon玩家头像图标地址      |  string   |      |
            | id         |     idID      |  int64   |      |
            | lastVimTime         |     lastVimTime最后更新活力时间      |  date-time   |      |
            | level         |     level等级      |  int32   |      |
            | likeNum         |     likeNum点赞数      |  int32   |      |
            | nextExpValue         |     nextExpValue下一个等级所需经验值      |  int64   |      |
            | nickname         |     nickname玩家昵称      |  string   |      |
            | ore         |     ore矿石      |  int32   |      |
            | plotId         |     plotId最后完成剧情id      |  int64   |      |
            | pwd         |     pwd      |  string   |      |
            | rankMonth         |     rankMonth排行榜月份（月周期使用）      |  string   |      |
            | rankScore         |     rankScore排位积分      |  int32   |      |
            | roleType         |     roleType角色模型 0男角色 1女角色      |  int32   |      |
            | salt         |     salt      |  string   |      |
            | sex         |     sex玩家性别 1-男 2-女      |  int32   |      |
            | status         |     status玩家状态 0-不可用 1-正常       |  int32   |      |
            | token         |     token      |  string   |      |
            | updateTime         |     updateTime更新时间      |  date-time   |      |
            | userId         |     userId      |  int64   |      |
            | vim         |     vim活力      |  int32   |      |
            | vimLimit         |     vimLimit活力上限      |  int32   |      |
            | vipEndTime         |     vipEndTimevip结束时间      |  date-time   |      |
            | vipOre         |     vipOrevip矿石      |  int32   |      |
            | vipStatus         |     vip状态      |  int32   |      |
            | vipType         |     vipType1-月会员 2-季会员 3-年会员      |  int32   |      |
            | wish         |     wish许愿币      |  int32   |      |
            

**com.qihui.pasture.model.ConfAnimal**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coinType         |     coinType养殖所需要的代币类型：0-不需要 1-金币，2-能量      |  int32   |      |
            | coinValue         |     coinValue种植所需要的代币值      |  int32   |      |
            | createTime         |     createTime      |  date-time   |      |
            | dropMemo         |     dropMemo掉落备注      |  string   |      |
            | gainCoinType         |     gainCoinType收获代币类型：0-不需要 1-金币，2-能量      |  int32   |      |
            | gainCoinVal         |     gainCoinType收获代币值      |  int32   |      |
            | gainExp         |     gainExp收获经验值      |  int32   |      |
            | icon         |     icon      |  string   |      |
            | id         |     id      |  int64   |      |
            | link         |     link获取链接1：      |  string   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     modelSpine模型动画      |  string   |      |
            | name         |     name      |  string   |      |
            | packId         |     packId碎片id      |  int64   |      |
            | payType         |     payType类型 1金币 2vip 3svip      |  int32   |      |
            | pickMaxValue         |     pickMaxValue采摘最大值      |  int32   |      |
            | pickMinValue         |     pickMinValue采摘最小值      |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | seq         |     seq排序      |  int32   |      |
            | subPackId         |     幼崽      |  int64   |      |
            | title         |     title标题      |  string   |      |
            | unlockLevel         |     unlockLevel解锁等级      |  int32   |      |
            | unlockPrice         |     unlockPrice价格      |  int32   |      |
            | unlockType         |     unlockType 类型 1-等级 2-价格 3-vip 4-关注公众号      |  int32   |      |
            | updateTime         |     updateTime      |  date-time   |      |
            | version         |     version版本号      |  string   |      |
            

**装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | charm         |     魅力值      |  int32   |      |
            | differenceAttrs         |     属性值差值      |  array   | 属性     |
            | differenceScore         |     评分差值      |  int32   |      |
            | effect         |     作用      |  string   |      |
            | icon         |     icon      |  string   |      |
            | id         |     id      |  int64   |      |
            | level         |     等级      |  int32   |      |
            | link         |     link获取链接1副本界面 2会员界面 3扭蛋界面 4签到界面 5智慧树 6购买获取  再根据pay_type类型展示 7升级界面 8宠物界面9学习10装备11段位赛12巡游13武道会      |  string   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | nextAttrs         |     下一级属性值      |  array   | 属性     |
            | nextLevel         |     下一等级  当等级=-1时， 无下一级      |  int32   |      |
            | packs         |     下一级升级所需      |  array   | 背包道具     |
            | payType         |     payType类型 1金币 2vip 3svip      |  int32   |      |
            | price         |     price价格      |  int32   |      |
            | redStatus         |     红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态 0未用于  1已拥有 2已装备      |  int32   |      |
            | title         |     title标题      |  string   |      |
            | type         |     类型 1天空 2草地 3房子 4装饰1 5装饰2      |  int32   |      |
            | unlockLevel         |     unlockLevel解锁等级      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**背包道具**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | bizType         |     类型 1宠物材料 2宠物蛋 3宠物道具 4人物升级材料 5互动道具 6外观碎片 7抽奖道具      |  int32   |      |
            | effect         |     effect作用      |  string   |      |
            | hatchStatus         |     状态 1可以孵化 2不可以      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | link         |     link获取链接      |  string   |      |
            | memo         |     memo备注      |  string   |      |
            | name         |     name名称      |  string   |      |
            | num         |     拥有个数      |  int32   |      |
            | packId         |     packId      |  int64   |      |
            | playerPackId         |     playerPackId      |  int64   |      |
            | price         |     price价值      |  int32   |      |
            | redStatus         |     红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | sellLevel         |     售卖等级 （小于玩家等级 可以售卖）      |  int32   |      |
            | source         |     source获取来源      |  string   |      |
            | status         |     status状态 0未用于  1已拥有 2已装备      |  int32   |      |
            | type         |     type类型 1宠物蛋 2材料      |  int32   |      |
            | useNum         |     使用消耗个数      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "accumulatePc": 0,
        "animal": {
            "coinType": 0,
            "coinValue": 0,
            "createTime": "",
            "dropMemo": "",
            "gainCoinType": 0,
            "gainCoinVal": 0,
            "gainExp": 0,
            "icon": "",
            "id": 0,
            "link": "",
            "model": "",
            "modelSpine": "",
            "name": "",
            "packId": 0,
            "payType": 0,
            "pickMaxValue": 0,
            "pickMinValue": 0,
            "score": 0,
            "seq": 0,
            "subPackId": 0,
            "title": "",
            "unlockLevel": 0,
            "unlockPrice": 0,
            "unlockType": 0,
            "updateTime": "",
            "version": ""
        },
        "appCode": "",
        "birthday": "",
        "coin": 0,
        "consumePc": 0,
        "createTime": "",
        "energy": 0,
        "equipages": [
            {
                "attrs": [
                    {
                        "attrType": 0,
                        "attrValue": 0,
                        "type": 0
                    }
                ],
                "charm": 0,
                "differenceAttrs": [
                    {
                        "attrType": 0,
                        "attrValue": 0,
                        "type": 0
                    }
                ],
                "differenceScore": 0,
                "effect": "",
                "icon": "",
                "id": 0,
                "level": 0,
                "link": "",
                "model": "",
                "modelSpine": "",
                "name": "",
                "nextAttrs": [
                    {
                        "attrType": 0,
                        "attrValue": 0,
                        "type": 0
                    }
                ],
                "nextLevel": 0,
                "packs": [
                    {
                        "attrs": [
                            {
                                "attrType": 0,
                                "attrValue": 0,
                                "type": 0
                            }
                        ],
                        "bizType": 0,
                        "effect": "",
                        "hatchStatus": 0,
                        "icon": "",
                        "link": "",
                        "memo": "",
                        "name": "",
                        "num": 0,
                        "packId": 0,
                        "playerPackId": 0,
                        "price": 0,
                        "redStatus": 0,
                        "score": 0,
                        "sellLevel": 0,
                        "source": "",
                        "status": 0,
                        "type": 0,
                        "useNum": 0
                    }
                ],
                "payType": 0,
                "price": 0,
                "redStatus": 0,
                "score": 0,
                "seq": 0,
                "status": 0,
                "title": "",
                "type": 0,
                "unlockLevel": 0
            }
        ],
        "expValue": 0,
        "icon": "",
        "id": 0,
        "lastVimTime": "",
        "level": 0,
        "likeNum": 0,
        "nextExpValue": 0,
        "nickname": "",
        "ore": 0,
        "plotId": 0,
        "pwd": "",
        "rankMonth": "",
        "rankScore": 0,
        "roleType": 0,
        "salt": "",
        "sex": 0,
        "status": 0,
        "token": "",
        "updateTime": "",
        "userId": 0,
        "vim": 0,
        "vimLimit": 0,
        "vipEndTime": "",
        "vipOre": 0,
        "vipStatus": 0,
        "vipType": 0,
        "wish": 0
    },
    "message": "",
    "msg": ""
}
```



----------分割线
隐私协议

**接口地址** `/pasture/login/gameHtml`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------分割线
隐私协议

**接口地址** `/pasture/login/gameHtml2`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------分割线
获取验证码

**接口地址** `/pasture/login/getCode`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«授权码信息»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    授权码信息   |   授权码信息    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**授权码信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| authCode         |     authCode      |  string   |      |
            | playerId         |     玩家id      |  int64   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "authCode": "",
        "playerId": 0
    },
    "message": "",
    "msg": ""
}
```



----------分割线
登录

**接口地址** `/pasture/login/login`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| loginParameters         |      loginParameters   |     body        |       true      | 登录参数   | 登录参数     |
            



**schema属性说明**
  
**登录参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| account  | 登录账号 |   body    |   true   |string  |       |
| outAccount  | 第三方账号 |   body    |   true   |string  |       |
| pwd  | 登录密码 |   body    |   true   |string  |       |
| version  | 版本号 |   body    |   true   |string  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«玩家»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    玩家   |   玩家    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**玩家**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| accumulatePc         |     accumulatePc累计产出金币      |  int32   |      |
            | animal         |     animal      |  com.qihui.pasture.model.ConfAnimal   | com.qihui.pasture.model.ConfAnimal     |
            | appCode         |     appCode账户体系生成的ID      |  string   |      |
            | birthday         |     birthday生日      |  date-time   |      |
            | coin         |     coin金币      |  int32   |      |
            | consumePc         |     consumePc累计消耗金币      |  int32   |      |
            | createTime         |     createTime创建时间      |  date-time   |      |
            | energy         |     energy能量币      |  int32   |      |
            | equipages         |     装备      |  array   | 装备     |
            | expValue         |     expValue当前经验值      |  int64   |      |
            | icon         |     icon玩家头像图标地址      |  string   |      |
            | id         |     idID      |  int64   |      |
            | lastVimTime         |     lastVimTime最后更新活力时间      |  date-time   |      |
            | level         |     level等级      |  int32   |      |
            | likeNum         |     likeNum点赞数      |  int32   |      |
            | nextExpValue         |     nextExpValue下一个等级所需经验值      |  int64   |      |
            | nickname         |     nickname玩家昵称      |  string   |      |
            | ore         |     ore矿石      |  int32   |      |
            | plotId         |     plotId最后完成剧情id      |  int64   |      |
            | pwd         |     pwd      |  string   |      |
            | rankMonth         |     rankMonth排行榜月份（月周期使用）      |  string   |      |
            | rankScore         |     rankScore排位积分      |  int32   |      |
            | roleType         |     roleType角色模型 0男角色 1女角色      |  int32   |      |
            | salt         |     salt      |  string   |      |
            | sex         |     sex玩家性别 1-男 2-女      |  int32   |      |
            | status         |     status玩家状态 0-不可用 1-正常       |  int32   |      |
            | token         |     token      |  string   |      |
            | updateTime         |     updateTime更新时间      |  date-time   |      |
            | userId         |     userId      |  int64   |      |
            | vim         |     vim活力      |  int32   |      |
            | vimLimit         |     vimLimit活力上限      |  int32   |      |
            | vipEndTime         |     vipEndTimevip结束时间      |  date-time   |      |
            | vipOre         |     vipOrevip矿石      |  int32   |      |
            | vipStatus         |     vip状态      |  int32   |      |
            | vipType         |     vipType1-月会员 2-季会员 3-年会员      |  int32   |      |
            | wish         |     wish许愿币      |  int32   |      |
            

**com.qihui.pasture.model.ConfAnimal**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coinType         |     coinType养殖所需要的代币类型：0-不需要 1-金币，2-能量      |  int32   |      |
            | coinValue         |     coinValue种植所需要的代币值      |  int32   |      |
            | createTime         |     createTime      |  date-time   |      |
            | dropMemo         |     dropMemo掉落备注      |  string   |      |
            | gainCoinType         |     gainCoinType收获代币类型：0-不需要 1-金币，2-能量      |  int32   |      |
            | gainCoinVal         |     gainCoinType收获代币值      |  int32   |      |
            | gainExp         |     gainExp收获经验值      |  int32   |      |
            | icon         |     icon      |  string   |      |
            | id         |     id      |  int64   |      |
            | link         |     link获取链接1：      |  string   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     modelSpine模型动画      |  string   |      |
            | name         |     name      |  string   |      |
            | packId         |     packId碎片id      |  int64   |      |
            | payType         |     payType类型 1金币 2vip 3svip      |  int32   |      |
            | pickMaxValue         |     pickMaxValue采摘最大值      |  int32   |      |
            | pickMinValue         |     pickMinValue采摘最小值      |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | seq         |     seq排序      |  int32   |      |
            | subPackId         |     幼崽      |  int64   |      |
            | title         |     title标题      |  string   |      |
            | unlockLevel         |     unlockLevel解锁等级      |  int32   |      |
            | unlockPrice         |     unlockPrice价格      |  int32   |      |
            | unlockType         |     unlockType 类型 1-等级 2-价格 3-vip 4-关注公众号      |  int32   |      |
            | updateTime         |     updateTime      |  date-time   |      |
            | version         |     version版本号      |  string   |      |
            

**装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | charm         |     魅力值      |  int32   |      |
            | differenceAttrs         |     属性值差值      |  array   | 属性     |
            | differenceScore         |     评分差值      |  int32   |      |
            | effect         |     作用      |  string   |      |
            | icon         |     icon      |  string   |      |
            | id         |     id      |  int64   |      |
            | level         |     等级      |  int32   |      |
            | link         |     link获取链接1副本界面 2会员界面 3扭蛋界面 4签到界面 5智慧树 6购买获取  再根据pay_type类型展示 7升级界面 8宠物界面9学习10装备11段位赛12巡游13武道会      |  string   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | nextAttrs         |     下一级属性值      |  array   | 属性     |
            | nextLevel         |     下一等级  当等级=-1时， 无下一级      |  int32   |      |
            | packs         |     下一级升级所需      |  array   | 背包道具     |
            | payType         |     payType类型 1金币 2vip 3svip      |  int32   |      |
            | price         |     price价格      |  int32   |      |
            | redStatus         |     红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态 0未用于  1已拥有 2已装备      |  int32   |      |
            | title         |     title标题      |  string   |      |
            | type         |     类型 1天空 2草地 3房子 4装饰1 5装饰2      |  int32   |      |
            | unlockLevel         |     unlockLevel解锁等级      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**背包道具**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | bizType         |     类型 1宠物材料 2宠物蛋 3宠物道具 4人物升级材料 5互动道具 6外观碎片 7抽奖道具      |  int32   |      |
            | effect         |     effect作用      |  string   |      |
            | hatchStatus         |     状态 1可以孵化 2不可以      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | link         |     link获取链接      |  string   |      |
            | memo         |     memo备注      |  string   |      |
            | name         |     name名称      |  string   |      |
            | num         |     拥有个数      |  int32   |      |
            | packId         |     packId      |  int64   |      |
            | playerPackId         |     playerPackId      |  int64   |      |
            | price         |     price价值      |  int32   |      |
            | redStatus         |     红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | sellLevel         |     售卖等级 （小于玩家等级 可以售卖）      |  int32   |      |
            | source         |     source获取来源      |  string   |      |
            | status         |     status状态 0未用于  1已拥有 2已装备      |  int32   |      |
            | type         |     type类型 1宠物蛋 2材料      |  int32   |      |
            | useNum         |     使用消耗个数      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "accumulatePc": 0,
        "animal": {
            "coinType": 0,
            "coinValue": 0,
            "createTime": "",
            "dropMemo": "",
            "gainCoinType": 0,
            "gainCoinVal": 0,
            "gainExp": 0,
            "icon": "",
            "id": 0,
            "link": "",
            "model": "",
            "modelSpine": "",
            "name": "",
            "packId": 0,
            "payType": 0,
            "pickMaxValue": 0,
            "pickMinValue": 0,
            "score": 0,
            "seq": 0,
            "subPackId": 0,
            "title": "",
            "unlockLevel": 0,
            "unlockPrice": 0,
            "unlockType": 0,
            "updateTime": "",
            "version": ""
        },
        "appCode": "",
        "birthday": "",
        "coin": 0,
        "consumePc": 0,
        "createTime": "",
        "energy": 0,
        "equipages": [
            {
                "attrs": [
                    {
                        "attrType": 0,
                        "attrValue": 0,
                        "type": 0
                    }
                ],
                "charm": 0,
                "differenceAttrs": [
                    {
                        "attrType": 0,
                        "attrValue": 0,
                        "type": 0
                    }
                ],
                "differenceScore": 0,
                "effect": "",
                "icon": "",
                "id": 0,
                "level": 0,
                "link": "",
                "model": "",
                "modelSpine": "",
                "name": "",
                "nextAttrs": [
                    {
                        "attrType": 0,
                        "attrValue": 0,
                        "type": 0
                    }
                ],
                "nextLevel": 0,
                "packs": [
                    {
                        "attrs": [
                            {
                                "attrType": 0,
                                "attrValue": 0,
                                "type": 0
                            }
                        ],
                        "bizType": 0,
                        "effect": "",
                        "hatchStatus": 0,
                        "icon": "",
                        "link": "",
                        "memo": "",
                        "name": "",
                        "num": 0,
                        "packId": 0,
                        "playerPackId": 0,
                        "price": 0,
                        "redStatus": 0,
                        "score": 0,
                        "sellLevel": 0,
                        "source": "",
                        "status": 0,
                        "type": 0,
                        "useNum": 0
                    }
                ],
                "payType": 0,
                "price": 0,
                "redStatus": 0,
                "score": 0,
                "seq": 0,
                "status": 0,
                "title": "",
                "type": 0,
                "unlockLevel": 0
            }
        ],
        "expValue": 0,
        "icon": "",
        "id": 0,
        "lastVimTime": "",
        "level": 0,
        "likeNum": 0,
        "nextExpValue": 0,
        "nickname": "",
        "ore": 0,
        "plotId": 0,
        "pwd": "",
        "rankMonth": "",
        "rankScore": 0,
        "roleType": 0,
        "salt": "",
        "sex": 0,
        "status": 0,
        "token": "",
        "updateTime": "",
        "userId": 0,
        "vim": 0,
        "vimLimit": 0,
        "vipEndTime": "",
        "vipOre": 0,
        "vipStatus": 0,
        "vipType": 0,
        "wish": 0
    },
    "message": "",
    "msg": ""
}
```



----------分割线
test

**接口地址** `/pasture/login/test`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------分割线
用户协议

**接口地址** `/pasture/login/userHtml`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------分割线
用户协议

**接口地址** `/pasture/login/userHtml2`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```


