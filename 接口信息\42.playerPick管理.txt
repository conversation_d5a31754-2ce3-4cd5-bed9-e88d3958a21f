playerPick新增

**接口地址** `/pasture/player/pick/add`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerPick         |      playerPick   |     body        |       true      | com.qihui.pasture.model.PlayerPick   | com.qihui.pasture.model.PlayerPick     |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerPick**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| bizType  | bizType业务类型 1普通采摘 |   body    |   false   |int32  |       |
| breedId  | 养殖id |   body    |   false   |int64  |       |
| createTime  | createTime |   body    |   false   |date-time  |       |
| friendId  | friendId好友id |   body    |   false   |int64  |       |
| id  | id |   body    |   false   |int64  |       |
| num  | num收获 |   body    |   false   |int32  |       |
| playerId  | playerId |   body    |   false   |int64  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



-------------分割线
playerPick删除

**接口地址** `/pasture/player/pick/delete`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



-------------分割线
playerPick获取详情

**接口地址** `/pasture/player/pick/detail`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«com.qihui.pasture.model.PlayerPick»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    com.qihui.pasture.model.PlayerPick   |   com.qihui.pasture.model.PlayerPick    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerPick**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| bizType         |     bizType业务类型 1普通采摘      |  int32   |      |
            | breedId         |     养殖id      |  int64   |      |
            | createTime         |     createTime      |  date-time   |      |
            | friendId         |     friendId好友id      |  int64   |      |
            | id         |     id      |  int64   |      |
            | num         |     num收获      |  int32   |      |
            | playerId         |     playerId      |  int64   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "bizType": 0,
        "breedId": 0,
        "createTime": "",
        "friendId": 0,
        "id": 0,
        "num": 0,
        "playerId": 0
    },
    "message": "",
    "msg": ""
}
```



-------------分割线
playerPick获取列表

**接口地址** `/pasture/player/pick/list`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| page         |      page   |     query        |       false      | integer   |      |
            | playerPick         |      playerPick   |     body        |       true      | com.qihui.pasture.model.PlayerPick   | com.qihui.pasture.model.PlayerPick     |
            | size         |      size   |     query        |       false      | integer   |      |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerPick**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| bizType  | bizType业务类型 1普通采摘 |   body    |   false   |int32  |       |
| breedId  | 养殖id |   body    |   false   |int64  |       |
| createTime  | createTime |   body    |   false   |date-time  |       |
| friendId  | friendId好友id |   body    |   false   |int64  |       |
| id  | id |   body    |   false   |int64  |       |
| num  | num收获 |   body    |   false   |int32  |       |
| playerId  | playerId |   body    |   false   |int64  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«com.qihui.pasture.model.PlayerPick»»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   com.qihui.pasture.model.PlayerPick    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerPick**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| bizType         |     bizType业务类型 1普通采摘      |  int32   |      |
            | breedId         |     养殖id      |  int64   |      |
            | createTime         |     createTime      |  date-time   |      |
            | friendId         |     friendId好友id      |  int64   |      |
            | id         |     id      |  int64   |      |
            | num         |     num收获      |  int32   |      |
            | playerId         |     playerId      |  int64   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "bizType": 0,
            "breedId": 0,
            "createTime": "",
            "friendId": 0,
            "id": 0,
            "num": 0,
            "playerId": 0
        }
    ],
    "message": "",
    "msg": ""
}
```



-------------分割线
playerPick更新

**接口地址** `/pasture/player/pick/update`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerPick         |      playerPick   |     body        |       true      | com.qihui.pasture.model.PlayerPick   | com.qihui.pasture.model.PlayerPick     |
            



**schema属性说明**
  
**com.qihui.pasture.model.PlayerPick**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| bizType  | bizType业务类型 1普通采摘 |   body    |   false   |int32  |       |
| breedId  | 养殖id |   body    |   false   |int64  |       |
| createTime  | createTime |   body    |   false   |date-time  |       |
| friendId  | friendId好友id |   body    |   false   |int64  |       |
| id  | id |   body    |   false   |int64  |       |
| num  | num收获 |   body    |   false   |int32  |       |
| playerId  | playerId |   body    |   false   |int64  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```


