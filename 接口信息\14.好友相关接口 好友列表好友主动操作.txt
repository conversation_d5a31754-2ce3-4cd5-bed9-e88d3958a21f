指定新增好友

**接口地址** `/pasture/app/friend/add`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            | userId         |      userId   |     query        |       true      | string   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------分割线
删除好友

**接口地址** `/pasture/app/friend/delete`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| friendPlayerId         |      friendPlayerId   |     query        |       true      | integer   |      |
            | playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------分割线
获取好友详细信息

**接口地址** `/pasture/app/friend/detail`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| friendPlayerId         |      friendPlayerId   |     query        |       true      | integer   |      |
            | playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«玩家详情»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    玩家详情   |   玩家详情    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**玩家详情**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | breed         |     动物养殖      |  动物养殖   | 动物养殖     |
            | coin         |     金币      |  int32   |      |
            | energy         |     能量      |  int32   |      |
            | equipageScore         |     装备评分      |  int32   |      |
            | equipages         |     装备      |  array   | 玩家装备     |
            | events         |     互动事件      |  array   | 互动事件     |
            | expValue         |     expValue当前经验值      |  int64   |      |
            | followWx         |     是否关注微信公众号 1-已关注 0-未关注      |  int32   |      |
            | icon         |     玩家头像图标地址      |  string   |      |
            | interacts         |     互动      |  array   | 互动事件     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | level         |     人物等级      |  int32   |      |
            | levelName         |     官职      |  string   |      |
            | model         |     角色模型      |  string   |      |
            | nextExpValue         |     nextExpValue下一个等级所需经验值      |  int64   |      |
            | nickname         |     玩家昵称      |  string   |      |
            | ore         |     粮草      |  int32   |      |
            | playerId         |     玩家id      |  int64   |      |
            | protectStatus         |     保护状态 1有保护 2无保护      |  int32   |      |
            | rankScore         |     排位积分      |  int32   |      |
            | score         |     总评分      |  int32   |      |
            | sex         |     玩家性别 1-男 2-女      |  int32   |      |
            | status         |     玩家状态 0-不可用 1-正常      |  int32   |      |
            | userId         |     用户id      |  string   |      |
            | vim         |     活力      |  int32   |      |
            | vimLimit         |     活力上限      |  int32   |      |
            | vipOre         |     vip粮草      |  int32   |      |
            | vipStatus         |     vip状态      |  int32   |      |
            | wish         |     许愿值      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**动物养殖**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| animalId         |     animalId动物id      |  int64   |      |
            | breedTime         |     breedTime养殖时间      |  date-time   |      |
            | createTime         |     createTime      |  date-time   |      |
            | downTime         |     倒计时      |  int64   |      |
            | modelSpine         |     动效      |  string   |      |
            | petId         |     petId宠物id      |  int64   |      |
            | pickStatus         |     是否可以采摘 1可以 2自己不能偷 3好友不能偷      |  int32   |      |
            | playerBreedId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态1 成长中 2待收获 3已收获      |  int32   |      |
            | suspendTime         |     暂停时间 不为空就是被暂停      |  date-time   |      |
            

**玩家装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| id         |     id      |  int64   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | type         |     类型 1神兵 2宝马 3皮肤 4兵书 5虎符      |  int32   |      |
            

**互动事件**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coin         |     金币      |  int32   |      |
            | createTime         |     createTime      |  date-time   |      |
            | detail         |     详情      |  string   |      |
            | eventType         |     操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽  8便便 9蚊子 10老鼠      |  int32   |      |
            | eventVal         |     eventVal事件值      |  int32   |      |
            | finishVal         |     eventVal事件完成值      |  int32   |      |
            | icon         |     图片      |  string   |      |
            | playerEventId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "attrs": [
            {
                "attrType": 0,
                "attrValue": 0,
                "type": 0
            }
        ],
        "breed": {
            "animalId": 0,
            "breedTime": "",
            "createTime": "",
            "downTime": 0,
            "modelSpine": "",
            "petId": 0,
            "pickStatus": 0,
            "playerBreedId": 0,
            "playerId": 0,
            "seq": 0,
            "status": 0,
            "suspendTime": ""
        },
        "coin": 0,
        "energy": 0,
        "equipageScore": 0,
        "equipages": [
            {
                "id": 0,
                "model": "",
                "modelSpine": "",
                "name": "",
                "type": 0
            }
        ],
        "events": [
            {
                "coin": 0,
                "createTime": "",
                "detail": "",
                "eventType": 0,
                "eventVal": 0,
                "finishVal": 0,
                "icon": "",
                "playerEventId": 0,
                "playerId": 0
            }
        ],
        "expValue": 0,
        "followWx": 0,
        "icon": "",
        "interacts": [
            {
                "coin": 0,
                "createTime": "",
                "detail": "",
                "eventType": 0,
                "eventVal": 0,
                "finishVal": 0,
                "icon": "",
                "playerEventId": 0,
                "playerId": 0
            }
        ],
        "isVip": 0,
        "level": 0,
        "levelName": "",
        "model": "",
        "nextExpValue": 0,
        "nickname": "",
        "ore": 0,
        "playerId": 0,
        "protectStatus": 0,
        "rankScore": 0,
        "score": 0,
        "sex": 0,
        "status": 0,
        "userId": "",
        "vim": 0,
        "vimLimit": 0,
        "vipOre": 0,
        "vipStatus": 0,
        "wish": 0
    },
    "message": "",
    "msg": ""
}
```



--------分割线
好友探索

**接口地址** `/pasture/app/friend/explore`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«好友探索结果»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    好友探索结果   |   好友探索结果    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**好友探索结果**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| boxId         |     宝箱ID,该值为空时代表没有出现宝箱      |  string   |      |
            | friendPlayerInfo         |     好友信息      |  玩家详情   | 玩家详情     |
            

**玩家详情**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | breed         |     动物养殖      |  动物养殖   | 动物养殖     |
            | coin         |     金币      |  int32   |      |
            | energy         |     能量      |  int32   |      |
            | equipageScore         |     装备评分      |  int32   |      |
            | equipages         |     装备      |  array   | 玩家装备     |
            | events         |     互动事件      |  array   | 互动事件     |
            | expValue         |     expValue当前经验值      |  int64   |      |
            | followWx         |     是否关注微信公众号 1-已关注 0-未关注      |  int32   |      |
            | icon         |     玩家头像图标地址      |  string   |      |
            | interacts         |     互动      |  array   | 互动事件     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | level         |     人物等级      |  int32   |      |
            | levelName         |     官职      |  string   |      |
            | model         |     角色模型      |  string   |      |
            | nextExpValue         |     nextExpValue下一个等级所需经验值      |  int64   |      |
            | nickname         |     玩家昵称      |  string   |      |
            | ore         |     粮草      |  int32   |      |
            | playerId         |     玩家id      |  int64   |      |
            | protectStatus         |     保护状态 1有保护 2无保护      |  int32   |      |
            | rankScore         |     排位积分      |  int32   |      |
            | score         |     总评分      |  int32   |      |
            | sex         |     玩家性别 1-男 2-女      |  int32   |      |
            | status         |     玩家状态 0-不可用 1-正常      |  int32   |      |
            | userId         |     用户id      |  string   |      |
            | vim         |     活力      |  int32   |      |
            | vimLimit         |     活力上限      |  int32   |      |
            | vipOre         |     vip粮草      |  int32   |      |
            | vipStatus         |     vip状态      |  int32   |      |
            | wish         |     许愿值      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**动物养殖**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| animalId         |     animalId动物id      |  int64   |      |
            | breedTime         |     breedTime养殖时间      |  date-time   |      |
            | createTime         |     createTime      |  date-time   |      |
            | downTime         |     倒计时      |  int64   |      |
            | modelSpine         |     动效      |  string   |      |
            | petId         |     petId宠物id      |  int64   |      |
            | pickStatus         |     是否可以采摘 1可以 2自己不能偷 3好友不能偷      |  int32   |      |
            | playerBreedId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态1 成长中 2待收获 3已收获      |  int32   |      |
            | suspendTime         |     暂停时间 不为空就是被暂停      |  date-time   |      |
            

**玩家装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| id         |     id      |  int64   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | type         |     类型 1神兵 2宝马 3皮肤 4兵书 5虎符      |  int32   |      |
            

**互动事件**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coin         |     金币      |  int32   |      |
            | createTime         |     createTime      |  date-time   |      |
            | detail         |     详情      |  string   |      |
            | eventType         |     操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽  8便便 9蚊子 10老鼠      |  int32   |      |
            | eventVal         |     eventVal事件值      |  int32   |      |
            | finishVal         |     eventVal事件完成值      |  int32   |      |
            | icon         |     图片      |  string   |      |
            | playerEventId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "boxId": "",
        "friendPlayerInfo": {
            "attrs": [
                {
                    "attrType": 0,
                    "attrValue": 0,
                    "type": 0
                }
            ],
            "breed": {
                "animalId": 0,
                "breedTime": "",
                "createTime": "",
                "downTime": 0,
                "modelSpine": "",
                "petId": 0,
                "pickStatus": 0,
                "playerBreedId": 0,
                "playerId": 0,
                "seq": 0,
                "status": 0,
                "suspendTime": ""
            },
            "coin": 0,
            "energy": 0,
            "equipageScore": 0,
            "equipages": [
                {
                    "id": 0,
                    "model": "",
                    "modelSpine": "",
                    "name": "",
                    "type": 0
                }
            ],
            "events": [
                {
                    "coin": 0,
                    "createTime": "",
                    "detail": "",
                    "eventType": 0,
                    "eventVal": 0,
                    "finishVal": 0,
                    "icon": "",
                    "playerEventId": 0,
                    "playerId": 0
                }
            ],
            "expValue": 0,
            "followWx": 0,
            "icon": "",
            "interacts": [
                {
                    "coin": 0,
                    "createTime": "",
                    "detail": "",
                    "eventType": 0,
                    "eventVal": 0,
                    "finishVal": 0,
                    "icon": "",
                    "playerEventId": 0,
                    "playerId": 0
                }
            ],
            "isVip": 0,
            "level": 0,
            "levelName": "",
            "model": "",
            "nextExpValue": 0,
            "nickname": "",
            "ore": 0,
            "playerId": 0,
            "protectStatus": 0,
            "rankScore": 0,
            "score": 0,
            "sex": 0,
            "status": 0,
            "userId": "",
            "vim": 0,
            "vimLimit": 0,
            "vipOre": 0,
            "vipStatus": 0,
            "wish": 0
        }
    },
    "message": "",
    "msg": ""
}
```



--------分割线
进入好友探索页面

**接口地址** `/pasture/app/friend/explore`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«好友探索结果»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    好友探索结果   |   好友探索结果    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**好友探索结果**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| boxId         |     宝箱ID,该值为空时代表没有出现宝箱      |  string   |      |
            | friendPlayerInfo         |     好友信息      |  玩家详情   | 玩家详情     |
            

**玩家详情**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | breed         |     动物养殖      |  动物养殖   | 动物养殖     |
            | coin         |     金币      |  int32   |      |
            | energy         |     能量      |  int32   |      |
            | equipageScore         |     装备评分      |  int32   |      |
            | equipages         |     装备      |  array   | 玩家装备     |
            | events         |     互动事件      |  array   | 互动事件     |
            | expValue         |     expValue当前经验值      |  int64   |      |
            | followWx         |     是否关注微信公众号 1-已关注 0-未关注      |  int32   |      |
            | icon         |     玩家头像图标地址      |  string   |      |
            | interacts         |     互动      |  array   | 互动事件     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | level         |     人物等级      |  int32   |      |
            | levelName         |     官职      |  string   |      |
            | model         |     角色模型      |  string   |      |
            | nextExpValue         |     nextExpValue下一个等级所需经验值      |  int64   |      |
            | nickname         |     玩家昵称      |  string   |      |
            | ore         |     粮草      |  int32   |      |
            | playerId         |     玩家id      |  int64   |      |
            | protectStatus         |     保护状态 1有保护 2无保护      |  int32   |      |
            | rankScore         |     排位积分      |  int32   |      |
            | score         |     总评分      |  int32   |      |
            | sex         |     玩家性别 1-男 2-女      |  int32   |      |
            | status         |     玩家状态 0-不可用 1-正常      |  int32   |      |
            | userId         |     用户id      |  string   |      |
            | vim         |     活力      |  int32   |      |
            | vimLimit         |     活力上限      |  int32   |      |
            | vipOre         |     vip粮草      |  int32   |      |
            | vipStatus         |     vip状态      |  int32   |      |
            | wish         |     许愿值      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**动物养殖**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| animalId         |     animalId动物id      |  int64   |      |
            | breedTime         |     breedTime养殖时间      |  date-time   |      |
            | createTime         |     createTime      |  date-time   |      |
            | downTime         |     倒计时      |  int64   |      |
            | modelSpine         |     动效      |  string   |      |
            | petId         |     petId宠物id      |  int64   |      |
            | pickStatus         |     是否可以采摘 1可以 2自己不能偷 3好友不能偷      |  int32   |      |
            | playerBreedId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态1 成长中 2待收获 3已收获      |  int32   |      |
            | suspendTime         |     暂停时间 不为空就是被暂停      |  date-time   |      |
            

**玩家装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| id         |     id      |  int64   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | type         |     类型 1神兵 2宝马 3皮肤 4兵书 5虎符      |  int32   |      |
            

**互动事件**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coin         |     金币      |  int32   |      |
            | createTime         |     createTime      |  date-time   |      |
            | detail         |     详情      |  string   |      |
            | eventType         |     操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽  8便便 9蚊子 10老鼠      |  int32   |      |
            | eventVal         |     eventVal事件值      |  int32   |      |
            | finishVal         |     eventVal事件完成值      |  int32   |      |
            | icon         |     图片      |  string   |      |
            | playerEventId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "boxId": "",
        "friendPlayerInfo": {
            "attrs": [
                {
                    "attrType": 0,
                    "attrValue": 0,
                    "type": 0
                }
            ],
            "breed": {
                "animalId": 0,
                "breedTime": "",
                "createTime": "",
                "downTime": 0,
                "modelSpine": "",
                "petId": 0,
                "pickStatus": 0,
                "playerBreedId": 0,
                "playerId": 0,
                "seq": 0,
                "status": 0,
                "suspendTime": ""
            },
            "coin": 0,
            "energy": 0,
            "equipageScore": 0,
            "equipages": [
                {
                    "id": 0,
                    "model": "",
                    "modelSpine": "",
                    "name": "",
                    "type": 0
                }
            ],
            "events": [
                {
                    "coin": 0,
                    "createTime": "",
                    "detail": "",
                    "eventType": 0,
                    "eventVal": 0,
                    "finishVal": 0,
                    "icon": "",
                    "playerEventId": 0,
                    "playerId": 0
                }
            ],
            "expValue": 0,
            "followWx": 0,
            "icon": "",
            "interacts": [
                {
                    "coin": 0,
                    "createTime": "",
                    "detail": "",
                    "eventType": 0,
                    "eventVal": 0,
                    "finishVal": 0,
                    "icon": "",
                    "playerEventId": 0,
                    "playerId": 0
                }
            ],
            "isVip": 0,
            "level": 0,
            "levelName": "",
            "model": "",
            "nextExpValue": 0,
            "nickname": "",
            "ore": 0,
            "playerId": 0,
            "protectStatus": 0,
            "rankScore": 0,
            "score": 0,
            "sex": 0,
            "status": 0,
            "userId": "",
            "vim": 0,
            "vimLimit": 0,
            "vipOre": 0,
            "vipStatus": 0,
            "wish": 0
        }
    },
    "message": "",
    "msg": ""
}
```



--------分割线
获取好友列表

**接口地址** `/pasture/app/friend/list`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«玩家好友列表»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    玩家好友列表   |   玩家好友列表    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**玩家好友列表**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| recommendList         |     系统推荐      |  array   | FriendVO     |
            

**FriendVO**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coinType         |     删除好友消耗的代币类型 1-能量币 2-矿石      |  int32   |      |
            | coinValue         |     代币数值      |  int32   |      |
            | delete         |     好友能否删除 true-可以删除 false-不能删除      |  boolean   |      |
            | friendPlayerId         |     好友玩家ID      |  int64   |      |
            | icon         |     好友头像      |  string   |      |
            | isVip         |     是否是vip      |  int32   |      |
            | level         |     玩家等级      |  int32   |      |
            | nickname         |     好友玩家昵称      |  string   |      |
            | patrolInviteStatus         |     巡逻任务邀请状态 1-已邀请 0-未邀请      |  int32   |      |
            | redHot         |     显示红点      |  boolean   |      |
            | score         |     玩家评分      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "recommendList": [
            {
                "coinType": 0,
                "coinValue": 0,
                "delete": true,
                "friendPlayerId": 0,
                "icon": "",
                "isVip": 0,
                "level": 0,
                "nickname": "",
                "patrolInviteStatus": 0,
                "redHot": true,
                "score": 0
            }
        ]
    },
    "message": "",
    "msg": ""
}
```



--------分割线
对好友主动操作

**接口地址** `/pasture/app/friend/operation`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| friendOptParameters         |      friendOptParameters   |     body        |       true      | 好友主动操作请求参数   | 好友主动操作请求参数     |
            



**schema属性说明**
  
**好友主动操作请求参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| code  | 操作编码 |   body    |   false   |string  |       |
| friendPlayerId  | 好友玩家ID |   body    |   false   |int64  |       |
| playerId  | 玩家ID |   body    |   false   |int64  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«消耗操作结果信息»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    消耗操作结果信息   |   消耗操作结果信息    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**消耗操作结果信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| bizData         |     业务相关数据      |  object   |      |
            | coins         |     能量币数值变化      |  array   | AwardCoinDTO     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | remind         |     提醒内容      |  string   |      |
            | status         |     1-成功 2-代币不足 3-会员级别不足 4当日次数已用完 5积分不足 6事件已存在无效操作 7道具不足       |  int32   |      |
            

**AwardCoinDTO**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| name         |           |  string   |      |
            | num         |           |  int32   |      |
            | type         |           |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "bizData": {},
        "coins": [
            {
                "name": "",
                "num": 0,
                "type": 0
            }
        ],
        "isVip": 0,
        "remind": "",
        "status": 0
    },
    "message": "",
    "msg": ""
}
```



--------分割线
获取好友可操作列表

**接口地址** `/pasture/app/friend/operation/list`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| friendPlayerId         |      friendPlayerId   |     query        |       true      | integer   |      |
            | playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«好友操作页签信息»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   好友操作页签信息    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**好友操作页签信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| checked         |     是否选中      |  boolean   |      |
            | optList         |     好友操作列表      |  array   | 好友操作     |
            | redHot         |     显示红点      |  boolean   |      |
            | remind         |     操作列表为空时客户端提示信息      |  string   |      |
            | sort         |     排序值      |  int32   |      |
            | tab         |     页签名称      |  string   |      |
            

**好友操作**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| code         |     用于前端显示资源      |  string   |      |
            | coinType         |     代币类型0-不需要 (1, 金币),(8, 免费次数),(9, 背包道具)      |  int32   |      |
            | coinValue         |     消耗值      |  int32   |      |
            | description         |     描述      |  string   |      |
            | isVip         |     vip玩家专属操作      |  int32   |      |
            | name         |     操作名称      |  string   |      |
            | num         |     道具数量      |  int32   |      |
            | optId         |     optId      |  int64   |      |
            | packId         |     道具id      |  int64   |      |
            | playerPackId         |     玩家道具id      |  int64   |      |
            | type         |     操作类型 好友相关(操作类型 1-黏糊糊 2-爱心 3-蜘蛛网 4恐吓 5帮忙       |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "checked": true,
            "optList": [
                {
                    "code": "",
                    "coinType": 0,
                    "coinValue": 0,
                    "description": "",
                    "isVip": 0,
                    "name": "",
                    "num": 0,
                    "optId": 0,
                    "packId": 0,
                    "playerPackId": 0,
                    "type": 0
                }
            ],
            "redHot": true,
            "remind": "",
            "sort": 0,
            "tab": ""
        }
    ],
    "message": "",
    "msg": ""
}
```


