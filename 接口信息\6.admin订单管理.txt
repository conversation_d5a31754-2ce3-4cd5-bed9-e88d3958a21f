导出

**接口地址** `/pasture/admin/order/export`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| appCode         |      应用编码   |     query        |       true      | string   |      |
            | endTime         |      结束时间   |     query        |       true      | string   |      |
            | payType         |      支付方式 1微信公众号 2小天才服务号 3线下支付 4小天才表端支付   |     query        |       true      | integer   |      |
            | startTime         |      开始时间   |     query        |       true      | string   |      |
            | type         |      订单类型 1所有订单 2入账订单   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

暂无




**响应示例**


```json

```



--------分割线

导出2

**接口地址** `/pasture/admin/order/export2`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

暂无




**响应示例**


```json

```



--------分割线
获取枚举（1支付方式）

**接口地址** `/pasture/admin/order/getEnum`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| id         |      id   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«枚举对象»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   枚举对象    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**枚举对象**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| code         |     编码      |  int32   |      |
            | name         |     名称      |  string   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "code": 0,
            "name": ""
        }
    ],
    "message": "",
    "msg": ""
}
```



--------分割线
列表

**接口地址** `/pasture/admin/order/list`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| param         |      param   |     body        |       true      | 后台获取玩家列表入参   | 后台获取玩家列表入参     |
            



**schema属性说明**
  
**后台获取玩家列表入参**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| appCode  | 渠道号 |   body    |   false   |string  |       |
| endTime  | 结束时间 |   body    |   false   |string  |       |
| name  | 名字 |   body    |   false   |string  |       |
| orderNo  | 订单号 |   body    |   false   |string  |       |
| page  | 当前页 |   body    |   false   |int32  |       |
| payType  | payType支付方式 1微信公众号 2小天才服务号 3线下支付 4小天才表端支付 5小天才H5支付 6华为支付 |   body    |   false   |int32  |       |
| playerId  | 玩家ID |   body    |   false   |int64  |       |
| size  | 每页大小 |   body    |   false   |int32  |       |
| startTime  | 开始时间 |   body    |   false   |string  |       |
| status  | 状态 0待支付 1已支付 2支付失败  3用户已支付，待确认  4已退款 5部分退款  |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------分割线
人工退款

**接口地址** `/pasture/admin/order/manualRefund`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| parameters         |      parameters   |     body        |       true      | 人工退款入参   | 人工退款入参     |
            



**schema属性说明**
  
**人工退款入参**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| amount  | 退款金额 单位元 |   body    |   false   |number  |       |
| coin  | 游戏币扣除 |   body    |   false   |int32  |       |
| days  | 退回天数 |   body    |   false   |int32  |       |
| goodsIds  | 商品ids 使用，分割 |   body    |   false   |string  |       |
| name  | 操作人名称 |   body    |   false   |string  |       |
| orderId  | 订单id |   body    |   true   |int32  |       |
| refundDesc  | 退款描述(显示给用户的话) |   body    |   false   |string  |       |
| type  | 类型 1全款 2部分 |   body    |   true   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«int»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    int32   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": 0,
    "message": "",
    "msg": ""
}
```



