<template>
  <div class="order-management">
    <div class="page-header">
      <h2>订单管理</h2>
      <div class="header-actions">
        <a-button type="primary" @click="showExportModal" :loading="exportLoading">
          <template #icon>
            <DownloadOutlined />
          </template>
          导出订单数据
        </a-button>
      </div>
    </div>

    <!-- 搜索表单 -->
    <a-card class="search-card" style="margin-bottom: 16px;">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="应用渠道">
          <AppSelector
            v-model="searchForm.appCode"
            width="150px"
          />
        </a-form-item>
        <a-form-item label="订单编号">
          <a-input v-model:value="searchForm.orderNo" placeholder="请输入订单编号" />
        </a-form-item>
        <a-form-item label="玩家ID">
          <a-input-number v-model:value="searchForm.playerId" placeholder="请输入玩家ID" style="width: 150px;" />
        </a-form-item>
        <a-form-item label="支付方式">
          <a-select v-model:value="searchForm.payType" placeholder="请选择支付方式" style="width: 150px;">
            <a-select-option :value="1">微信公众号</a-select-option>
            <a-select-option :value="2">小天才服务号</a-select-option>
            <a-select-option :value="3">线下支付</a-select-option>
            <a-select-option :value="4">小天才表端支付</a-select-option>
            <a-select-option :value="5">小天才H5支付</a-select-option>
            <a-select-option :value="6">华为支付</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="订单状态">
          <a-select v-model:value="searchForm.status" placeholder="请选择订单状态" style="width: 150px;">
            <a-select-option :value="0">待支付</a-select-option>
            <a-select-option :value="1">已支付</a-select-option>
            <a-select-option :value="2">支付失败</a-select-option>
            <a-select-option :value="3">用户已支付，待确认</a-select-option>
            <a-select-option :value="4">已退款</a-select-option>
            <a-select-option :value="5">部分退款</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="创建时间">
          <a-range-picker
            v-model:value="dateRange"
            format="YYYY-MM-DD"
            placeholder="['开始时间', '结束时间']"
            style="width: 240px;"
            @change="handleDateChange"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="searchOrders" :loading="loading">
            搜索
          </a-button>
          <a-button style="margin-left: 8px;" @click="resetSearch">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 订单列表 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="orders"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'payType'">
            <a-tag :color="getPayTypeColor(record.payType)">
              {{ getPayTypeName(record.payType) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusName(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'amount'">
            ¥{{ Number(record.amount).toFixed(2) }}
          </template>
          <template v-if="column.key === 'createTime'">
            {{ formatTime(record.createTime) }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewOrder(record)">
                查看详情
              </a-button>
              <a-button 
                v-if="record.status === 1" 
                type="link" 
                size="small" 
                danger 
                @click="showRefundModal(record)"
              >
                退款
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 退款弹窗 -->
    <a-modal
      v-model:open="refundModalVisible"
      title="订单退款"
      @ok="handleRefund"
      @cancel="refundModalVisible = false"
      :confirm-loading="refundLoading"
    >
      <a-form :model="refundForm" layout="vertical">
        <a-form-item label="退款类型">
          <a-radio-group v-model:value="refundForm.type">
            <a-radio :value="1">全额退款</a-radio>
            <a-radio :value="2">部分退款</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item v-if="refundForm.type === 2" label="退款金额（元）">
          <a-input-number
            v-model:value="refundForm.amount"
            :min="0.01"
            :max="currentOrder?.amount ? Number(currentOrder.amount) : 0"
            :precision="2"
            style="width: 100%;"
          />
        </a-form-item>
        <a-form-item label="退款原因">
          <a-textarea v-model:value="refundForm.refundDesc" placeholder="请输入退款原因" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 导出弹窗 -->
    <a-modal
      v-model:open="exportModalVisible"
      title="数据导出"
      @ok="handleExport"
      @cancel="exportModalVisible = false"
      :confirm-loading="exportLoading"
      width="500px"
    >
      <a-form :model="exportForm" layout="vertical">
        <a-form-item label="应用渠道">
          <AppSelector
            v-model="exportForm.appCode"
            width="100%"
          />
        </a-form-item>
        <a-form-item label="订单时间" required>
          <a-range-picker
            v-model:value="exportDateRange"
            format="YYYY-MM-DD"
            style="width: 100%;"
            placeholder="['开始日期', '结束日期']"
          />
        </a-form-item>
        <a-form-item label="订单类型" required>
          <a-radio-group v-model:value="exportForm.type">
            <a-radio :value="1">所有订单</a-radio>
            <a-radio :value="2">入账订单</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { DownloadOutlined } from '@ant-design/icons-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  getOrderList,
  refundOrder,
  type OrderQueryParams,
  type Order,
  type RefundParams,
  type ExportOrderParams
} from '../api/order'
import AppSelector from '../components/AppSelector.vue'
import { useAppStore } from '../stores/app'
import { handleDateRangeChange, formatToApiDate } from '../utils/dateFormat'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const refundLoading = ref(false)
const orders = ref<Order[]>([])
const dateRange = ref<[Dayjs, Dayjs] | null>(null)
const refundModalVisible = ref(false)
const exportModalVisible = ref(false)
const currentOrder = ref<Order | null>(null)
const exportDateRange = ref<[Dayjs, Dayjs] | null>(null)

// 搜索表单
const searchForm = reactive<OrderQueryParams>({
  appCode: undefined,
  orderNo: undefined,
  playerId: undefined,
  payType: undefined,
  status: undefined,
  startTime: undefined,
  endTime: undefined,
  page: 1,
  size: 10
})

// 退款表单
const refundForm = reactive<RefundParams>({
  orderId: 0,
  type: 1,
  amount: undefined,
  refundDesc: '',
  name: '管理员'
})

// 导出表单
const exportForm = reactive({
  appCode: '',
  endTime: '',
  startTime: '',
  type: 1 // 默认所有订单
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '订单ID',
    dataIndex: 'id',
    key: 'id',
    width: 80
  },
  {
    title: '订单编号',
    dataIndex: 'flowNo',
    key: 'flowNo',
    width: 180
  },
  {
    title: '玩家ID',
    dataIndex: 'playerId',
    key: 'playerId',
    width: 100
  },
  {
    title: '玩家名称',
    dataIndex: 'userName',
    key: 'userName',
    width: 120
  },
  {
    title: '订单金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 100
  },
  {
    title: '支付方式',
    dataIndex: 'payType',
    key: 'payType',
    width: 120
  },
  {
    title: '订单状态',
    dataIndex: 'status',
    key: 'status',
    width: 120
  },
  {
    title: '应用渠道',
    dataIndex: 'appCode',
    key: 'appCode',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
]

// 工具函数
const getPayTypeName = (payType: number) => {
  const types: Record<number, string> = {
    1: '微信公众号',
    2: '小天才服务号',
    3: '线下支付',
    4: '小天才表端支付',
    5: '小天才H5支付',
    6: '华为支付'
  }
  return types[payType] || '未知'
}

const getPayTypeColor = (payType: number) => {
  const colors: Record<number, string> = {
    1: 'green',
    2: 'blue',
    3: 'orange',
    4: 'purple',
    5: 'cyan',
    6: 'red'
  }
  return colors[payType] || 'default'
}

const getStatusName = (status: number) => {
  const statuses: Record<number, string> = {
    0: '待支付',
    1: '已支付',
    2: '支付失败',
    3: '用户已支付，待确认',
    4: '已退款',
    5: '部分退款'
  }
  return statuses[status] || '未知'
}

const getStatusColor = (status: number) => {
  const colors: Record<number, string> = {
    0: 'orange',
    1: 'green',
    2: 'red',
    3: 'blue',
    4: 'purple',
    5: 'cyan'
  }
  return colors[status] || 'default'
}

const formatTime = (timeStr: string) => {
  if (!timeStr) return '-'
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss')
}

// 处理日期范围变化
const handleDateChange = (dates: [Dayjs, Dayjs] | null) => {
  handleDateRangeChange(dates, (startTime?: string, endTime?: string) => {
    searchForm.startTime = startTime
    searchForm.endTime = endTime
  })
}

// 搜索订单
const searchOrders = async () => {
  loading.value = true
  try {
    const params: any = {
      page: pagination.current,
      size: pagination.pageSize
    }

    // 只有选择了具体应用渠道才添加appCode参数
    if (searchForm.appCode && searchForm.appCode.trim()) {
      params.appCode = searchForm.appCode
    }

    // 只添加有值的搜索参数
    if (searchForm.orderNo?.trim()) params.orderNo = searchForm.orderNo.trim()
    if (searchForm.playerId) params.playerId = searchForm.playerId
    if (searchForm.payType) params.payType = searchForm.payType
    if (searchForm.status !== undefined) params.status = searchForm.status
    if (searchForm.startTime) params.startTime = searchForm.startTime
    if (searchForm.endTime) params.endTime = searchForm.endTime

    const response = await getOrderList(params)

    if (response.data) {
      // 根据接口文档，data直接是数组
      if (Array.isArray(response.data)) {
        orders.value = response.data
        pagination.total = response.data.length
      } else if (response.data.list) {
        // 如果是分页格式
        orders.value = response.data.list || []
        pagination.total = response.data.total || 0
      } else {
        orders.value = []
        pagination.total = 0
      }
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    message.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    appCode: undefined,
    orderNo: undefined,
    playerId: undefined,
    payType: undefined,
    status: undefined,
    startTime: undefined,
    endTime: undefined,
    page: 1,
    size: 10
  })
  dateRange.value = null
  pagination.current = 1
  searchOrders()
}

// 处理表格变化
const handleTableChange: TableProps['onChange'] = (pag) => {
  pagination.current = pag.current || 1
  pagination.pageSize = pag.pageSize || 10
  searchOrders()
}

// 查看订单详情
const viewOrder = (order: Order) => {
  // TODO: 实现订单详情查看
  message.info('订单详情功能待实现')
}

// 显示退款弹窗
const showRefundModal = (order: Order) => {
  currentOrder.value = order
  refundForm.orderId = order.id!
  refundForm.type = 1
  refundForm.amount = undefined
  refundForm.refundDesc = ''
  refundModalVisible.value = true
}

// 处理退款
const handleRefund = async () => {
  if (!currentOrder.value) return

  refundLoading.value = true
  try {
    const params: RefundParams = {
      orderId: refundForm.orderId,
      type: refundForm.type,
      refundDesc: refundForm.refundDesc,
      name: refundForm.name
    }

    if (refundForm.type === 2 && refundForm.amount) {
      params.amount = refundForm.amount
    }

    await refundOrder(params)
    message.success('退款申请提交成功')
    refundModalVisible.value = false
    searchOrders() // 刷新列表
  } catch (error) {
    console.error('退款失败:', error)
    message.error('退款申请失败')
  } finally {
    refundLoading.value = false
  }
}

// 显示导出弹窗
const showExportModal = () => {
  exportModalVisible.value = true
  // 重置导出表单
  exportForm.appCode = ''
  exportForm.type = 1
  exportDateRange.value = null
}

// 处理导出 - 超链接方式，无需token认证
const handleExport = async () => {
  try {
    // 验证必填项
    if (!exportDateRange.value || exportDateRange.value.length !== 2) {
      message.error('请选择订单时间范围')
      return
    }

    exportLoading.value = true

    // 格式化日期 - 根据接口文档，时间格式应该是 YYYY-MM-DD HH:mm:ss
    const startTime = exportDateRange.value[0].format('YYYY-MM-DD') + ' 00:00:00'
    const endTime = exportDateRange.value[1].format('YYYY-MM-DD') + ' 23:59:59'

    // 构建查询参数 - 根据接口文档的必填参数
    const params = new URLSearchParams({
      appCode: exportForm.appCode || '', // 应用编码，必填
      startTime, // 开始时间，必填
      endTime, // 结束时间，必填
      type: exportForm.type.toString() // 订单类型，必填
    })

    // 构建导出URL
    const exportUrl = `https://api.zj7hui.com/pasture/admin/order/export?${params.toString()}`

    console.log('订单导出URL:', exportUrl)

    // 创建a标签并直接跳转到服务器下载地址
    const link = document.createElement('a')
    link.href = exportUrl
    link.target = '_blank' // 在新窗口打开，避免影响当前页面

    // 添加到DOM，触发跳转，然后移除
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    message.success('正在导出订单数据，请稍候...')
    exportModalVisible.value = false
  } catch (error: any) {
    console.error('导出订单数据失败:', error)
    message.error('导出订单数据失败')
  } finally {
    exportLoading.value = false
  }
}

// 组件挂载时初始化
onMounted(async () => {
  // 等待应用列表加载完成后设置默认为"所有"
  const appStore = useAppStore()
  await appStore.initAppList()

  // 默认选择"所有"（空字符串）
  searchForm.appCode = ""
  console.log('OrderManagement设置默认为所有应用')
  searchOrders()
})

// 组件卸载时清理数据
onUnmounted(() => {
  orders.value = []
  pagination.total = 0
  loading.value = false

  // 清理搜索表单
  Object.assign(searchForm, {
    appCode: undefined,
    orderNo: undefined,
    playerId: undefined,
    payType: undefined,
    status: undefined,
    startTime: undefined,
    endTime: undefined,
    page: 1,
    size: 10
  })
  dateRange.value = null

  console.log('订单管理页面已清理')
})
</script>

<style scoped>
.order-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.search-card .ant-form-item {
  margin-bottom: 16px;
}

.export-link {
  text-decoration: none;
}

.export-link:hover {
  text-decoration: none;
}
</style>
