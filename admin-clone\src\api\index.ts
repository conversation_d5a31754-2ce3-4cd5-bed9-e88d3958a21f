import axios from 'axios'
import type { AxiosInstance, AxiosResponse } from 'axios'
import { message } from 'ant-design-vue'

// API配置 - 支持环境变量和localStorage自定义
const getApiBaseUrl = () => {
  // 优先使用localStorage中的自定义API地址
  const customApiUrl = localStorage.getItem('custom_api_url')
  if (customApiUrl) {
    console.log('使用localStorage自定义API地址:', customApiUrl)
    return customApiUrl
  }

  // 获取环境变量配置
  const apiMode = import.meta.env.VITE_API_MODE || 'direct'
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'https://api.zj7hui.com/pasture'

  console.log('API模式:', apiMode)
  console.log('API基础地址:', apiBaseUrl)

  // 根据API模式决定使用代理还是直接访问
  if (apiMode === 'proxy' && import.meta.env.DEV) {
    console.log('使用开发代理模式')
    return '/pasture' // 使用vite代理
  }

  // 直接访问服务器API
  console.log('使用直接访问模式')
  return apiBaseUrl
}

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json'
  },
  withCredentials: false // 处理跨域
})

// 请求拦截器
api.interceptors.request.use(
  (config: any) => {
    // 添加token - 后端需要Accesstoken字段
    const token = localStorage.getItem('token')
    console.log('请求拦截器 - 当前token:', token)
    console.log('请求拦截器 - 请求URL:', config.url)

    if (token && config.headers) {
      config.headers.Accesstoken = token
      console.log('请求拦截器 - 已添加Accesstoken:', token)
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log('API响应:', response)
    const { data } = response

    // 根据接口文档，返回格式是 { code, data, message, msg }
    if (data.code === '200' || data.code === 200) {
      return data
    } else {
      console.error('API返回错误:', data)
      // 不在这里显示错误消息，让调用方处理
      return Promise.reject(new Error(data.message || data.msg || '请求失败'))
    }
  },
  (error) => {
    console.error('API请求错误:', error)

    if (error.response?.status === 401) {
      // 未授权，跳转到登录页
      localStorage.removeItem('token')
      window.location.href = '#/login'
    } else if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
      console.log('网络连接错误，可能需要使用演示模式')
    }

    return Promise.reject(error)
  }
)

export default api
