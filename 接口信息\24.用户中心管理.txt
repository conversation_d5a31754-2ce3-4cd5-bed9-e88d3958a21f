获取动物列表

**接口地址** `/pasture/player/animalList`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| page         |      page   |     query        |       true      | integer   |      |
            | playerId         |      playerId   |     query        |       true      | integer   |      |
            | size         |      size   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«Page«动物»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   动物    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**动物**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| animalId         |     id      |  int64   |      |
            | coinType         |     coinType养殖所需要的代币类型：0-不需要 1-金币，2-能量 3道具      |  int32   |      |
            | coinValue         |     coinValue种植所需要的代币值      |  int32   |      |
            | createTime         |     createTime      |  date-time   |      |
            | dropMemo         |     dropMemo掉落备注      |  string   |      |
            | gainCoinType         |     gainCoinType收获代币类型：0-不需要 1-金币，2-能量      |  int32   |      |
            | gainCoinVal         |     gainCoinType收获代币值      |  int32   |      |
            | gainExp         |     gainExp收获经验值      |  int32   |      |
            | icon         |     icon      |  string   |      |
            | link         |     link获取链接1：      |  string   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     modelSpine模型动画      |  string   |      |
            | name         |     name      |  string   |      |
            | packId         |     packId碎片id      |  int64   |      |
            | payType         |     payType类型 1金币 2vip 3svip      |  int32   |      |
            | pickMaxValue         |     pickMaxValue采摘最大值      |  int32   |      |
            | pickMinValue         |     pickMinValue采摘最小值      |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     状态 1未解锁 2已解锁      |  int32   |      |
            | subPackId         |     幼崽      |  int64   |      |
            | title         |     title标题      |  string   |      |
            | unlockLevel         |     unlockLevel解锁等级      |  int32   |      |
            | unlockPrice         |     unlockPrice价格      |  int32   |      |
            | unlockType         |     unlockType 类型 1-等级 2-价格 3-vip 4-关注公众号 5 svip      |  int32   |      |
            | updateTime         |     updateTime      |  date-time   |      |
            | version         |     version版本号      |  string   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "animalId": 0,
            "coinType": 0,
            "coinValue": 0,
            "createTime": "",
            "dropMemo": "",
            "gainCoinType": 0,
            "gainCoinVal": 0,
            "gainExp": 0,
            "icon": "",
            "link": "",
            "model": "",
            "modelSpine": "",
            "name": "",
            "packId": 0,
            "payType": 0,
            "pickMaxValue": 0,
            "pickMinValue": 0,
            "score": 0,
            "seq": 0,
            "status": 0,
            "subPackId": 0,
            "title": "",
            "unlockLevel": 0,
            "unlockPrice": 0,
            "unlockType": 0,
            "updateTime": "",
            "version": ""
        }
    ],
    "message": "",
    "msg": ""
}
```



----------分割线
养殖

**接口地址** `/pasture/player/breed`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| param         |      param   |     body        |       true      | 养殖参数   | 养殖参数     |
            



**schema属性说明**
  
**养殖参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| animalId  | 动物ID |   body    |   false   |int64  |       |
| packId  | 背包id |   body    |   false   |int64  |       |
| playerId  | 玩家ID |   body    |   false   |int64  |       |
| type  | 类型1 正常养殖 2清除原有动物养殖 |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------分割线

获取玩家币信息

**接口地址** `/pasture/player/coin`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«com.qimingxing.journey.model.Player»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    com.qimingxing.journey.model.Player   |   com.qimingxing.journey.model.Player    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**com.qimingxing.journey.model.Player**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| account         |     account      |  string   |      |
            | accumulatePc         |     accumulatePc累计产出金币      |  int32   |      |
            | appCode         |     appCode      |  string   |      |
            | birthday         |     birthday生日      |  date-time   |      |
            | coin         |     金币      |  int32   |      |
            | consumePc         |     consumePc累计消耗金币      |  int32   |      |
            | createTime         |     createTime创建时间      |  date-time   |      |
            | energy         |     能量币      |  int32   |      |
            | expValue         |     expValue当前经验值      |  int64   |      |
            | grade         |     所选年级 0学前 1一年级 2二年级 3三年级 4四年级 5五年级 6六年级      |  int32   |      |
            | icon         |     icon玩家头像图标地址      |  string   |      |
            | id         |     idID      |  int64   |      |
            | lastLoginTime         |     lastLoginTime最后登录时间      |  date-time   |      |
            | lastVimTime         |     lastVimTime最后更新活力时间      |  date-time   |      |
            | level         |     level等级      |  int32   |      |
            | likeNum         |     likeNum点赞数      |  int32   |      |
            | nextExpValue         |     nextExpValue下一个等级所需经验值      |  int64   |      |
            | nickname         |     nickname玩家昵称      |  string   |      |
            | ore         |     ore矿石      |  int32   |      |
            | outAccount         |     outAccount      |  string   |      |
            | plotId         |     plotId最后完成剧情id      |  int64   |      |
            | protectNum         |     保护次数      |  int32   |      |
            | protectTime         |     保护时间      |  date-time   |      |
            | pwd         |     pwd      |  string   |      |
            | rankMonth         |     rankMonth排行榜月份（月周期使用）      |  string   |      |
            | rankScore         |     rankScore排位积分      |  int32   |      |
            | roleType         |     roleType角色模型 0男角色 1女角色      |  int32   |      |
            | salt         |     salt      |  string   |      |
            | sex         |     sex玩家性别 1-男 2-女      |  int32   |      |
            | status         |     status玩家状态 0-不可用 1-正常       |  int32   |      |
            | token         |     token最后登录token      |  string   |      |
            | updateTime         |     updateTime更新时间      |  date-time   |      |
            | userId         |     用户id      |  string   |      |
            | version         |     version      |  string   |      |
            | vim         |     vim活力      |  int32   |      |
            | vimLimit         |     vimLimit活力上限      |  int32   |      |
            | vipEndTime         |     vipEndTimevip结束时间      |  date-time   |      |
            | vipOre         |     vipOrevip矿石      |  int32   |      |
            | vipStatus         |     vip状态      |  int32   |      |
            | vipType         |     vipType1-月会员 2-季会员 3-年会员      |  int32   |      |
            | wish         |     wish许愿币      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "account": "",
        "accumulatePc": 0,
        "appCode": "",
        "birthday": "",
        "coin": 0,
        "consumePc": 0,
        "createTime": "",
        "energy": 0,
        "expValue": 0,
        "grade": 0,
        "icon": "",
        "id": 0,
        "lastLoginTime": "",
        "lastVimTime": "",
        "level": 0,
        "likeNum": 0,
        "nextExpValue": 0,
        "nickname": "",
        "ore": 0,
        "outAccount": "",
        "plotId": 0,
        "protectNum": 0,
        "protectTime": "",
        "pwd": "",
        "rankMonth": "",
        "rankScore": 0,
        "roleType": 0,
        "salt": "",
        "sex": 0,
        "status": 0,
        "token": "",
        "updateTime": "",
        "userId": "",
        "version": "",
        "vim": 0,
        "vimLimit": 0,
        "vipEndTime": "",
        "vipOre": 0,
        "vipStatus": 0,
        "vipType": 0,
        "wish": 0
    },
    "message": "",
    "msg": ""
}
```



----------分割线
获取玩家个人中心信息

**接口地址** `/pasture/player/getPlayerCenterInfo`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«玩家个人中心»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    玩家个人中心   |   玩家个人中心    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**玩家个人中心**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| animal         |     animal      |  LevelBo   | LevelBo     |
            | grade         |     年级相关信息      |  玩家年级信息   | 玩家年级信息     |
            | sex         |     性别 0未设置 1-男 2-女      |  int32   |      |
            

**LevelBo**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| icon         |           |  string   |      |
            | level         |           |  int32   |      |
            | name         |           |  string   |      |
            

**玩家年级信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| content         |     禁止修改时，客户端提示信息      |  string   |      |
            | grade         |     手表端年级      |  int32   |      |
            | status         |     修改年级操作是否可用 1-可用 0-不可用      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "animal": {
            "icon": "",
            "level": 0,
            "name": ""
        },
        "grade": {
            "content": "",
            "grade": 0,
            "status": 0
        },
        "sex": 0
    },
    "message": "",
    "msg": ""
}
```



----------分割线
获取玩家年级信息

**接口地址** `/pasture/player/getPlayerGrade`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«玩家年级信息»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    玩家年级信息   |   玩家年级信息    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**玩家年级信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| content         |     禁止修改时，客户端提示信息      |  string   |      |
            | grade         |     手表端年级      |  int32   |      |
            | status         |     修改年级操作是否可用 1-可用 0-不可用      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "content": "",
        "grade": 0,
        "status": 0
    },
    "message": "",
    "msg": ""
}
```



----------分割线
获取操作列表

**接口地址** `/pasture/player/player/operation/list`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            | version         |      version   |     query        |       true      | string   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«好友操作页签信息»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    好友操作页签信息   |   好友操作页签信息    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**好友操作页签信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| checked         |     是否选中      |  boolean   |      |
            | optList         |     好友操作列表      |  array   | 好友操作     |
            | redHot         |     显示红点      |  boolean   |      |
            | remind         |     操作列表为空时客户端提示信息      |  string   |      |
            | sort         |     排序值      |  int32   |      |
            | tab         |     页签名称      |  string   |      |
            

**好友操作**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| code         |     用于前端显示资源      |  string   |      |
            | coinType         |     代币类型0-不需要 (1, 金币),(8, 免费次数),(9, 背包道具)      |  int32   |      |
            | coinValue         |     消耗值      |  int32   |      |
            | description         |     描述      |  string   |      |
            | isVip         |     vip玩家专属操作      |  int32   |      |
            | name         |     操作名称      |  string   |      |
            | num         |     道具数量      |  int32   |      |
            | optId         |     optId      |  int64   |      |
            | packId         |     道具id      |  int64   |      |
            | playerPackId         |     玩家道具id      |  int64   |      |
            | type         |     操作类型 好友相关(操作类型 1-黏糊糊 2-爱心 3-蜘蛛网 4恐吓 5帮忙       |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "checked": true,
        "optList": [
            {
                "code": "",
                "coinType": 0,
                "coinValue": 0,
                "description": "",
                "isVip": 0,
                "name": "",
                "num": 0,
                "optId": 0,
                "packId": 0,
                "playerPackId": 0,
                "type": 0
            }
        ],
        "redHot": true,
        "remind": "",
        "sort": 0,
        "tab": ""
    },
    "message": "",
    "msg": ""
}
```



----------分割线
获取玩家信息

**接口地址** `/pasture/player/playerDetail`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«玩家详情»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    玩家详情   |   玩家详情    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**玩家详情**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | breed         |     动物养殖      |  动物养殖   | 动物养殖     |
            | coin         |     金币      |  int32   |      |
            | energy         |     能量      |  int32   |      |
            | equipageScore         |     装备评分      |  int32   |      |
            | equipages         |     装备      |  array   | 玩家装备     |
            | events         |     互动事件      |  array   | 互动事件     |
            | expValue         |     expValue当前经验值      |  int64   |      |
            | followWx         |     是否关注微信公众号 1-已关注 0-未关注      |  int32   |      |
            | icon         |     玩家头像图标地址      |  string   |      |
            | interacts         |     互动      |  array   | 互动事件     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | level         |     人物等级      |  int32   |      |
            | levelName         |     官职      |  string   |      |
            | model         |     角色模型      |  string   |      |
            | nextExpValue         |     nextExpValue下一个等级所需经验值      |  int64   |      |
            | nickname         |     玩家昵称      |  string   |      |
            | ore         |     粮草      |  int32   |      |
            | playerId         |     玩家id      |  int64   |      |
            | protectStatus         |     保护状态 1有保护 2无保护      |  int32   |      |
            | rankScore         |     排位积分      |  int32   |      |
            | score         |     总评分      |  int32   |      |
            | sex         |     玩家性别 1-男 2-女      |  int32   |      |
            | status         |     玩家状态 0-不可用 1-正常      |  int32   |      |
            | userId         |     用户id      |  string   |      |
            | vim         |     活力      |  int32   |      |
            | vimLimit         |     活力上限      |  int32   |      |
            | vipOre         |     vip粮草      |  int32   |      |
            | vipStatus         |     vip状态      |  int32   |      |
            | wish         |     许愿值      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**动物养殖**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| animalId         |     animalId动物id      |  int64   |      |
            | breedTime         |     breedTime养殖时间      |  date-time   |      |
            | createTime         |     createTime      |  date-time   |      |
            | downTime         |     倒计时      |  int64   |      |
            | modelSpine         |     动效      |  string   |      |
            | petId         |     petId宠物id      |  int64   |      |
            | pickStatus         |     是否可以采摘 1可以 2自己不能偷 3好友不能偷      |  int32   |      |
            | playerBreedId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态1 成长中 2待收获 3已收获      |  int32   |      |
            | suspendTime         |     暂停时间 不为空就是被暂停      |  date-time   |      |
            

**玩家装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| id         |     id      |  int64   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | type         |     类型 1神兵 2宝马 3皮肤 4兵书 5虎符      |  int32   |      |
            

**互动事件**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coin         |     金币      |  int32   |      |
            | createTime         |     createTime      |  date-time   |      |
            | detail         |     详情      |  string   |      |
            | eventType         |     操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽  8便便 9蚊子 10老鼠      |  int32   |      |
            | eventVal         |     eventVal事件值      |  int32   |      |
            | finishVal         |     eventVal事件完成值      |  int32   |      |
            | icon         |     图片      |  string   |      |
            | playerEventId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "attrs": [
            {
                "attrType": 0,
                "attrValue": 0,
                "type": 0
            }
        ],
        "breed": {
            "animalId": 0,
            "breedTime": "",
            "createTime": "",
            "downTime": 0,
            "modelSpine": "",
            "petId": 0,
            "pickStatus": 0,
            "playerBreedId": 0,
            "playerId": 0,
            "seq": 0,
            "status": 0,
            "suspendTime": ""
        },
        "coin": 0,
        "energy": 0,
        "equipageScore": 0,
        "equipages": [
            {
                "id": 0,
                "model": "",
                "modelSpine": "",
                "name": "",
                "type": 0
            }
        ],
        "events": [
            {
                "coin": 0,
                "createTime": "",
                "detail": "",
                "eventType": 0,
                "eventVal": 0,
                "finishVal": 0,
                "icon": "",
                "playerEventId": 0,
                "playerId": 0
            }
        ],
        "expValue": 0,
        "followWx": 0,
        "icon": "",
        "interacts": [
            {
                "coin": 0,
                "createTime": "",
                "detail": "",
                "eventType": 0,
                "eventVal": 0,
                "finishVal": 0,
                "icon": "",
                "playerEventId": 0,
                "playerId": 0
            }
        ],
        "isVip": 0,
        "level": 0,
        "levelName": "",
        "model": "",
        "nextExpValue": 0,
        "nickname": "",
        "ore": 0,
        "playerId": 0,
        "protectStatus": 0,
        "rankScore": 0,
        "score": 0,
        "sex": 0,
        "status": 0,
        "userId": "",
        "vim": 0,
        "vimLimit": 0,
        "vipOre": 0,
        "vipStatus": 0,
        "wish": 0
    },
    "message": "",
    "msg": ""
}
```



----------分割线
弃养宠物

**接口地址** `/pasture/player/playerInfo/clear`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------分割线
能量源

**接口地址** `/pasture/player/playerInfo/energyMask`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| optId         |      optId   |     query        |       true      | integer   |      |
            | playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```


----------分割线
喂养

**接口地址** `/pasture/player/playerInfo/feed`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| optId         |      optId   |     query        |       true      | integer   |      |
            | playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------分割线
获取收获宠物

**接口地址** `/pasture/player/playerInfo/harvest`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------分割线
等级列表

**接口地址** `/pasture/player/playerInfo/levelList`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

暂无





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«LevelBo»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   LevelBo    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**LevelBo**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| icon         |           |  string   |      |
            | level         |           |  int32   |      |
            | name         |           |  string   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "icon": "",
            "level": 0,
            "name": ""
        }
    ],
    "message": "",
    "msg": ""
}
```



----------分割线
升级

**接口地址** `/pasture/player/playerInfo/upgrade`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«升级后玩家信息»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    升级后玩家信息   |   升级后玩家信息    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**升级后玩家信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coinType         |     收获代币类型： 1-金币，2-能量      |  int32   |      |
            | coinVal         |     收获代币值      |  int32   |      |
            | drops         |     物品掉落      |  array   | 掉落物品明细     |
            | level         |     人物等级      |  int32   |      |
            | player         |     玩家      |  玩家详情   | 玩家详情     |
            | score         |     评分      |  int32   |      |
            

**掉落物品明细**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| code         |     图标      |  string   |      |
            | name         |     名称      |  string   |      |
            | sourceId         |     sourceId道具id      |  int64   |      |
            | sourceType         |     sourceType类型 1背包方案 2背包道具 3主角经验 4能量币 5体力 6矿石 7宠物 8装备 9排位积分 10 金币      |  int32   |      |
            | type         |     类型 1普通掉落 2首次掉落      |  int32   |      |
            | upgrade         |     人物升级信息      |  升级后玩家信息   | 升级后玩家信息     |
            | value         |     num数量      |  int32   |      |
            

**玩家详情**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | breed         |     动物养殖      |  动物养殖   | 动物养殖     |
            | coin         |     金币      |  int32   |      |
            | energy         |     能量      |  int32   |      |
            | equipageScore         |     装备评分      |  int32   |      |
            | equipages         |     装备      |  array   | 玩家装备     |
            | events         |     互动事件      |  array   | 互动事件     |
            | expValue         |     expValue当前经验值      |  int64   |      |
            | followWx         |     是否关注微信公众号 1-已关注 0-未关注      |  int32   |      |
            | icon         |     玩家头像图标地址      |  string   |      |
            | interacts         |     互动      |  array   | 互动事件     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | level         |     人物等级      |  int32   |      |
            | levelName         |     官职      |  string   |      |
            | model         |     角色模型      |  string   |      |
            | nextExpValue         |     nextExpValue下一个等级所需经验值      |  int64   |      |
            | nickname         |     玩家昵称      |  string   |      |
            | ore         |     粮草      |  int32   |      |
            | playerId         |     玩家id      |  int64   |      |
            | protectStatus         |     保护状态 1有保护 2无保护      |  int32   |      |
            | rankScore         |     排位积分      |  int32   |      |
            | score         |     总评分      |  int32   |      |
            | sex         |     玩家性别 1-男 2-女      |  int32   |      |
            | status         |     玩家状态 0-不可用 1-正常      |  int32   |      |
            | userId         |     用户id      |  string   |      |
            | vim         |     活力      |  int32   |      |
            | vimLimit         |     活力上限      |  int32   |      |
            | vipOre         |     vip粮草      |  int32   |      |
            | vipStatus         |     vip状态      |  int32   |      |
            | wish         |     许愿值      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**动物养殖**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| animalId         |     animalId动物id      |  int64   |      |
            | breedTime         |     breedTime养殖时间      |  date-time   |      |
            | createTime         |     createTime      |  date-time   |      |
            | downTime         |     倒计时      |  int64   |      |
            | modelSpine         |     动效      |  string   |      |
            | petId         |     petId宠物id      |  int64   |      |
            | pickStatus         |     是否可以采摘 1可以 2自己不能偷 3好友不能偷      |  int32   |      |
            | playerBreedId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态1 成长中 2待收获 3已收获      |  int32   |      |
            | suspendTime         |     暂停时间 不为空就是被暂停      |  date-time   |      |
            

**玩家装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| id         |     id      |  int64   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | type         |     类型 1神兵 2宝马 3皮肤 4兵书 5虎符      |  int32   |      |
            

**互动事件**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| coin         |     金币      |  int32   |      |
            | createTime         |     createTime      |  date-time   |      |
            | detail         |     详情      |  string   |      |
            | eventType         |     操作类型 1-苍蝇 2-肮脏 3-生病 4饥饿 5黑影 6野兽  8便便 9蚊子 10老鼠      |  int32   |      |
            | eventVal         |     eventVal事件值      |  int32   |      |
            | finishVal         |     eventVal事件完成值      |  int32   |      |
            | icon         |     图片      |  string   |      |
            | playerEventId         |     id      |  int64   |      |
            | playerId         |     playerId      |  int64   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "coinType": 0,
        "coinVal": 0,
        "drops": [
            {
                "code": "",
                "name": "",
                "sourceId": 0,
                "sourceType": 0,
                "type": 0,
                "upgrade": {},
                "value": 0
            }
        ],
        "level": 0,
        "player": {
            "attrs": [
                {
                    "attrType": 0,
                    "attrValue": 0,
                    "type": 0
                }
            ],
            "breed": {
                "animalId": 0,
                "breedTime": "",
                "createTime": "",
                "downTime": 0,
                "modelSpine": "",
                "petId": 0,
                "pickStatus": 0,
                "playerBreedId": 0,
                "playerId": 0,
                "seq": 0,
                "status": 0,
                "suspendTime": ""
            },
            "coin": 0,
            "energy": 0,
            "equipageScore": 0,
            "equipages": [
                {
                    "id": 0,
                    "model": "",
                    "modelSpine": "",
                    "name": "",
                    "type": 0
                }
            ],
            "events": [
                {
                    "coin": 0,
                    "createTime": "",
                    "detail": "",
                    "eventType": 0,
                    "eventVal": 0,
                    "finishVal": 0,
                    "icon": "",
                    "playerEventId": 0,
                    "playerId": 0
                }
            ],
            "expValue": 0,
            "followWx": 0,
            "icon": "",
            "interacts": [
                {
                    "coin": 0,
                    "createTime": "",
                    "detail": "",
                    "eventType": 0,
                    "eventVal": 0,
                    "finishVal": 0,
                    "icon": "",
                    "playerEventId": 0,
                    "playerId": 0
                }
            ],
            "isVip": 0,
            "level": 0,
            "levelName": "",
            "model": "",
            "nextExpValue": 0,
            "nickname": "",
            "ore": 0,
            "playerId": 0,
            "protectStatus": 0,
            "rankScore": 0,
            "score": 0,
            "sex": 0,
            "status": 0,
            "userId": "",
            "vim": 0,
            "vimLimit": 0,
            "vipOre": 0,
            "vipStatus": 0,
            "wish": 0
        },
        "score": 0
    },
    "message": "",
    "msg": ""
}
```



----------分割线
获取红点信息

**接口地址** `/pasture/player/queryReds`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«红点»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   红点    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**红点**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| ids         |     红点作用id      |  array   |      |
            | status         |     状态1-有红点 0-无红点      |  int32   |      |
            | type         |     类型  1.牧场可升级 (金币满足条件)\n2.幼崽可解锁 (金币满足条件，等级满足条件)\n3.装扮可解锁 (金币满足条件，等级满足条件)\n4.订单可完成(动物数量满足条件)        |  int32   |      |
            | types         |     红点作用类型      |  array   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "ids": [],
            "status": 0,
            "type": 0,
            "types": []
        }
    ],
    "message": "",
    "msg": ""
}
```



----------分割线
保存玩家年级信息

**接口地址** `/pasture/player/saveGrade`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| saveGradeParameters         |      saveGradeParameters   |     body        |       true      | 保存年级信息参数   | 保存年级信息参数     |
            



**schema属性说明**
  
**保存年级信息参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| grade  | 所选年级 0学前 1一年级 2二年级 3三年级 4四年级 5五年级 6六年级 |   body    |   false   |int32  |       |
| playerId  | 玩家ID |   body    |   false   |int64  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------分割线
保存玩家性别信息

**接口地址** `/pasture/player/saveSex`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| saveSexParameters         |      saveSexParameters   |     body        |       true      | 保存性别信息参数   | 保存性别信息参数     |
            



**schema属性说明**
  
**保存性别信息参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId  | 玩家ID |   body    |   false   |int64  |       |
| sex  | 1-男 2-女 |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------分割线
切换账号

**接口地址** `/pasture/player/switching`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| parameters         |      parameters   |     body        |       true      | 切换账号参数   | 切换账号参数     |
            



**schema属性说明**
  
**切换账号参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| swappedPlayerId  | 被交换用户id |   body    |   true   |int64  |       |
| switchingPlayerId  | 交换用户id |   body    |   true   |int64  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«boolean»                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    boolean   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": true,
    "message": "",
    "msg": ""
}
```



----------分割线
解锁动物

**接口地址** `/pasture/player/unlockAnimal`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| param         |      param   |     body        |       true      | 养殖参数   | 养殖参数     |
            



**schema属性说明**
  
**养殖参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| animalId  | 动物ID |   body    |   false   |int64  |       |
| packId  | 背包id |   body    |   false   |int64  |       |
| playerId  | 玩家ID |   body    |   false   |int64  |       |
| type  | 类型1 正常养殖 2清除原有动物养殖 |   body    |   false   |int32  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



----------分割线
更新玩家昵称及头像

**接口地址** `/pasture/player/update/nickname`


**请求方式** `POST`


**consumes** `["application/json"]`


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| updateNicknameParameters         |      updateNicknameParameters   |     body        |       true      | 更新玩家昵称头像请求参数   | 更新玩家昵称头像请求参数     |
            



**schema属性说明**
  
**更新玩家昵称头像请求参数**

| 参数名称         | 说明    |     参数类型 |  是否必须   |  类型  |  schema |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| icon  | 玩家头像 |   body    |   false   |string  |       |
| nickname  | 玩家新昵称 |   body    |   false   |string  |       |
| playerId  | 玩家ID |   body    |   false   |int64  |       |





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 201         | Created                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```

