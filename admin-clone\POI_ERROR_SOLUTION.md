# Apache POI库错误解决方案

## 🚨 错误信息

```json
{
  "code": "400",
  "message": "Handler dispatch failed; nested exception is java.lang.NoSuchMethodError: org.apache.poi.ss.usermodel.WorkbookFactory.create(Z)Lorg/apache/poi/ss/usermodel/Workbook;",
  "msg": "Handler dispatch failed; nested exception is java.lang.NoSuchMethodError: org.apache.poi.ss.usermodel.WorkbookFactory.create(Z)Lorg/apache/poi/ss/usermodel/Workbook;"
}
```

## 🔍 问题分析

### 错误原因
这是一个典型的Apache POI库版本不兼容问题：

1. **方法签名变化**：`WorkbookFactory.create(boolean)` 方法在不同POI版本中的签名发生了变化
2. **版本冲突**：项目中可能同时存在POI 3.x 和 4.x/5.x 版本的依赖
3. **类路径问题**：不同版本的POI jar包在类路径中冲突

### 影响范围
- 所有Excel导出功能
- 订单导出：`/pasture/admin/order/export`
- 退款导出：`/pasture/order/refund/export`
- 看板数据导出：`/pasture/admin/spectaculars/report`

## 🛠️ 解决方案

### 方案1：升级POI库（推荐）

#### Maven项目
在 `pom.xml` 中更新POI依赖：

```xml
<properties>
    <poi.version>5.2.4</poi.version>
</properties>

<dependencies>
    <!-- Apache POI 核心库 -->
    <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi</artifactId>
        <version>${poi.version}</version>
    </dependency>
    
    <!-- Apache POI OOXML 支持 (Excel 2007+) -->
    <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-ooxml</artifactId>
        <version>${poi.version}</version>
    </dependency>
    
    <!-- Apache POI Scratchpad (可选，用于PPT、Word等) -->
    <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-scratchpad</artifactId>
        <version>${poi.version}</version>
    </dependency>
</dependencies>
```

#### Gradle项目
在 `build.gradle` 中更新：

```gradle
dependencies {
    implementation 'org.apache.poi:poi:5.2.4'
    implementation 'org.apache.poi:poi-ooxml:5.2.4'
    implementation 'org.apache.poi:poi-scratchpad:5.2.4'
}
```

### 方案2：修改代码兼容性

如果无法升级POI版本，修改导出代码：

#### 旧代码（可能导致错误）
```java
// 这种写法在某些版本中会出错
Workbook workbook = WorkbookFactory.create(true);
```

#### 新代码（兼容性更好）
```java
// 方式1：直接创建工作簿
Workbook workbook = new XSSFWorkbook(); // Excel 2007+
// 或
Workbook workbook = new HSSFWorkbook(); // Excel 97-2003

// 方式2：从输入流创建
try (InputStream is = new FileInputStream(file)) {
    Workbook workbook = WorkbookFactory.create(is);
}

// 方式3：使用具体的工厂方法
if (filename.endsWith(".xlsx")) {
    workbook = new XSSFWorkbook();
} else {
    workbook = new HSSFWorkbook();
}
```

### 方案3：清理依赖冲突

#### 检查依赖冲突
```bash
# Maven项目
mvn dependency:tree | grep poi

# Gradle项目
./gradlew dependencies | grep poi
```

#### 排除冲突依赖
```xml
<!-- Maven: 排除传递依赖中的旧版本POI -->
<dependency>
    <groupId>some.other.library</groupId>
    <artifactId>library-name</artifactId>
    <version>1.0.0</version>
    <exclusions>
        <exclusion>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

## 🔧 完整的导出代码示例

```java
@RestController
@RequestMapping("/pasture/admin")
public class ExportController {
    
    @GetMapping("/order/export")
    public void exportOrders(
            @RequestParam String appCode,
            @RequestParam String startTime,
            @RequestParam String endTime,
            @RequestParam Integer type,
            HttpServletResponse response) {
        
        try {
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("订单数据");
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("订单ID");
            headerRow.createCell(1).setCellValue("用户ID");
            headerRow.createCell(2).setCellValue("金额");
            headerRow.createCell(3).setCellValue("创建时间");
            
            // 查询数据并填充
            List<Order> orders = orderService.getOrders(appCode, startTime, endTime, type);
            for (int i = 0; i < orders.size(); i++) {
                Row row = sheet.createRow(i + 1);
                Order order = orders.get(i);
                row.createCell(0).setCellValue(order.getId());
                row.createCell(1).setCellValue(order.getUserId());
                row.createCell(2).setCellValue(order.getAmount());
                row.createCell(3).setCellValue(order.getCreateTime());
            }
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=orders.xlsx");
            
            // 输出文件
            workbook.write(response.getOutputStream());
            workbook.close();
            
        } catch (Exception e) {
            log.error("导出订单数据失败", e);
            throw new RuntimeException("导出失败");
        }
    }
}
```

## 📋 验证步骤

1. **清理项目**：
   ```bash
   mvn clean  # 或 ./gradlew clean
   ```

2. **重新编译**：
   ```bash
   mvn compile  # 或 ./gradlew build
   ```

3. **检查依赖**：
   ```bash
   mvn dependency:tree | grep poi
   ```

4. **测试导出功能**：
   - 访问导出URL
   - 检查是否正常下载Excel文件

## 🎯 预期结果

修复后，导出接口应该：
- 正常生成Excel文件
- 浏览器自动下载文件
- 不再出现POI相关错误

## 📞 技术支持

如果问题仍然存在，请检查：
1. Java版本兼容性
2. 服务器内存是否足够
3. 是否有其他依赖冲突
4. 日志中的详细错误信息

---

**创建时间**: 2025-01-26  
**适用版本**: Apache POI 3.x - 5.x  
**状态**: 待后端实施
