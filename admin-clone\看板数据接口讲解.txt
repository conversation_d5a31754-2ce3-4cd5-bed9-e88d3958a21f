看板数据中的用户总数 新增用户数 活跃用户数 付费金额 付费人数 入账金额 入账笔数 出账金额 出账笔数  次日留存率
以上这些分页面的接口都是
/pasture/admin/spectaculars/dimensionDetail
传入的值是 appcode 都不需要传   但是不要忘了 请求到要带assesstoken
{
    "appCode": "",
    "endTime": "",
    "granularity": 0,
    "id": 0,
    "startTime": "",
    "timeInterval": 0,
    "type": 0
}
用户总数传值是{
    "appCode": "",
    "endTime": "",
    "granularity": 0,
    "id": 189,
    "startTime": "",
    "timeInterval": 0,
    "type": 1
}
新增用户数{
    "appCode": "",
    "endTime": "",
    "granularity": 0,
    "id": 190,
    "startTime": "",
    "timeInterval": 0,
    "type": 1
}
活跃用户数{
    "appCode": "",
    "endTime": "",
    "granularity": 0,
    "id": 191,
    "startTime": "",
    "timeInterval": 0,
    "type": 1
}
付费金额{
    "appCode": "",
    "endTime": "",
    "granularity": 0,
    "id": 192,
    "startTime": "",
    "timeInterval": 0,
    "type": 1
}
付费人数{
    "appCode": "",
    "endTime": "",
    "granularity": 0,
    "id": 193,
    "startTime": "",
    "timeInterval": 0,
    "type": 1
}
入账金额{
    "appCode": "",
    "endTime": "",
    "granularity": 0,
    "id": 194,
    "startTime": "",
    "timeInterval": 0,
    "type": 1
}
入账笔数{
    "appCode": "",
    "endTime": "",
    "granularity": 0,
    "id": 195,
    "startTime": "",
    "timeInterval": 0,
    "type": 1
}
入账笔数{
    "appCode": "",
    "endTime": "",
    "granularity": 0,
    "id": 196,
    "startTime": "",
    "timeInterval": 0,
    "type": 1
}
出账笔数{
    "appCode": "",
    "endTime": "",
    "granularity": 0,
    "id": 197,
    "startTime": "",
    "timeInterval": 0,
    "type": 1
}
次日存留率{
    "appCode": "",
    "endTime": "",
    "granularity": 0,
    "id": 198,
    "startTime": "",
    "timeInterval": 0,
    "type": 1
}