# 🚀 Admin Clone 项目部署指南

## 📦 打包文件说明

- **dist.zip**: 生产环境部署包，包含所有构建后的静态文件
- **dist/**: 构建输出目录，可直接部署到Web服务器

## 🌐 部署环境配置

### ⚠️ 重要：API地址配置

#### 开发环境（本地开发）
```bash
# 启动开发服务器
npm run dev
# 访问：http://localhost:5173
```
- **API模式**: 可选择代理模式或直接访问模式
- **线上API**: `https://api.zj7hui.com/pasture`
- **切换命令**: `npm run api:proxy` 或 `npm run api:direct`

#### 生产环境（服务器部署）
- **API地址**: `https://api.zj7hui.com/pasture`
- **访问方式**: 前端直接请求线上API
- **无需代理**: 生产环境直接访问，无跨域问题

## 📋 部署步骤

### 1. 解压部署包
```bash
# 解压dist.zip到Web服务器目录
unzip dist.zip -d /var/www/html/admin-clone/
```

### 2. Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html/admin-clone;
    index index.html;

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 不需要代理配置，前端直接请求后端
}
```

### 3. Apache配置示例
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/html/admin-clone
    
    # SPA路由支持
    <Directory "/var/www/html/admin-clone">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
</VirtualHost>
```

## 🔧 验证部署

### 1. 检查文件结构
```
admin-clone/
├── index.html          # 主页面
├── favicon.ico         # 网站图标
└── assets/            # 静态资源
    ├── *.js           # JavaScript文件
    ├── *.css          # 样式文件
    └── *.woff2        # 字体文件
```

### 2. 测试访问
1. 访问部署地址
2. 检查登录页面是否正常显示
3. 使用 admin/123456 登录
4. 验证数据看板功能

### 3. API连通性测试
```bash
# 测试线上API连接
curl -X POST https://api.zj7hui.com/pasture/admin/login/login \
  -H "Content-Type: application/json" \
  -d '{"account":"admin","pwd":"123456"}'
```

## ⚠️ 常见问题

### 1. 页面空白或404
- 检查Web服务器配置是否支持SPA路由
- 确认index.html文件存在且可访问

### 2. API请求失败
- 确认网络可以访问`https://api.zj7hui.com`
- 检查HTTPS证书是否有效
- 验证API服务是否正常运行

### 3. 登录失败
- 确认用户名密码：admin/123456
- 检查浏览器控制台网络请求
- 验证后端API响应

## 📝 项目信息

- **技术栈**: Vue 3 + TypeScript + Ant Design Vue
- **构建工具**: Vite
- **API地址**: https://api.zj7hui.com/pasture
- **认证方式**: Header中携带Accesstoken

## 🎯 快速部署命令

```bash
# 1. 上传并解压
scp dist.zip user@server:/tmp/
ssh user@server "cd /var/www/html && unzip /tmp/dist.zip"

# 2. 重启Web服务器
sudo systemctl restart nginx
# 或
sudo systemctl restart apache2

# 3. 测试访问
curl -I http://your-domain.com
```

## 📞 技术支持

如遇到部署问题，请检查：
1. Web服务器配置
2. 网络连通性
3. 后端服务状态
4. 浏览器控制台错误信息
