# 组件使用指南

## 🧩 公共组件

### AppSelector - 应用渠道选择器

统一的应用渠道选择组件，自动从API获取渠道列表。

**使用方式**:
```vue
<template>
  <AppSelector
    v-model="selectedApp"
    @change="handleAppChange"
    width="200px"
    placeholder="请选择应用渠道"
  />
</template>

<script setup>
import AppSelector from '@/components/AppSelector.vue'

const selectedApp = ref('')

const handleAppChange = (value) => {
  console.log('选择的应用:', value)
}
</script>
```

**Props**:
- `modelValue?: string` - 选中的值
- `placeholder?: string` - 占位符文本，默认"请选择应用渠道"
- `width?: string` - 组件宽度，默认"200px"
- `allowClear?: boolean` - 是否允许清空，默认true

**Events**:
- `update:modelValue` - 值变化事件
- `change` - 选择变化事件

**特点**:
- 自动从API获取应用列表
- 支持加载状态显示
- 提供备用默认数据
- 全局状态管理

## 🛠️ 工具函数

### 时间格式化工具 (dateFormat.ts)

#### formatToApiDate
将日期格式化为API需要的格式。

```typescript
import { formatToApiDate } from '@/utils/dateFormat'

// 格式化为 yyyyMMdd HHmmss
const apiDate = formatToApiDate(new Date())
// 输出: "20241225 143000"

// 只格式化日期部分
const apiDateOnly = formatToApiDate(new Date(), false)
// 输出: "20241225"
```

#### formatDateRange
处理日期范围格式化。

```typescript
import { formatDateRange } from '@/utils/dateFormat'

const { startTime, endTime } = formatDateRange(
  startDate,  // 开始日期
  endDate,    // 结束日期
  true,       // 开始时间设为00:00:00
  true        // 结束时间设为23:59:59
)
```

#### handleDateRangeChange
日期范围选择器变化处理。

```vue
<template>
  <a-range-picker
    v-model:value="dateRange"
    @change="handleDateChange"
  />
</template>

<script setup>
import { handleDateRangeChange } from '@/utils/dateFormat'

const dateRange = ref(null)

const handleDateChange = (dates) => {
  handleDateRangeChange(dates, (startTime, endTime) => {
    searchForm.startTime = startTime
    searchForm.endTime = endTime
  })
}
</script>
```

### 导出工具 (export.ts)

#### exportByUrl
通过URL拼接方式导出文件。

```typescript
import { exportByUrl } from '@/utils/export'

await exportByUrl({
  baseUrl: 'https://api.zj7hui.com',
  endpoint: '/pasture/admin/user/export',
  params: { appCode: 'pasture' },
  filename: 'users.xlsx'
})
```

#### 预定义导出函数

```typescript
import { 
  exportUserList, 
  exportOrderList, 
  exportRefundList,
  getExportParams 
} from '@/utils/export'

// 导出用户列表
const exportUsers = async () => {
  const params = getExportParams(searchForm)
  await exportUserList(params)
}

// 导出订单列表
const exportOrders = async () => {
  const params = getExportParams(searchForm)
  await exportOrderList(params)
}

// 导出退款列表
const exportRefunds = async () => {
  const params = getExportParams(searchForm)
  await exportRefundList(params)
}
```

## 🏪 状态管理 (Pinia Stores)

### AppStore - 应用渠道管理

```typescript
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

// 获取应用列表
await appStore.fetchAppList()

// 获取应用选项（用于下拉框）
const options = appStore.appOptions

// 根据code获取应用名称
const appName = appStore.getAppName('pasture')

// 初始化应用列表（如果未初始化）
await appStore.initAppList()
```

**状态**:
- `appList: AppItem[]` - 应用列表
- `loading: boolean` - 加载状态
- `initialized: boolean` - 是否已初始化

**计算属性**:
- `appOptions` - 下拉框选项格式
- `getAppName` - 根据code获取名称的函数

**方法**:
- `fetchAppList()` - 获取应用列表
- `initAppList()` - 初始化（如果未初始化）
- `setDefaultAppList()` - 设置默认列表
- `reset()` - 重置状态

## 📄 页面组件模式

### 标准列表页面结构

```vue
<template>
  <div class="page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>页面标题</h2>
      <div class="header-actions">
        <a-button type="primary" @click="exportData" :loading="exportLoading">
          <template #icon>
            <DownloadOutlined />
          </template>
          导出数据
        </a-button>
      </div>
    </div>

    <!-- 搜索表单 -->
    <a-card class="search-card" style="margin-bottom: 16px;">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="应用渠道">
          <AppSelector v-model="searchForm.appCode" width="150px" />
        </a-form-item>
        <!-- 其他搜索条件 -->
        <a-form-item>
          <a-button type="primary" @click="search" :loading="loading">
            搜索
          </a-button>
          <a-button @click="resetSearch" style="margin-left: 8px;">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 数据表格 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="dataList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <!-- 自定义列渲染 -->
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
// 标准导入
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { DownloadOutlined } from '@ant-design/icons-vue'
import AppSelector from '@/components/AppSelector.vue'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const dataList = ref([])

// 搜索表单
const searchForm = reactive({
  appCode: undefined,
  page: 1,
  size: 10
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 搜索数据
const search = async () => {
  // 实现搜索逻辑
}

// 重置搜索
const resetSearch = () => {
  // 重置表单
}

// 导出数据
const exportData = async () => {
  // 实现导出逻辑
}

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current || 1
  pagination.pageSize = pag.pageSize || 10
  search()
}

// 组件挂载
onMounted(() => {
  search()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.page-header h2 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}
</style>
```

## 🎨 样式规范

### CSS类命名
- 页面容器: `.page-container`
- 页面头部: `.page-header`
- 头部操作: `.header-actions`
- 搜索卡片: `.search-card`

### 响应式设计
```css
/* 移动端适配 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
```

## 🔧 开发最佳实践

### 1. 组件命名
- 使用PascalCase命名组件
- 文件名与组件名保持一致
- 组件名应该具有描述性

### 2. API调用
```typescript
// 推荐的API调用模式
const fetchData = async () => {
  loading.value = true
  try {
    const response = await apiFunction(params)
    if (response.data) {
      dataList.value = response.data.list || response.data
      // 处理分页
      if (response.data.total !== undefined) {
        pagination.total = response.data.total
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    message.error('获取数据失败')
  } finally {
    loading.value = false
  }
}
```

### 3. 错误处理
```typescript
// 统一错误处理
const handleError = (error: any, defaultMessage: string) => {
  console.error(defaultMessage, error)
  const errorMessage = error?.response?.data?.message || defaultMessage
  message.error(errorMessage)
}
```

### 4. 类型定义
```typescript
// 定义清晰的接口类型
interface SearchForm {
  appCode?: string
  keyword?: string
  startTime?: string
  endTime?: string
  page: number
  size: number
}

interface DataItem {
  id: number
  name: string
  status: number
  createTime: string
}
```

## 📚 参考资源

- [Vue 3 官方文档](https://vuejs.org/)
- [Ant Design Vue 文档](https://antdv.com/)
- [Pinia 状态管理](https://pinia.vuejs.org/)
- [TypeScript 手册](https://www.typescriptlang.org/docs/)
