import api from './index'

// 管理员信息接口
export interface Admin {
  companyId?: string
  createTime?: string
  createUserId?: string
  email?: string
  id?: number
  loginAccount?: string
  name?: string
  password?: string
  phoneNo?: string
  roleName?: string
  salt?: string
  status?: string
  token?: string
  updateTime?: string
  updateUserId?: string
}

// 管理员参数接口
export interface AdminParams {
  adminRoles?: any[]
  email?: string
  endTime?: string
  id?: number
  loginAccount?: string
  name?: string
  page?: number
  password?: string
  phoneNo?: string
  roleIds?: string
  roleName?: string
  salt?: string
  size?: number
  startTime?: string
  status?: string
  token?: string
}

// 账号注册参数
export interface RegisterParams {
  account: string
  pwd: string
  roleIds: string
}

// 重置密码参数
export interface ResetPasswordParams {
  adminId: number
  pwd: string
}

// 分页信息接口
export interface PageInfo<T> {
  endRow: number
  firstPage: number
  hasNextPage: boolean
  hasPreviousPage: boolean
  isFirstPage: boolean
  isLastPage: boolean
  lastPage: number
  list: T[]
  navigateFirstPage: number
  navigateLastPage: number
  navigatePages: number
  navigatepageNums: number[]
  nextPage: number
  orderBy: string
  pageNum: number
  pageSize: number
  pages: number
  prePage: number
  size: number
  startRow: number
  total: number
}

// 新增管理员
export const addAdmin = (params: RegisterParams) => {
  return api.post('/admin/admin/add', params)
}

// 删除管理员
export const deleteAdmin = (id: number) => {
  return api.post('/admin/admin/delete', null, { params: { id } })
}

// 获取管理员详情
export const getAdminDetail = (id: number) => {
  return api.post('/admin/admin/detail', null, { params: { id } })
}

// 获取管理员列表
export const getAdminList = (params: AdminParams) => {
  return api.post('/admin/admin/list', params)
}

// 重置密码
export const resetPassword = (params: ResetPasswordParams) => {
  return api.post('/admin/admin/restpwd', params)
}

// 状态改变：启用禁用
export const changeAdminStatus = (id: number) => {
  return api.post('/admin/admin/status', null, { params: { id } })
}

// 更新管理员
export const updateAdmin = (user: AdminParams) => {
  return api.post('/admin/admin/update', user)
}
