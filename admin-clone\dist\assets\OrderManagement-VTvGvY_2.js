import{d as e,f as a,r as l,o as t,b as d,a1 as o,a0 as u,k as r,_ as n,S as i,V as p,G as s,u as v,U as m,a2 as c,a3 as y,F as _}from"./vendor-Dhi-hotu.js";import{d as f,e as h,f as g}from"./antd-DSyIu0Jx.js";import{g as k,A as T,r as C}from"./AppSelector-CdXIhBuS.js";import{u as w}from"./app-C5BK_7TP.js";import{h as I}from"./dateFormat-DmnmjEpg.js";import{_ as b}from"./_plugin-vue_export-helper-xcdYWU4n.js";import"./auth-CDe9Kw3v.js";const x={class:"order-management"},D={class:"page-header"},Y={class:"header-actions"},U=b(e({__name:"OrderManagement",setup(e){const b=a(!1),U=a(!1),N=a(!1),z=a([]),M=a(null),j=a(!1),S=a(!1),V=a(null),O=a(null),A=l({appCode:void 0,orderNo:void 0,playerId:void 0,payType:void 0,status:void 0,startTime:void 0,endTime:void 0,page:1,size:10}),H=l({orderId:0,type:1,amount:void 0,refundDesc:"",name:"管理员"}),F=l({appCode:"",endTime:"",startTime:"",type:1}),L=l({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`}),q=[{title:"订单ID",dataIndex:"id",key:"id",width:80},{title:"订单编号",dataIndex:"flowNo",key:"flowNo",width:180},{title:"玩家ID",dataIndex:"playerId",key:"playerId",width:100},{title:"玩家名称",dataIndex:"userName",key:"userName",width:120},{title:"订单金额",dataIndex:"amount",key:"amount",width:100},{title:"支付方式",dataIndex:"payType",key:"payType",width:120},{title:"订单状态",dataIndex:"status",key:"status",width:120},{title:"应用渠道",dataIndex:"appCode",key:"appCode",width:100},{title:"创建时间",dataIndex:"createTime",key:"createTime",width:160},{title:"操作",key:"action",width:150,fixed:"right"}],R=e=>({1:"微信公众号",2:"小天才服务号",3:"线下支付",4:"小天才表端支付",5:"小天才H5支付",6:"华为支付"}[e]||"未知"),$=e=>({0:"待支付",1:"已支付",2:"支付失败",3:"用户已支付，待确认",4:"已退款",5:"部分退款"}[e]||"未知"),E=e=>{I(e,(e,a)=>{A.startTime=e,A.endTime=a})},G=async()=>{b.value=!0;try{const e={page:L.current,size:L.pageSize};A.appCode&&A.appCode.trim()&&(e.appCode=A.appCode),A.orderNo?.trim()&&(e.orderNo=A.orderNo.trim()),A.playerId&&(e.playerId=A.playerId),A.payType&&(e.payType=A.payType),void 0!==A.status&&(e.status=A.status),A.startTime&&(e.startTime=A.startTime),A.endTime&&(e.endTime=A.endTime);const a=await k(e);a.data&&(Array.isArray(a.data)?(z.value=a.data,L.total=a.data.length):a.data.list?(z.value=a.data.list||[],L.total=a.data.total||0):(z.value=[],L.total=0))}catch(e){console.error("获取订单列表失败:",e),f.error("获取订单列表失败")}finally{b.value=!1}},J=()=>{Object.assign(A,{appCode:void 0,orderNo:void 0,playerId:void 0,payType:void 0,status:void 0,startTime:void 0,endTime:void 0,page:1,size:10}),M.value=null,L.current=1,G()},P=e=>{L.current=e.current||1,L.pageSize=e.pageSize||10,G()},Q=async()=>{if(V.value){N.value=!0;try{const e={orderId:H.orderId,type:H.type,refundDesc:H.refundDesc,name:H.name};2===H.type&&H.amount&&(e.amount=H.amount),await C(e),f.success("退款申请提交成功"),j.value=!1,G()}catch(e){console.error("退款失败:",e),f.error("退款申请失败")}finally{N.value=!1}}},B=()=>{S.value=!0,F.appCode="",F.type=1,O.value=null},K=async()=>{try{if(!O.value||2!==O.value.length)return void f.error("请选择订单时间范围");U.value=!0;const e=O.value[0].format("YYYY-MM-DD")+" 00:00:00",a=O.value[1].format("YYYY-MM-DD")+" 23:59:59",l=`https://api.zj7hui.com/pasture/admin/order/export?${new URLSearchParams({appCode:F.appCode||"",startTime:e,endTime:a,type:F.type.toString()}).toString()}`;console.log("订单导出URL:",l);const t=document.createElement("a");t.href=l,t.target="_blank",document.body.appendChild(t),t.click(),document.body.removeChild(t),f.success("正在导出订单数据，请稍候..."),S.value=!1}catch(e){console.error("导出订单数据失败:",e),f.error("导出订单数据失败")}finally{U.value=!1}};return t(async()=>{const e=w();await e.initAppList(),A.appCode="",console.log("OrderManagement设置默认为所有应用"),G()}),d(()=>{z.value=[],L.total=0,b.value=!1,Object.assign(A,{appCode:void 0,orderNo:void 0,playerId:void 0,payType:void 0,status:void 0,startTime:void 0,endTime:void 0,page:1,size:10}),M.value=null,console.log("订单管理页面已清理")}),(e,a)=>{const l=i("a-button"),t=i("a-form-item"),d=i("a-input"),k=i("a-input-number"),C=i("a-select-option"),w=i("a-select"),I=i("a-range-picker"),W=i("a-form"),X=i("a-card"),Z=i("a-tag"),ee=i("a-space"),ae=i("a-table"),le=i("a-radio"),te=i("a-radio-group"),de=i("a-textarea"),oe=i("a-modal");return p(),o("div",x,[u("div",D,[a[17]||(a[17]=u("h2",null,"订单管理",-1)),u("div",Y,[r(l,{type:"primary",onClick:B,loading:U.value},{icon:n(()=>[r(v(h))]),default:n(()=>[a[16]||(a[16]=s(" 导出订单数据 ",-1))]),_:1,__:[16]},8,["loading"])])]),r(X,{class:"search-card",style:{"margin-bottom":"16px"}},{default:n(()=>[r(W,{layout:"inline",model:A},{default:n(()=>[r(t,{label:"应用渠道"},{default:n(()=>[r(T,{modelValue:A.appCode,"onUpdate:modelValue":a[0]||(a[0]=e=>A.appCode=e),width:"150px"},null,8,["modelValue"])]),_:1}),r(t,{label:"订单编号"},{default:n(()=>[r(d,{value:A.orderNo,"onUpdate:value":a[1]||(a[1]=e=>A.orderNo=e),placeholder:"请输入订单编号"},null,8,["value"])]),_:1}),r(t,{label:"玩家ID"},{default:n(()=>[r(k,{value:A.playerId,"onUpdate:value":a[2]||(a[2]=e=>A.playerId=e),placeholder:"请输入玩家ID",style:{width:"150px"}},null,8,["value"])]),_:1}),r(t,{label:"支付方式"},{default:n(()=>[r(w,{value:A.payType,"onUpdate:value":a[3]||(a[3]=e=>A.payType=e),placeholder:"请选择支付方式",style:{width:"150px"}},{default:n(()=>[r(C,{value:1},{default:n(()=>a[18]||(a[18]=[s("微信公众号",-1)])),_:1,__:[18]}),r(C,{value:2},{default:n(()=>a[19]||(a[19]=[s("小天才服务号",-1)])),_:1,__:[19]}),r(C,{value:3},{default:n(()=>a[20]||(a[20]=[s("线下支付",-1)])),_:1,__:[20]}),r(C,{value:4},{default:n(()=>a[21]||(a[21]=[s("小天才表端支付",-1)])),_:1,__:[21]}),r(C,{value:5},{default:n(()=>a[22]||(a[22]=[s("小天才H5支付",-1)])),_:1,__:[22]}),r(C,{value:6},{default:n(()=>a[23]||(a[23]=[s("华为支付",-1)])),_:1,__:[23]})]),_:1},8,["value"])]),_:1}),r(t,{label:"订单状态"},{default:n(()=>[r(w,{value:A.status,"onUpdate:value":a[4]||(a[4]=e=>A.status=e),placeholder:"请选择订单状态",style:{width:"150px"}},{default:n(()=>[r(C,{value:0},{default:n(()=>a[24]||(a[24]=[s("待支付",-1)])),_:1,__:[24]}),r(C,{value:1},{default:n(()=>a[25]||(a[25]=[s("已支付",-1)])),_:1,__:[25]}),r(C,{value:2},{default:n(()=>a[26]||(a[26]=[s("支付失败",-1)])),_:1,__:[26]}),r(C,{value:3},{default:n(()=>a[27]||(a[27]=[s("用户已支付，待确认",-1)])),_:1,__:[27]}),r(C,{value:4},{default:n(()=>a[28]||(a[28]=[s("已退款",-1)])),_:1,__:[28]}),r(C,{value:5},{default:n(()=>a[29]||(a[29]=[s("部分退款",-1)])),_:1,__:[29]})]),_:1},8,["value"])]),_:1}),r(t,{label:"创建时间"},{default:n(()=>[r(I,{value:M.value,"onUpdate:value":a[5]||(a[5]=e=>M.value=e),format:"YYYY-MM-DD",placeholder:"['开始时间', '结束时间']",style:{width:"240px"},onChange:E},null,8,["value"])]),_:1}),r(t,null,{default:n(()=>[r(l,{type:"primary",onClick:G,loading:b.value},{default:n(()=>a[30]||(a[30]=[s(" 搜索 ",-1)])),_:1,__:[30]},8,["loading"]),r(l,{style:{"margin-left":"8px"},onClick:J},{default:n(()=>a[31]||(a[31]=[s(" 重置 ",-1)])),_:1,__:[31]})]),_:1})]),_:1},8,["model"])]),_:1}),r(X,null,{default:n(()=>[r(ae,{columns:q,"data-source":z.value,loading:b.value,pagination:L,onChange:P,"row-key":"id"},{bodyCell:n(({column:e,record:t})=>{return["payType"===e.key?(p(),m(Z,{key:0,color:(i=t.payType,{1:"green",2:"blue",3:"orange",4:"purple",5:"cyan",6:"red"}[i]||"default")},{default:n(()=>[s(y(R(t.payType)),1)]),_:2},1032,["color"])):c("",!0),"status"===e.key?(p(),m(Z,{key:1,color:(u=t.status,{0:"orange",1:"green",2:"red",3:"blue",4:"purple",5:"cyan"}[u]||"default")},{default:n(()=>[s(y($(t.status)),1)]),_:2},1032,["color"])):c("",!0),"amount"===e.key?(p(),o(_,{key:2},[s(" ¥"+y(Number(t.amount).toFixed(2)),1)],64)):c("",!0),"createTime"===e.key?(p(),o(_,{key:3},[s(y((d=t.createTime,d?g(d).format("YYYY-MM-DD HH:mm:ss"):"-")),1)],64)):c("",!0),"action"===e.key?(p(),m(ee,{key:4},{default:n(()=>[r(l,{type:"link",size:"small",onClick:e=>{f.info("订单详情功能待实现")}},{default:n(()=>a[32]||(a[32]=[s(" 查看详情 ",-1)])),_:2,__:[32]},1032,["onClick"]),1===t.status?(p(),m(l,{key:0,type:"link",size:"small",danger:"",onClick:e=>{return a=t,V.value=a,H.orderId=a.id,H.type=1,H.amount=void 0,H.refundDesc="",void(j.value=!0);var a}},{default:n(()=>a[33]||(a[33]=[s(" 退款 ",-1)])),_:2,__:[33]},1032,["onClick"])):c("",!0)]),_:2},1024)):c("",!0)];var d,u,i}),_:1},8,["data-source","loading","pagination"])]),_:1}),r(oe,{open:j.value,"onUpdate:open":a[9]||(a[9]=e=>j.value=e),title:"订单退款",onOk:Q,onCancel:a[10]||(a[10]=e=>j.value=!1),"confirm-loading":N.value},{default:n(()=>[r(W,{model:H,layout:"vertical"},{default:n(()=>[r(t,{label:"退款类型"},{default:n(()=>[r(te,{value:H.type,"onUpdate:value":a[6]||(a[6]=e=>H.type=e)},{default:n(()=>[r(le,{value:1},{default:n(()=>a[34]||(a[34]=[s("全额退款",-1)])),_:1,__:[34]}),r(le,{value:2},{default:n(()=>a[35]||(a[35]=[s("部分退款",-1)])),_:1,__:[35]})]),_:1},8,["value"])]),_:1}),2===H.type?(p(),m(t,{key:0,label:"退款金额（元）"},{default:n(()=>[r(k,{value:H.amount,"onUpdate:value":a[7]||(a[7]=e=>H.amount=e),min:.01,max:V.value?.amount?Number(V.value.amount):0,precision:2,style:{width:"100%"}},null,8,["value","max"])]),_:1})):c("",!0),r(t,{label:"退款原因"},{default:n(()=>[r(de,{value:H.refundDesc,"onUpdate:value":a[8]||(a[8]=e=>H.refundDesc=e),placeholder:"请输入退款原因"},null,8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open","confirm-loading"]),r(oe,{open:S.value,"onUpdate:open":a[14]||(a[14]=e=>S.value=e),title:"数据导出",onOk:K,onCancel:a[15]||(a[15]=e=>S.value=!1),"confirm-loading":U.value,width:"500px"},{default:n(()=>[r(W,{model:F,layout:"vertical"},{default:n(()=>[r(t,{label:"应用渠道"},{default:n(()=>[r(T,{modelValue:F.appCode,"onUpdate:modelValue":a[11]||(a[11]=e=>F.appCode=e),width:"100%"},null,8,["modelValue"])]),_:1}),r(t,{label:"订单时间",required:""},{default:n(()=>[r(I,{value:O.value,"onUpdate:value":a[12]||(a[12]=e=>O.value=e),format:"YYYY-MM-DD",style:{width:"100%"},placeholder:"['开始日期', '结束日期']"},null,8,["value"])]),_:1}),r(t,{label:"订单类型",required:""},{default:n(()=>[r(te,{value:F.type,"onUpdate:value":a[13]||(a[13]=e=>F.type=e)},{default:n(()=>[r(le,{value:1},{default:n(()=>a[36]||(a[36]=[s("所有订单",-1)])),_:1,__:[36]}),r(le,{value:2},{default:n(()=>a[37]||(a[37]=[s("入账订单",-1)])),_:1,__:[37]})]),_:1},8,["value"])]),_:1})]),_:1},8,["model"])]),_:1},8,["open","confirm-loading"])])}}}),[["__scopeId","data-v-488d820f"]]);export{U as default};
