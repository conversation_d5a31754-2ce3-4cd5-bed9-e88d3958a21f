装备详情

**接口地址** `/pasture/player/equipage/detail`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| equipageId         |      equipageId   |     query        |       true      | integer   |      |
            | playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«装备»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    装备   |   装备    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | charm         |     魅力值      |  int32   |      |
            | differenceAttrs         |     属性值差值      |  array   | 属性     |
            | differenceScore         |     评分差值      |  int32   |      |
            | effect         |     作用      |  string   |      |
            | icon         |     icon      |  string   |      |
            | id         |     id      |  int64   |      |
            | level         |     等级      |  int32   |      |
            | link         |     link获取链接1副本界面 2会员界面 3扭蛋界面 4签到界面 5智慧树 6购买获取  再根据pay_type类型展示 7升级界面 8宠物界面9学习10装备11段位赛12巡游13武道会      |  string   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | nextAttrs         |     下一级属性值      |  array   | 属性     |
            | nextLevel         |     下一等级  当等级=-1时， 无下一级      |  int32   |      |
            | packs         |     下一级升级所需      |  array   | 背包道具     |
            | payType         |     payType类型 1金币 2vip 3svip      |  int32   |      |
            | price         |     price价格      |  int32   |      |
            | redStatus         |     红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态 0未用于  1已拥有 2已装备      |  int32   |      |
            | title         |     title标题      |  string   |      |
            | type         |     类型 1天空 2草地 3房子 4装饰1 5装饰2      |  int32   |      |
            | unlockLevel         |     unlockLevel解锁等级      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**背包道具**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | bizType         |     类型 1宠物材料 2宠物蛋 3宠物道具 4人物升级材料 5互动道具 6外观碎片 7抽奖道具      |  int32   |      |
            | effect         |     effect作用      |  string   |      |
            | hatchStatus         |     状态 1可以孵化 2不可以      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | link         |     link获取链接      |  string   |      |
            | memo         |     memo备注      |  string   |      |
            | name         |     name名称      |  string   |      |
            | num         |     拥有个数      |  int32   |      |
            | packId         |     packId      |  int64   |      |
            | playerPackId         |     playerPackId      |  int64   |      |
            | price         |     price价值      |  int32   |      |
            | redStatus         |     红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | sellLevel         |     售卖等级 （小于玩家等级 可以售卖）      |  int32   |      |
            | source         |     source获取来源      |  string   |      |
            | status         |     status状态 0未用于  1已拥有 2已装备      |  int32   |      |
            | type         |     type类型 1宠物蛋 2材料      |  int32   |      |
            | useNum         |     使用消耗个数      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "attrs": [
            {
                "attrType": 0,
                "attrValue": 0,
                "type": 0
            }
        ],
        "charm": 0,
        "differenceAttrs": [
            {
                "attrType": 0,
                "attrValue": 0,
                "type": 0
            }
        ],
        "differenceScore": 0,
        "effect": "",
        "icon": "",
        "id": 0,
        "level": 0,
        "link": "",
        "model": "",
        "modelSpine": "",
        "name": "",
        "nextAttrs": [
            {
                "attrType": 0,
                "attrValue": 0,
                "type": 0
            }
        ],
        "nextLevel": 0,
        "packs": [
            {
                "attrs": [
                    {
                        "attrType": 0,
                        "attrValue": 0,
                        "type": 0
                    }
                ],
                "bizType": 0,
                "effect": "",
                "hatchStatus": 0,
                "icon": "",
                "link": "",
                "memo": "",
                "name": "",
                "num": 0,
                "packId": 0,
                "playerPackId": 0,
                "price": 0,
                "redStatus": 0,
                "score": 0,
                "sellLevel": 0,
                "source": "",
                "status": 0,
                "type": 0,
                "useNum": 0
            }
        ],
        "payType": 0,
        "price": 0,
        "redStatus": 0,
        "score": 0,
        "seq": 0,
        "status": 0,
        "title": "",
        "type": 0,
        "unlockLevel": 0
    },
    "message": "",
    "msg": ""
}
```



-----------------分割线
上阵

**接口地址** `/pasture/player/equipage/fit`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| equipageId         |      equipageId   |     query        |       true      | integer   |      |
            | playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«int»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    int32   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": 0,
    "message": "",
    "msg": ""
}
```



-----------------分割线
获取装备列表 type 类型 1神兵 2宝马 3皮肤 4兵书 5虎符

**接口地址** `/pasture/player/equipage/list`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            | type         |      type   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«装备»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   装备    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | charm         |     魅力值      |  int32   |      |
            | differenceAttrs         |     属性值差值      |  array   | 属性     |
            | differenceScore         |     评分差值      |  int32   |      |
            | effect         |     作用      |  string   |      |
            | icon         |     icon      |  string   |      |
            | id         |     id      |  int64   |      |
            | level         |     等级      |  int32   |      |
            | link         |     link获取链接1副本界面 2会员界面 3扭蛋界面 4签到界面 5智慧树 6购买获取  再根据pay_type类型展示 7升级界面 8宠物界面9学习10装备11段位赛12巡游13武道会      |  string   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | nextAttrs         |     下一级属性值      |  array   | 属性     |
            | nextLevel         |     下一等级  当等级=-1时， 无下一级      |  int32   |      |
            | packs         |     下一级升级所需      |  array   | 背包道具     |
            | payType         |     payType类型 1金币 2vip 3svip      |  int32   |      |
            | price         |     price价格      |  int32   |      |
            | redStatus         |     红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态 0未用于  1已拥有 2已装备      |  int32   |      |
            | title         |     title标题      |  string   |      |
            | type         |     类型 1天空 2草地 3房子 4装饰1 5装饰2      |  int32   |      |
            | unlockLevel         |     unlockLevel解锁等级      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**背包道具**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | bizType         |     类型 1宠物材料 2宠物蛋 3宠物道具 4人物升级材料 5互动道具 6外观碎片 7抽奖道具      |  int32   |      |
            | effect         |     effect作用      |  string   |      |
            | hatchStatus         |     状态 1可以孵化 2不可以      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | link         |     link获取链接      |  string   |      |
            | memo         |     memo备注      |  string   |      |
            | name         |     name名称      |  string   |      |
            | num         |     拥有个数      |  int32   |      |
            | packId         |     packId      |  int64   |      |
            | playerPackId         |     playerPackId      |  int64   |      |
            | price         |     price价值      |  int32   |      |
            | redStatus         |     红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | sellLevel         |     售卖等级 （小于玩家等级 可以售卖）      |  int32   |      |
            | source         |     source获取来源      |  string   |      |
            | status         |     status状态 0未用于  1已拥有 2已装备      |  int32   |      |
            | type         |     type类型 1宠物蛋 2材料      |  int32   |      |
            | useNum         |     使用消耗个数      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "attrs": [
                {
                    "attrType": 0,
                    "attrValue": 0,
                    "type": 0
                }
            ],
            "charm": 0,
            "differenceAttrs": [
                {
                    "attrType": 0,
                    "attrValue": 0,
                    "type": 0
                }
            ],
            "differenceScore": 0,
            "effect": "",
            "icon": "",
            "id": 0,
            "level": 0,
            "link": "",
            "model": "",
            "modelSpine": "",
            "name": "",
            "nextAttrs": [
                {
                    "attrType": 0,
                    "attrValue": 0,
                    "type": 0
                }
            ],
            "nextLevel": 0,
            "packs": [
                {
                    "attrs": [
                        {
                            "attrType": 0,
                            "attrValue": 0,
                            "type": 0
                        }
                    ],
                    "bizType": 0,
                    "effect": "",
                    "hatchStatus": 0,
                    "icon": "",
                    "link": "",
                    "memo": "",
                    "name": "",
                    "num": 0,
                    "packId": 0,
                    "playerPackId": 0,
                    "price": 0,
                    "redStatus": 0,
                    "score": 0,
                    "sellLevel": 0,
                    "source": "",
                    "status": 0,
                    "type": 0,
                    "useNum": 0
                }
            ],
            "payType": 0,
            "price": 0,
            "redStatus": 0,
            "score": 0,
            "seq": 0,
            "status": 0,
            "title": "",
            "type": 0,
            "unlockLevel": 0
        }
    ],
    "message": "",
    "msg": ""
}
```



-----------------分割线
解锁

**接口地址** `/pasture/player/equipage/unlock`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| equipageId         |      equipageId   |     query        |       true      | integer   |      |
            | playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«消耗操作结果信息«装备»»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    消耗操作结果信息«装备»   |   消耗操作结果信息«装备»    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**消耗操作结果信息«装备»**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| bizData         |     业务相关数据      |  装备   | 装备     |
            | coins         |     能量币数值变化      |  array   | AwardCoinDTO     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | remind         |     提醒内容      |  string   |      |
            | status         |     1-成功 2-代币不足 3-会员级别不足 4当日次数已用完 5积分不足 6事件已存在无效操作 7道具不足       |  int32   |      |
            

**装备**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | charm         |     魅力值      |  int32   |      |
            | differenceAttrs         |     属性值差值      |  array   | 属性     |
            | differenceScore         |     评分差值      |  int32   |      |
            | effect         |     作用      |  string   |      |
            | icon         |     icon      |  string   |      |
            | id         |     id      |  int64   |      |
            | level         |     等级      |  int32   |      |
            | link         |     link获取链接1副本界面 2会员界面 3扭蛋界面 4签到界面 5智慧树 6购买获取  再根据pay_type类型展示 7升级界面 8宠物界面9学习10装备11段位赛12巡游13武道会      |  string   |      |
            | model         |     model      |  string   |      |
            | modelSpine         |     动画      |  string   |      |
            | name         |     name      |  string   |      |
            | nextAttrs         |     下一级属性值      |  array   | 属性     |
            | nextLevel         |     下一等级  当等级=-1时， 无下一级      |  int32   |      |
            | packs         |     下一级升级所需      |  array   | 背包道具     |
            | payType         |     payType类型 1金币 2vip 3svip      |  int32   |      |
            | price         |     price价格      |  int32   |      |
            | redStatus         |     红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | seq         |     seq排序      |  int32   |      |
            | status         |     status状态 0未用于  1已拥有 2已装备      |  int32   |      |
            | title         |     title标题      |  string   |      |
            | type         |     类型 1天空 2草地 3房子 4装饰1 5装饰2      |  int32   |      |
            | unlockLevel         |     unlockLevel解锁等级      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**背包道具**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | bizType         |     类型 1宠物材料 2宠物蛋 3宠物道具 4人物升级材料 5互动道具 6外观碎片 7抽奖道具      |  int32   |      |
            | effect         |     effect作用      |  string   |      |
            | hatchStatus         |     状态 1可以孵化 2不可以      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | link         |     link获取链接      |  string   |      |
            | memo         |     memo备注      |  string   |      |
            | name         |     name名称      |  string   |      |
            | num         |     拥有个数      |  int32   |      |
            | packId         |     packId      |  int64   |      |
            | playerPackId         |     playerPackId      |  int64   |      |
            | price         |     price价值      |  int32   |      |
            | redStatus         |     红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | sellLevel         |     售卖等级 （小于玩家等级 可以售卖）      |  int32   |      |
            | source         |     source获取来源      |  string   |      |
            | status         |     status状态 0未用于  1已拥有 2已装备      |  int32   |      |
            | type         |     type类型 1宠物蛋 2材料      |  int32   |      |
            | useNum         |     使用消耗个数      |  int32   |      |
            

**AwardCoinDTO**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| name         |           |  string   |      |
            | num         |           |  int32   |      |
            | type         |           |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "bizData": {
            "attrs": [
                {
                    "attrType": 0,
                    "attrValue": 0,
                    "type": 0
                }
            ],
            "charm": 0,
            "differenceAttrs": [
                {
                    "attrType": 0,
                    "attrValue": 0,
                    "type": 0
                }
            ],
            "differenceScore": 0,
            "effect": "",
            "icon": "",
            "id": 0,
            "level": 0,
            "link": "",
            "model": "",
            "modelSpine": "",
            "name": "",
            "nextAttrs": [
                {
                    "attrType": 0,
                    "attrValue": 0,
                    "type": 0
                }
            ],
            "nextLevel": 0,
            "packs": [
                {
                    "attrs": [
                        {
                            "attrType": 0,
                            "attrValue": 0,
                            "type": 0
                        }
                    ],
                    "bizType": 0,
                    "effect": "",
                    "hatchStatus": 0,
                    "icon": "",
                    "link": "",
                    "memo": "",
                    "name": "",
                    "num": 0,
                    "packId": 0,
                    "playerPackId": 0,
                    "price": 0,
                    "redStatus": 0,
                    "score": 0,
                    "sellLevel": 0,
                    "source": "",
                    "status": 0,
                    "type": 0,
                    "useNum": 0
                }
            ],
            "payType": 0,
            "price": 0,
            "redStatus": 0,
            "score": 0,
            "seq": 0,
            "status": 0,
            "title": "",
            "type": 0,
            "unlockLevel": 0
        },
        "coins": [
            {
                "name": "",
                "num": 0,
                "type": 0
            }
        ],
        "isVip": 0,
        "remind": "",
        "status": 0
    },
    "message": "",
    "msg": ""
}
```



-----------------分割线
升级

**接口地址** `/pasture/player/equipage/upgrade`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| equipageId         |      equipageId   |     query        |       true      | integer   |      |
            | playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«消耗操作结果信息»                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |
| 404         | Not Found                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    消耗操作结果信息   |   消耗操作结果信息    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**消耗操作结果信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| bizData         |     业务相关数据      |  object   |      |
            | coins         |     能量币数值变化      |  array   | AwardCoinDTO     |
            | isVip         |     是否是会员玩家 1-会员 0-不是会员      |  int32   |      |
            | remind         |     提醒内容      |  string   |      |
            | status         |     1-成功 2-代币不足 3-会员级别不足 4当日次数已用完 5积分不足 6事件已存在无效操作 7道具不足       |  int32   |      |
            

**AwardCoinDTO**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| name         |           |  string   |      |
            | num         |           |  int32   |      |
            | type         |           |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": {
        "bizData": {},
        "coins": [
            {
                "name": "",
                "num": 0,
                "type": 0
            }
        ],
        "isVip": 0,
        "remind": "",
        "status": 0
    },
    "message": "",
    "msg": ""
}
```


