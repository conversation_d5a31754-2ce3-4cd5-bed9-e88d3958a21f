import api from './index'

// 订单查询参数接口
export interface OrderQueryParams {
  appCode?: string
  endTime?: string
  name?: string
  orderNo?: string
  page?: number
  payType?: number // 1微信公众号 2小天才服务号 3线下支付 4小天才表端支付 5小天才H5支付 6华为支付
  playerId?: number
  size?: number
  startTime?: string
  status?: number // 0待支付 1已支付 2支付失败 3用户已支付，待确认 4已退款 5部分退款
}

// 订单信息接口
export interface Order {
  id?: number
  orderNo?: string
  flowNo?: string // 订单号（实际字段）
  playerId?: number
  playerName?: string
  userName?: string // 玩家名称（实际字段）
  amount?: number
  payType?: number
  status?: number
  createTime?: string
  updateTime?: string
  appCode?: string
}

// 退款参数接口
export interface RefundParams {
  amount?: number // 退款金额 单位元
  coin?: number // 游戏币扣除
  days?: number // 退回天数
  goodsIds?: string // 商品ids 使用，分割
  name?: string // 操作人名称
  orderId: number // 订单id
  refundDesc?: string // 退款描述(显示给用户的话)
  type: number // 类型 1全款 2部分
}

// 退款查询参数接口
export interface RefundQueryParams {
  appCode?: string
  createName?: string
  endTime?: string
  flowNo?: string
  page?: number
  payType?: number
  refundSn?: string
  size?: number
  startTime?: string
}

// 退款信息接口
export interface Refund {
  id?: number
  amount?: number
  appCode?: string
  createName?: string
  createTime?: string
  flowNo?: string
  payType?: number
  refundDesc?: string
  refundSn?: string
  refundStatus?: number // 0失败 1发起成功 2实际到账
  returnDays?: number
  successTime?: string
  type?: number // 1全部退款 2部分退款
  updateTime?: string
  vipEndTime?: string
}

// 获取订单列表
export const getOrderList = (params: OrderQueryParams) => {
  return api.post('/admin/order/list', params)
}

// 订单人工退款
export const refundOrder = (params: RefundParams) => {
  return api.post('/admin/order/manualRefund', params)
}

// 获取退款列表
export const getRefundList = (params: RefundQueryParams) => {
  return api.post('/order/refund/pageList', params)
}

// 通过订单号获取退款列表
export const getRefundByOrderNo = (orderNo: string) => {
  return api.get('/order/refund/list', { params: { orderNo } })
}

// 导出订单参数接口
export interface ExportOrderParams {
  appCode: string
  endTime: string
  startTime: string
  type: number // 1所有订单 2入账订单
}

// 导出订单
export const exportOrders = (params: ExportOrderParams) => {
  return api.get('/admin/order/export', {
    params,
    responseType: 'blob' // 用于下载文件
  })
}

// 导出退款订单参数接口
export interface ExportRefundParams {
  appCode?: string
  endTime: string
  startTime: string
}

// 导出退款订单
export const exportRefunds = (params: ExportRefundParams) => {
  return api.get('/order/refund/export', {
    params,
    responseType: 'blob' // 用于下载文件
  })
}
