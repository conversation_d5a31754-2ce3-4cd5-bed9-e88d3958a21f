# Admin Clone 项目

这是一个基于 Vue 3 + TypeScript + Ant Design Vue 的管理后台项目，主要用于数据看板展示和应用管理。

## 🚀 技术栈

- **前端框架**: Vue 3 (Composition API)
- **开发语言**: TypeScript
- **UI 组件库**: Ant Design Vue 4.x
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **构建工具**: Vite
- **HTTP 客户端**: Axios
- **日期处理**: Day.js
- **Excel 导出**: xlsx

## 📁 项目结构

```
admin-clone/
├── src/
│   ├── api/                    # API 接口定义
│   │   ├── auth.ts            # 认证相关接口
│   │   ├── dashboard.ts       # 看板数据接口
│   │   ├── apps.ts           # 应用管理接口
│   │   └── request.ts        # Axios 配置和拦截器
│   ├── assets/                # 静态资源
│   ├── components/            # 公共组件
│   │   └── Layout.vue        # 主布局组件
│   ├── router/                # 路由配置
│   │   └── index.ts          # 路由定义和守卫
│   ├── stores/                # Pinia 状态管理
│   │   └── auth.ts           # 认证状态管理
│   ├── utils/                 # 工具函数
│   │   ├── auth.ts           # 认证工具
│   │   └── date.ts           # 日期处理工具
│   ├── views/                 # 页面组件
│   │   ├── Login.vue         # 登录页面
│   │   ├── StatisticsBoard.vue # 数据看板页面
│   │   └── AppList.vue       # 应用列表页面
│   ├── App.vue               # 根组件
│   └── main.ts               # 入口文件
├── public/                    # 公共静态资源
├── scripts/                   # 构建脚本
│   └── switch-api.js         # API 环境切换脚本
├── dist/                      # 构建输出目录
├── 看板数据接口讲解.txt        # 接口文档
└── 各种文档.md               # 项目文档
```

## 🛠️ 开发环境设置

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```
访问：`http://localhost:5173`

### 构建生产版本

```bash
npm run build
```

### 类型检查

```bash
npm run type-check
```

## 🌐 API 接口配置

### ⚠️ 重要：代理配置说明

#### 开发环境（本地开发）
- **API模式**: 支持代理模式和直接访问模式
- **线上API**: `https://api.zj7hui.com/pasture`
- **配置文件**: `vite.config.ts` 和 `.env.development`
- **切换命令**: `npm run api:proxy` 或 `npm run api:direct`

#### 生产环境（服务器部署）
- **API地址**: `https://api.zj7hui.com/pasture`
- **访问方式**: 前端直接请求线上API
- **无需代理**: 生产环境直接访问，无跨域问题

### 主要接口列表

#### 1. 用户认证
```typescript
// 登录接口
POST /pasture/admin/login
Body: {
  "username": "admin",
  "password": "123456"
}
Response: {
  "code": "200",
  "data": {
    "accesstoken": "token_value"
  }
}
```

#### 2. 数据看板接口
```typescript
// 获取维度数据
POST /pasture/admin/spectaculars/dimensionDetail
Headers: {
  "accesstoken": "token_value"
}
Body: {
  "appCode": "",
  "endTime": "2025-07-26",
  "granularity": 0,
  "id": 189,  // 维度ID，见下方说明
  "startTime": "2025-07-20",
  "timeInterval": 0,
  "type": 1
}
```

#### 3. 维度ID对照表
- **用户总数**: id: 189
- **新增用户数**: id: 190
- **活跃用户数**: id: 191
- **付费金额**: id: 192
- **付费人数**: id: 193
- **入账金额**: id: 194
- **入账笔数**: id: 195
- **出账金额**: id: 196
- **出账笔数**: id: 197
- **次日留存率**: id: 198

#### 4. 应用管理
```typescript
// 获取应用列表
GET /pasture/admin/apps
Headers: {
  "accesstoken": "token_value"
}
```

## 🎯 功能特性

### 1. 用户认证系统
- ✅ 登录/登出功能
- ✅ Token 自动管理
- ✅ 路由守卫保护
- ✅ 登录状态持久化

### 2. 数据看板
- ✅ 多维度数据展示（用户、金额、笔数、留存率）
- ✅ 动态表头（根据数据类型显示不同表头）
- ✅ 日期范围筛选
- ✅ 数据导出功能（Excel格式）
- ✅ 只显示汇总数据的详细记录
- ✅ 紧凑型表格布局
- ✅ 分页支持

### 3. 应用管理
- ✅ 应用列表查看
- ✅ 应用详情展示

## 📊 数据看板详细说明

### 数据结构
API返回的数据包含多个sections，我们只使用name包含"汇总"的section中的statistics数组：

```json
{
  "data": {
    "sections": [
      {
        "name": "神奇牧场-付费金额",
        "statistics": [...],
        "total": 57.00000
      },
      {
        "name": "付费金额汇总",  // 我们使用这个
        "statistics": [
          {
            "cutDay": "2025-07-25",
            "statisticsValue": 57.00000,
            "proportion": 1.0000
          }
        ],
        "total": 57.00000
      }
    ]
  }
}
```

### 表头动态配置
- **用户相关**（用户总数、新增用户数、活跃用户数、付费人数）→ `日期 | 人数`
- **金额相关**（付费金额、入账金额、出账金额）→ `日期 | 金额`
- **笔数相关**（入账笔数、出账笔数）→ `日期 | 笔数`
- **留存率相关**（次日留存率）→ `日期 | 留存率`

## 🚀 部署说明

### 开发环境部署
1. 克隆项目：`git clone [repository]`
2. 安装依赖：`npm install`
3. 启动开发服务器：`npm run dev`
4. 访问：`http://localhost:5173`
5. **重要**: 开发环境会自动使用代理访问 `http://*************:8080`

### 生产环境部署
1. 构建项目：`npm run build`
2. 将 `dist` 目录上传到 Web 服务器
3. 配置 Web 服务器（Nginx/Apache）
4. **重要**: 确保服务器网络可以直接访问后端 API `http://*************:8080`
5. **不需要配置代理**，前端会直接请求后端

### Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    # 不需要代理配置，前端直接请求后端
}
```

## ⚠️ 重要注意事项

### 网络配置
1. **开发环境**: 必须配置代理，已在 `vite.config.ts` 中配置
2. **生产环境**: 不需要代理，前端直接请求后端
3. **后端地址**: `http://*************:8080`

### 认证要求
- 所有 API 请求都需要在 Header 中携带 `accesstoken`
- Token 通过登录接口获取
- Token 会自动存储在 localStorage 中

### 数据格式
- 日期格式：`YYYY-MM-DD`
- 数值显示：自动添加千分位分隔符
- 导出格式：Excel (.xlsx)

## 🔧 故障排除

### 常见问题

1. **开发环境API请求失败**
   - 检查后端服务是否启动
   - 确认代理配置是否正确
   - 查看浏览器控制台错误信息

2. **生产环境API请求失败**
   - 确认服务器网络可以访问后端
   - 检查防火墙设置
   - 确认后端服务正常运行

3. **登录失败**
   - 确认用户名密码正确（admin/123456）
   - 检查网络连接
   - 查看API响应状态

## 📝 更新日志

- **v1.0.0**: 初始版本，包含基础功能
- **v1.1.0**: 优化数据看板，添加动态表头
- **v1.2.0**: 优化表格布局，支持紧凑显示
- **v1.3.0**: 完善文档，添加详细部署说明
- **v1.4.0**: 简化用户界面，移除通知功能和多余菜单项

## 🎯 快速上手指南

### 新Agent快速了解项目

1. **项目概述**: Vue3 + TypeScript 管理后台，主要功能是数据看板
2. **核心页面**: 登录页、数据看板页、应用列表页
3. **关键配置**:
   - 开发环境需要代理到 `http://*************:8080`
   - 生产环境直接请求后端，无需代理
4. **认证方式**: Header中携带accesstoken
5. **数据看板**: 显示汇总数据的详细记录，支持多维度切换

### 常用命令
```bash
# 开发
npm run dev

# 构建
npm run build

# 类型检查
npm run type-check
```

## 🚀 部署说明

### 开发环境部署
1. 克隆项目：`git clone [repository]`
2. 安装依赖：`npm install`
3. 启动开发服务器：`npm run dev`
4. 访问：`http://localhost:5173`
5. **重要**: 开发环境会自动使用代理访问 `http://*************:8080`

### 生产环境部署
1. 构建项目：`npm run build`
2. 将 `dist` 目录上传到 Web 服务器
3. 配置 Web 服务器（Nginx/Apache）
4. **API访问**: 前端直接访问 `https://api.zj7hui.com/pasture`
5. **不需要配置代理**，前端会直接请求线上API

### Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    # 不需要代理配置，前端直接请求后端
}
```

## ⚠️ 重要注意事项

### API配置
1. **开发环境**: 支持代理模式和直接访问模式，可通过命令切换
2. **生产环境**: 直接访问线上API，无需代理
3. **API地址**: `https://api.zj7hui.com/pasture`

### 认证要求
- 所有 API 请求都需要在 Header 中携带 `accesstoken`
- Token 通过登录接口获取
- Token 会自动存储在 localStorage 中

### 数据格式
- 日期格式：`YYYY-MM-DD`
- 数值显示：自动添加千分位分隔符
- 导出格式：Excel (.xlsx)

## 🔧 故障排除

### 常见问题

1. **开发环境API请求失败**
   - 检查后端服务是否启动
   - 确认代理配置是否正确
   - 查看浏览器控制台错误信息

2. **生产环境API请求失败**
   - 确认服务器网络可以访问后端
   - 检查防火墙设置
   - 确认后端服务正常运行

3. **登录失败**
   - 确认用户名密码正确（admin/123456）
   - 检查网络连接
   - 查看API响应状态

## 📝 更新日志

- **v1.0.0**: 初始版本，包含基础功能
- **v1.1.0**: 优化数据看板，添加动态表头
- **v1.2.0**: 优化表格布局，支持紧凑显示
- **v1.3.0**: 完善文档，添加详细部署说明
