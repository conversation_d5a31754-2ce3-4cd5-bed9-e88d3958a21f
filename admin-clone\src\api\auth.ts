import api from './index'

// 登录参数接口
export interface LoginParams {
  account: string
  pwd: string
}

// 登录响应接口
export interface LoginResponse {
  code: string
  data: any
  message: string
  msg: string
}

// 菜单权限接口
export interface PermissionBo {
  children?: PermissionBo[]
  composingKey?: string
  createTime?: string
  createUserId?: string
  hasRelevance?: string
  id?: number
  isAction?: string
  label?: string
  logo?: string
  name?: string
  parentId?: string
  seq?: number
  type?: string
  updateTime?: string
  updateUserId?: string
  url?: string
}

// 登录API - 根据您提供的参数格式
export const login = (params: LoginParams) => {
  const requestData = {
    account: params.account,
    pwd: params.pwd
  }
  console.log('发送登录请求:', requestData)
  return api.post('/admin/login/login', requestData)
}

// 获取应用列表
export const getAppList = () => {
  return api.post('/admin/login/appList')
}

// 获取用户菜单
export const getUserMenu = () => {
  return api.get('/admin/login/queryMenu')
}
