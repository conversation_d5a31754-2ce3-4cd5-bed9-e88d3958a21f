import{a,_ as e}from"./_plugin-vue_export-helper-xcdYWU4n.js";import{d as l,f as o,w as s,o as t,S as p,a1 as d,V as u,k as n,a2 as i,u as r,a6 as m,_ as v,G as c,F as f,a4 as h,U as g,a3 as _,a0 as w}from"./vendor-Dhi-hotu.js";import{u as y}from"./app-C5BK_7TP.js";const V=e=>a.post("/admin/order/list",e),x=e=>a.post("/admin/order/manualRefund",e),C=e=>a.post("/order/refund/pageList",e),O={key:0,style:{"margin-top":"8px","font-size":"12px",color:"#666"}},b=e(l({__name:"AppSelector",props:{modelValue:{},placeholder:{default:"请选择应用渠道"},width:{default:"200px"},allowClear:{type:Boolean,default:!0},showDebug:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(a,{emit:e}){const l=a,V=e,x=y(),C=o(l.modelValue);s(()=>l.modelValue,a=>{C.value=a},{immediate:!0}),s(C,a=>{V("update:modelValue",a)});const b=a=>{V("change",a)};return t(async()=>{await x.initAppList()}),(a,e)=>{const l=p("a-select-option"),o=p("a-select");return u(),d("div",null,[n(o,{value:C.value,"onUpdate:value":e[0]||(e[0]=a=>C.value=a),placeholder:a.placeholder,style:m({width:a.width}),allowClear:a.allowClear,loading:r(x).loading,onChange:b,"show-search":"","filter-option":!1},{default:v(()=>[n(l,{value:""},{default:v(()=>e[1]||(e[1]=[c("所有",-1)])),_:1,__:[1]}),(u(!0),d(f,null,h(r(x).appOptions,a=>(u(),g(l,{key:a.value,value:a.value},{default:v(()=>[c(_(a.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["value","placeholder","style","allowClear","loading"]),a.showDebug?(u(),d("div",O,[w("div",null,"当前选中值: "+_(C.value),1),w("div",null,"应用选项数量: "+_(r(x).appOptions.length),1),w("div",null,"应用列表: "+_(JSON.stringify(r(x).appOptions)),1)])):i("",!0)])}}}),[["__scopeId","data-v-141daf3c"]]);export{b as A,C as a,V as g,x as r};
