收取奖励

**接口地址** `/pasture/player/hold/award`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerHoldId         |      playerHoldId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------分割线
开始打工

**接口地址** `/pasture/player/hold/hold`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| holdId         |      holdId   |     query        |       true      | integer   |      |
            | playerPetId         |      playerPetId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------分割线
离开

**接口地址** `/pasture/player/hold/leave`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerHoldId         |      playerHoldId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------分割线
岗位列表

**接口地址** `/pasture/player/hold/list`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerId         |      playerId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result«List«玩家占领信息»»                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    array   |   玩家占领信息    |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            



**schema属性说明**
  
**玩家占领信息**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| awardTime         |     最后领奖时间      |  date-time   |      |
            | hasTime         |     拥有加速时间      |  int32   |      |
            | holdId         |     占领id      |  int32   |      |
            | icon         |     任务ICON      |  string   |      |
            | isVip         |     任务需要VIP 0-所有玩家 1-普通会员 2-高级会员      |  int32   |      |
            | name         |     名称      |  string   |      |
            | pets         |     宠物      |  array   | 宠物或图鉴     |
            | playerHoldId         |     玩家任务记录ID      |  int64   |      |
            | playerId         |     玩家ID      |  int64   |      |
            | progress         |     进度 百分制 返回 0-100       |  int32   |      |
            | realized         |     已赚金币数      |  number   |      |
            | seq         |     排序配置值      |  int32   |      |
            | status         |     0-未占领 1-占领中       |  int32   |      |
            | unitCoin         |     unitCoin每秒金币数      |  number   |      |
            

**宠物或图鉴**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | biography         |     传记      |  string   |      |
            | debris         |     孵化碎片      |  背包道具   | 背包道具     |
            | differenceAttrs         |     属性值差值      |  array   | 属性     |
            | differenceScore         |     评分差值      |  int32   |      |
            | evolutionId         |     evolutionId等阶id      |  int64   |      |
            | evolutionItems         |     进阶材料      |  array   | 背包道具     |
            | evolutionRedStatus         |     进阶红点状态 0 无红点 1有红点       |  int32   |      |
            | evolutionType         |     evolutionType等阶类型（冗余）      |  int32   |      |
            | expValue         |     exp当前经验      |  int64   |      |
            | holdStatus         |     打工状态 1空闲 2打工中      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | ingredients         |     升级所需材料      |  array   | 背包道具     |
            | inviteStatus         |     任务邀请状态 0未邀请 1已邀请       |  int32   |      |
            | isNeedEvolution         |     是否待进阶      |  boolean   |      |
            | journeyStatus         |     旅行状态 1空闲 2旅行中      |  int32   |      |
            | level         |     level当前等级      |  int32   |      |
            | link         |     link获取链接      |  string   |      |
            | model         |     模型      |  string   |      |
            | name         |     name      |  string   |      |
            | needCoin         |     升级所需金币      |  int32   |      |
            | needGrain         |     下级所需口粮      |  int64   |      |
            | nextEvolutionIcon         |     下一阶图标      |  string   |      |
            | nextExpValue         |     下级所需经验      |  int64   |      |
            | num         |     拥有个数      |  int32   |      |
            | patrolStatus         |     巡逻状态 0-未巡逻 1-巡逻中      |  int32   |      |
            | petId         |     petId      |  int64   |      |
            | playerPetId         |     玩家宠物id      |  int64   |      |
            | redStatus         |     列表红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | soliloquy         |     独白      |  string   |      |
            | species         |      种类 1宠物 2纪念品3特产4国宝5小伙伴      |  int32   |      |
            | status         |     status状态 0未拥有 1已拥有      |  int32   |      |
            | type         |     类型 1：A（紫色）,2：S（金色）,3：SS（红色）      |  int32   |      |
            | upgradeRedStatus         |     升一级红点状态 0 无红点 1有红点       |  int32   |      |
            | useNum         |     使用消耗个数      |  int32   |      |
            

**属性**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrType         |     类型 1基础属性       |  int32   |      |
            | attrValue         |     attrValue属性值      |  number   |      |
            | type         |     类型 1体 2功 3防 4暴      |  int32   |      |
            

**背包道具**

| 属性名称         |  说明          |   类型  |  schema |
| ------------ | ------------------|--------|----------- |
| attrs         |     属性值      |  array   | 属性     |
            | bizType         |     类型 1宠物材料 2宠物蛋 3宠物道具 4人物升级材料 5互动道具 6外观碎片 7抽奖道具      |  int32   |      |
            | effect         |     effect作用      |  string   |      |
            | hatchStatus         |     状态 1可以孵化 2不可以      |  int32   |      |
            | icon         |     icon图标      |  string   |      |
            | link         |     link获取链接      |  string   |      |
            | memo         |     memo备注      |  string   |      |
            | name         |     name名称      |  string   |      |
            | num         |     拥有个数      |  int32   |      |
            | packId         |     packId      |  int64   |      |
            | playerPackId         |     playerPackId      |  int64   |      |
            | price         |     price价值      |  int32   |      |
            | redStatus         |     红点状态 0 无红点 1有红点       |  int32   |      |
            | score         |     score评分      |  int32   |      |
            | sellLevel         |     售卖等级 （小于玩家等级 可以售卖）      |  int32   |      |
            | source         |     source获取来源      |  string   |      |
            | status         |     status状态 0未用于  1已拥有 2已装备      |  int32   |      |
            | type         |     type类型 1宠物蛋 2材料      |  int32   |      |
            | useNum         |     使用消耗个数      |  int32   |      |
            




**响应示例**


```json
{
    "code": "",
    "data": [
        {
            "awardTime": "",
            "hasTime": 0,
            "holdId": 0,
            "icon": "",
            "isVip": 0,
            "name": "",
            "pets": [
                {
                    "attrs": [
                        {
                            "attrType": 0,
                            "attrValue": 0,
                            "type": 0
                        }
                    ],
                    "biography": "",
                    "debris": {
                        "attrs": [
                            {
                                "attrType": 0,
                                "attrValue": 0,
                                "type": 0
                            }
                        ],
                        "bizType": 0,
                        "effect": "",
                        "hatchStatus": 0,
                        "icon": "",
                        "link": "",
                        "memo": "",
                        "name": "",
                        "num": 0,
                        "packId": 0,
                        "playerPackId": 0,
                        "price": 0,
                        "redStatus": 0,
                        "score": 0,
                        "sellLevel": 0,
                        "source": "",
                        "status": 0,
                        "type": 0,
                        "useNum": 0
                    },
                    "differenceAttrs": [
                        {
                            "attrType": 0,
                            "attrValue": 0,
                            "type": 0
                        }
                    ],
                    "differenceScore": 0,
                    "evolutionId": 0,
                    "evolutionItems": [
                        {}
                    ],
                    "evolutionRedStatus": 0,
                    "evolutionType": 0,
                    "expValue": 0,
                    "holdStatus": 0,
                    "icon": "",
                    "ingredients": [
                        {}
                    ],
                    "inviteStatus": 0,
                    "isNeedEvolution": true,
                    "journeyStatus": 0,
                    "level": 0,
                    "link": "",
                    "model": "",
                    "name": "",
                    "needCoin": 0,
                    "needGrain": 0,
                    "nextEvolutionIcon": "",
                    "nextExpValue": 0,
                    "num": 0,
                    "patrolStatus": 0,
                    "petId": 0,
                    "playerPetId": 0,
                    "redStatus": 0,
                    "score": 0,
                    "soliloquy": "",
                    "species": 0,
                    "status": 0,
                    "type": 0,
                    "upgradeRedStatus": 0,
                    "useNum": 0
                }
            ],
            "playerHoldId": 0,
            "playerId": 0,
            "progress": 0,
            "realized": 0,
            "seq": 0,
            "status": 0,
            "unitCoin": 0
        }
    ],
    "message": "",
    "msg": ""
}
```



--------分割线
3倍收取奖励

**接口地址** `/pasture/player/hold/multipleAward`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerHoldId         |      playerHoldId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```



--------分割线
加速

**接口地址** `/pasture/player/hold/speed`


**请求方式** `GET`


**consumes** ``


**produces** `["*/*"]`


**接口描述** ``

**请求参数**

| 参数名称         | 说明     |     参数类型 |  是否必须      |  类型   |  schema  |
| ------------ | -------------------------------- |-----------|--------|----|--- |
| playerHoldId         |      playerHoldId   |     query        |       true      | integer   |      |
            





**响应状态**

| 状态码         | 说明                             |    schema                         |
| ------------ | -------------------------------- |---------------------- |
| 200         | OK                        |Result                          |
| 204         | No Content                        |                          |
| 401         | Unauthorized                        |                          |
| 403         | Forbidden                        |                          |




**响应参数**

| 参数名称         | 说明                             |    类型 |  schema |
| ------------ | -------------------|-------|----------- |
| code     |      |    string   |       |
            | data     |      |    object   |       |
            | message     |      |    string   |       |
            | msg     |      |    string   |       |
            




**响应示例**


```json
{
    "code": "",
    "data": {},
    "message": "",
    "msg": ""
}
```


