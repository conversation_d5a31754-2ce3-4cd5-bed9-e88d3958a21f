import type { RouteRecordRaw } from 'vue-router'

// 完整的路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue')
  },
  {
    path: '/',
    component: () => import('../views/Layout.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('../views/Dashboard.vue'),
        meta: { title: '实时统计' }
      },
      {
        path: 'statistics',
        name: 'Statistics',
        component: () => import('../views/StatisticsBoard.vue'),
        meta: { title: '数据看板' }
      },
      {
        path: 'users',
        name: 'UserManagement',
        component: () => import('../views/UserManagement.vue'),
        meta: { title: '用户管理' }
      },
      {
        path: 'orders',
        name: 'OrderManagement',
        component: () => import('../views/OrderManagement.vue'),
        meta: { title: '订单管理' }
      },
      {
        path: 'refunds',
        name: 'RefundManagement',
        component: () => import('../views/RefundManagement.vue'),
        meta: { title: '退款管理' }
      }
    ]
  }
]

console.log('📋 路由配置已加载:', routes)

export default routes
